# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Logs
logs
*.log
dev-debug.log

# TypeScript cache
*.tsbuildinfo

# Optional caches
.npm
.eslintcache
.stylelintcache

# Build outputs
build/
dist/
.next
out

# Environment variables
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Database
*.db
*.sqlite
*.sqlite3

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Package lock files (we use npm)
yarn.lock
pnpm-lock.yaml

# Test files
test-results/
playwright-report/

# Upload directories
uploads/
public/uploads/

# Cache directories
.cache/
.tmp/

# Project specific
.taskmaster/
