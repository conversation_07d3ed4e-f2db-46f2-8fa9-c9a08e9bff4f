const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

/**
 * Domain Management Tables Migration
 * Creates tables for custom domains, SSL certificates, and institute branding
 */

async function migrateDomainTables() {
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'lte_lms',
    password: process.env.DB_PASSWORD || '1234',
    port: process.env.DB_PORT || 5432,
  });

  try {
    console.log('🚀 Starting domain management tables migration...');

    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connected successfully');
    console.log(`📊 Connected to database: ${client.database}`);
    console.log(`🏠 Host: ${client.host}:${client.port}`);
    client.release();

    // Domain management tables SQL
    const domainTablesSQL = `
-- =============================================
-- CUSTOM DOMAIN MANAGEMENT TABLES
-- =============================================

-- Institute custom domains table
CREATE TABLE IF NOT EXISTS institute_domains (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL UNIQUE,
    subdomain VARCHAR(100), -- For subdomain.exampllms.com
    domain_type VARCHAR(20) DEFAULT 'custom', -- 'custom', 'subdomain'
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_method VARCHAR(50) DEFAULT 'dns_txt',
    verification_record VARCHAR(500), -- The actual DNS record to add
    verification_attempts INTEGER DEFAULT 0,
    last_verification_check TIMESTAMP,
    ssl_certificate_id VARCHAR(255),
    ssl_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'issued', 'expired', 'failed'
    ssl_expires_at TIMESTAMP,
    ssl_auto_renew BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT FALSE,
    is_primary BOOLEAN DEFAULT FALSE, -- Primary domain for the institute
    redirect_to_primary BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,
    activated_at TIMESTAMP
);

-- Domain verification logs table
CREATE TABLE IF NOT EXISTS domain_verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain_id UUID NOT NULL REFERENCES institute_domains(id) ON DELETE CASCADE,
    verification_type VARCHAR(50) NOT NULL, -- 'dns_txt', 'file_upload', 'meta_tag'
    verification_status VARCHAR(50) NOT NULL, -- 'pending', 'success', 'failed'
    verification_details JSONB, -- Store verification response details
    error_message TEXT,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SSL certificate management table
CREATE TABLE IF NOT EXISTS ssl_certificates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain_id UUID NOT NULL REFERENCES institute_domains(id) ON DELETE CASCADE,
    certificate_authority VARCHAR(100) DEFAULT 'letsencrypt',
    certificate_data TEXT, -- PEM encoded certificate
    private_key_data TEXT, -- PEM encoded private key (encrypted)
    certificate_chain TEXT, -- PEM encoded certificate chain
    issued_at TIMESTAMP,
    expires_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT TRUE,
    renewal_attempts INTEGER DEFAULT 0,
    last_renewal_attempt TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'issued', 'expired', 'revoked'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Institute branding and customization table
CREATE TABLE IF NOT EXISTS institute_branding (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    logo_url VARCHAR(500),
    favicon_url VARCHAR(500),
    primary_color VARCHAR(7) DEFAULT '#007bff', -- Hex color code
    secondary_color VARCHAR(7) DEFAULT '#6c757d',
    accent_color VARCHAR(7) DEFAULT '#28a745',
    font_family VARCHAR(100) DEFAULT 'Inter',
    custom_css TEXT,
    landing_page_template VARCHAR(50) DEFAULT 'default',
    landing_page_content JSONB, -- Custom landing page content
    social_links JSONB, -- Social media links
    contact_info JSONB, -- Contact information
    seo_settings JSONB, -- SEO meta tags and settings
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Custom domain indexes
CREATE INDEX IF NOT EXISTS idx_institute_domains_institute_id ON institute_domains(institute_id);
CREATE INDEX IF NOT EXISTS idx_institute_domains_domain ON institute_domains(domain);
CREATE INDEX IF NOT EXISTS idx_institute_domains_is_active ON institute_domains(is_active);
CREATE INDEX IF NOT EXISTS idx_institute_domains_is_verified ON institute_domains(is_verified);
CREATE INDEX IF NOT EXISTS idx_institute_domains_ssl_status ON institute_domains(ssl_status);

-- Domain verification logs indexes
CREATE INDEX IF NOT EXISTS idx_domain_verification_logs_domain_id ON domain_verification_logs(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_verification_logs_status ON domain_verification_logs(verification_status);
CREATE INDEX IF NOT EXISTS idx_domain_verification_logs_checked_at ON domain_verification_logs(checked_at);

-- SSL certificates indexes
CREATE INDEX IF NOT EXISTS idx_ssl_certificates_domain_id ON ssl_certificates(domain_id);
CREATE INDEX IF NOT EXISTS idx_ssl_certificates_expires_at ON ssl_certificates(expires_at);
CREATE INDEX IF NOT EXISTS idx_ssl_certificates_status ON ssl_certificates(status);

-- Institute branding indexes
CREATE INDEX IF NOT EXISTS idx_institute_branding_institute_id ON institute_branding(institute_id);
CREATE INDEX IF NOT EXISTS idx_institute_branding_is_active ON institute_branding(is_active);
`;

    console.log('📄 Executing domain management tables SQL...');

    // Execute the SQL
    await pool.query(domainTablesSQL);

    console.log('✅ Domain management tables migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - institute_domains');
    console.log('   - domain_verification_logs');
    console.log('   - ssl_certificates');
    console.log('   - institute_branding');
    console.log('🔍 Created indexes for performance optimization');

  } catch (error) {
    console.error('❌ Migration error:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('⚠️  Tables already exist, skipping creation');
    } else {
      throw error;
    }
  } finally {
    console.log('🔒 Database pool closed');
    await pool.end();
  }
}

// Run migration if script is executed directly
if (require.main === module) {
  migrateDomainTables()
    .then(() => {
      console.log('🏁 Domain management migration completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { migrateDomainTables };
