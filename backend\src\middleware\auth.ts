import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { UserRole, Permission, JWTPayload, User, ROLE_PERMISSIONS, PermissionContext, AccessControlResult } from '../types/auth'

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: User
    }
  }
}

/**
 * Middleware to authenticate JWT tokens and attach user to request
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Access token required',
        code: 'UNAUTHORIZED'
      })
      return
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix
    
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET not configured')
      res.status(500).json({
        success: false,
        error: 'Server configuration error',
        code: 'SERVER_ERROR'
      })
      return
    }

    // Verify and decode JWT
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload
    
    // TODO: Fetch user from database to ensure user still exists and is active
    // For now, we'll construct user from JWT payload
    const user: User = {
      id: decoded.userId,
      email: decoded.email,
      firstName: '', // TODO: Add to JWT or fetch from DB
      lastName: '', // TODO: Add to JWT or fetch from DB
      role: decoded.role,
      instituteId: decoded.instituteId,
      isActive: true, // TODO: Check from DB
      emailVerified: true, // TODO: Check from DB
      createdAt: new Date(), // TODO: Fetch from DB
      updatedAt: new Date(), // TODO: Fetch from DB
    }

    req.user = user
    next()
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        success: false,
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      })
      return
    }
    
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        success: false,
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      })
      return
    }

    console.error('Authentication error:', error)
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    })
  }
}

/**
 * Middleware to check if user has required role(s)
 */
export const requireRole = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      })
      return
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        code: 'FORBIDDEN',
        requiredRoles: roles,
        userRole: req.user.role
      })
      return
    }

    next()
  }
}

/**
 * Middleware to check if user has required permission(s)
 */
export const requirePermission = (...permissions: Permission[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      })
      return
    }

    const userPermissions = ROLE_PERMISSIONS[req.user.role]
    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission)
    )

    if (!hasPermission) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        code: 'FORBIDDEN',
        requiredPermissions: permissions,
        userRole: req.user.role
      })
      return
    }

    next()
  }
}

/**
 * Check if user has permission for a specific action
 */
export const hasPermission = (user: User, permission: Permission): boolean => {
  const userPermissions = ROLE_PERMISSIONS[user.role]
  return userPermissions.includes(permission)
}

/**
 * Check if user has any of the specified roles
 */
export const hasRole = (user: User, ...roles: UserRole[]): boolean => {
  return roles.includes(user.role)
}

/**
 * Advanced permission checking with context
 */
export const checkPermission = (context: PermissionContext): AccessControlResult => {
  const { user, resource, action } = context

  // Check if user has the required permission
  if (!hasPermission(user, action)) {
    return {
      allowed: false,
      reason: 'User does not have required permission',
      requiredPermission: action
    }
  }

  // Additional context-based checks
  if (resource) {
    // Institute-scoped permissions
    if (user.role === UserRole.INSTITUTE_ADMIN || user.role === UserRole.TEACHER || user.role === UserRole.STUDENT) {
      if (resource.instituteId && user.instituteId !== resource.instituteId) {
        return {
          allowed: false,
          reason: 'User does not belong to the same institute as the resource'
        }
      }
    }

    // Resource ownership checks
    if (resource.ownerId && user.role === UserRole.TEACHER) {
      // Teachers can only edit their own courses
      if (action === Permission.EDIT_OWN_COURSES && resource.ownerId !== user.id) {
        return {
          allowed: false,
          reason: 'Teachers can only edit their own courses'
        }
      }
    }
  }

  return { allowed: true }
}

/**
 * Middleware for advanced permission checking with context
 */
export const requirePermissionWithContext = (permission: Permission) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      })
      return
    }

    // Extract resource context from request
    const resourceContext = {
      type: req.params.resourceType || 'unknown',
      id: req.params.id || req.params.resourceId,
      // TODO: Fetch ownerId and instituteId from database based on resource
    }

    const context: PermissionContext = {
      user: req.user,
      resource: resourceContext,
      action: permission
    }

    const result = checkPermission(context)

    if (!result.allowed) {
      res.status(403).json({
        success: false,
        error: result.reason || 'Permission denied',
        code: 'FORBIDDEN',
        requiredPermission: result.requiredPermission,
        requiredRole: result.requiredRole
      })
      return
    }

    next()
  }
}

/**
 * Middleware to ensure user belongs to the same institute (for institute-scoped operations)
 */
export const requireSameInstitute = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
      code: 'UNAUTHORIZED'
    })
    return
  }

  // Super admins can access all institutes
  if (req.user.role === UserRole.SUPER_ADMIN) {
    next()
    return
  }

  const targetInstituteId = req.params.instituteId || req.body.instituteId

  if (targetInstituteId && req.user.instituteId !== targetInstituteId) {
    res.status(403).json({
      success: false,
      error: 'Access denied: Different institute',
      code: 'FORBIDDEN'
    })
    return
  }

  next()
}

/**
 * Optional authentication - doesn't fail if no token provided
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      next()
      return
    }

    const token = authHeader.substring(7)
    
    if (!process.env.JWT_SECRET) {
      next()
      return
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload
    
    const user: User = {
      id: decoded.userId,
      email: decoded.email,
      firstName: '',
      lastName: '',
      role: decoded.role,
      instituteId: decoded.instituteId,
      isActive: true,
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    req.user = user
  } catch (error) {
    // Ignore authentication errors for optional auth
    console.log('Optional auth failed:', error instanceof Error ? error.message : 'Unknown error')
  }
  
  next()
}
