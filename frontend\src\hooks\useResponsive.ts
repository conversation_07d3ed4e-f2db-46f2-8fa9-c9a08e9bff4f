import { useEffect, useState } from 'react'

/**
 * Breakpoint definitions matching Tailwind CSS defaults
 */
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof breakpoints

/**
 * Hook for detecting current screen size and breakpoints
 */
export function useBreakpoint() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('sm')
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  })

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight

      setWindowSize({ width, height })

      // Determine current breakpoint
      if (width >= breakpoints['2xl']) {
        setCurrentBreakpoint('2xl')
      } else if (width >= breakpoints.xl) {
        setCurrentBreakpoint('xl')
      } else if (width >= breakpoints.lg) {
        setCurrentBreakpoint('lg')
      } else if (width >= breakpoints.md) {
        setCurrentBreakpoint('md')
      } else {
        setCurrentBreakpoint('sm')
      }
    }

    // Set initial values
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isAbove = (breakpoint: Breakpoint) => {
    return windowSize.width >= breakpoints[breakpoint]
  }

  const isBelow = (breakpoint: Breakpoint) => {
    return windowSize.width < breakpoints[breakpoint]
  }

  const isBetween = (min: Breakpoint, max: Breakpoint) => {
    return windowSize.width >= breakpoints[min] && windowSize.width < breakpoints[max]
  }

  return {
    currentBreakpoint,
    windowSize,
    isAbove,
    isBelow,
    isBetween,
    isMobile: isBelow('md'),
    isTablet: isBetween('md', 'lg'),
    isDesktop: isAbove('lg'),
  }
}

/**
 * Hook for responsive values based on breakpoints
 */
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>) {
  const { currentBreakpoint } = useBreakpoint()

  // Find the appropriate value for current breakpoint
  const breakpointOrder: Breakpoint[] = ['sm', 'md', 'lg', 'xl', '2xl']
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint)

  // Look for value at current breakpoint or fallback to smaller breakpoints
  for (let i = currentIndex; i >= 0; i--) {
    const breakpoint = breakpointOrder[i]
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]
    }
  }

  // Fallback to the first available value
  for (const breakpoint of breakpointOrder) {
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]
    }
  }

  return undefined
}

/**
 * Hook for detecting device orientation
 */
export function useOrientation() {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait')

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape')
    }

    handleOrientationChange()
    window.addEventListener('resize', handleOrientationChange)
    
    return () => window.removeEventListener('resize', handleOrientationChange)
  }, [])

  return orientation
}

/**
 * Hook for detecting touch device
 */
export function useTouchDevice() {
  const [isTouchDevice, setIsTouchDevice] = useState(false)

  useEffect(() => {
    const checkTouchDevice = () => {
      setIsTouchDevice(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore
        navigator.msMaxTouchPoints > 0
      )
    }

    checkTouchDevice()
  }, [])

  return isTouchDevice
}

/**
 * Hook for managing responsive grid columns
 */
export function useResponsiveGrid(
  columns: Partial<Record<Breakpoint, number>> = {
    sm: 1,
    md: 2,
    lg: 3,
    xl: 4,
  }
) {
  const currentColumns = useResponsiveValue(columns) || 1

  const getGridClasses = () => {
    const classes: string[] = []

    Object.entries(columns).forEach(([breakpoint, cols]) => {
      if (breakpoint === 'sm') {
        classes.push(`grid-cols-${cols}`)
      } else {
        classes.push(`${breakpoint}:grid-cols-${cols}`)
      }
    })

    return classes.join(' ')
  }

  return {
    currentColumns,
    gridClasses: getGridClasses(),
  }
}

/**
 * Hook for responsive spacing
 */
export function useResponsiveSpacing(
  spacing: Partial<Record<Breakpoint, string>> = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  }
) {
  const currentSpacing = useResponsiveValue(spacing) || 'p-4'

  const getSpacingClasses = () => {
    const classes: string[] = []

    Object.entries(spacing).forEach(([breakpoint, space]) => {
      if (breakpoint === 'sm') {
        classes.push(space)
      } else {
        classes.push(`${breakpoint}:${space}`)
      }
    })

    return classes.join(' ')
  }

  return {
    currentSpacing,
    spacingClasses: getSpacingClasses(),
  }
}

/**
 * Hook for responsive text sizes
 */
export function useResponsiveText(
  sizes: Partial<Record<Breakpoint, string>> = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  }
) {
  const currentSize = useResponsiveValue(sizes) || 'text-base'

  const getTextClasses = () => {
    const classes: string[] = []

    Object.entries(sizes).forEach(([breakpoint, size]) => {
      if (breakpoint === 'sm') {
        classes.push(size)
      } else {
        classes.push(`${breakpoint}:${size}`)
      }
    })

    return classes.join(' ')
  }

  return {
    currentSize,
    textClasses: getTextClasses(),
  }
}

/**
 * Hook for container queries (experimental)
 */
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        setContainerSize({ width, height })
      }
    })

    resizeObserver.observe(containerRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [containerRef])

  const isContainerAbove = (width: number) => containerSize.width >= width
  const isContainerBelow = (width: number) => containerSize.width < width

  return {
    containerSize,
    isContainerAbove,
    isContainerBelow,
  }
}

/**
 * Hook for managing responsive navigation
 */
export function useResponsiveNavigation() {
  const { isMobile } = useBreakpoint()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Close menu when switching to desktop
  useEffect(() => {
    if (!isMobile) {
      setIsMenuOpen(false)
    }
  }, [isMobile])

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const closeMenu = () => setIsMenuOpen(false)
  const openMenu = () => setIsMenuOpen(true)

  return {
    isMobile,
    isMenuOpen,
    toggleMenu,
    closeMenu,
    openMenu,
  }
}

/**
 * Hook for responsive image loading
 */
export function useResponsiveImage(
  sources: Partial<Record<Breakpoint, string>>,
  alt: string
) {
  const currentSource = useResponsiveValue(sources)

  const getSrcSet = () => {
    return Object.entries(sources)
      .map(([breakpoint, src]) => `${src} ${breakpoints[breakpoint as Breakpoint]}w`)
      .join(', ')
  }

  const getSizes = () => {
    return Object.entries(sources)
      .map(([breakpoint], index, array) => {
        if (index === array.length - 1) {
          return `${breakpoints[breakpoint as Breakpoint]}px`
        }
        return `(max-width: ${breakpoints[breakpoint as Breakpoint]}px) ${breakpoints[breakpoint as Breakpoint]}px`
      })
      .join(', ')
  }

  return {
    currentSource,
    srcSet: getSrcSet(),
    sizes: getSizes(),
    alt,
  }
}
