const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

/**
 * Database Performance Analysis Tool
 * Analyzes query performance, index usage, and provides optimization recommendations
 */

class DatabasePerformanceAnalyzer {
  constructor() {
    this.pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'lte_lms',
      password: process.env.DB_PASSWORD || '1234',
      port: process.env.DB_PORT || 5432,
    });
  }

  /**
   * Analyze table sizes and row counts
   */
  async analyzeTableSizes() {
    const query = `
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation,
        most_common_vals,
        most_common_freqs
      FROM pg_stats 
      WHERE schemaname = 'public'
      ORDER BY tablename, attname;
    `;

    const sizeQuery = `
      SELECT 
        table_name,
        pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as size,
        pg_total_relation_size(quote_ident(table_name)) as size_bytes
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY pg_total_relation_size(quote_ident(table_name)) DESC;
    `;

    const rowCountQuery = `
      SELECT
        schemaname,
        relname as tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_rows,
        n_dead_tup as dead_rows,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables
      ORDER BY n_live_tup DESC;
    `;

    try {
      const [statsResult, sizeResult, rowCountResult] = await Promise.all([
        this.pool.query(query),
        this.pool.query(sizeQuery),
        this.pool.query(rowCountQuery)
      ]);

      return {
        columnStats: statsResult.rows,
        tableSizes: sizeResult.rows,
        tableActivity: rowCountResult.rows
      };
    } catch (error) {
      console.error('Error analyzing table sizes:', error.message);
      throw error;
    }
  }

  /**
   * Analyze index usage and effectiveness
   */
  async analyzeIndexUsage() {
    const indexUsageQuery = `
      SELECT
        schemaname,
        relname as tablename,
        indexrelname as indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan,
        CASE
          WHEN idx_scan = 0 THEN 'UNUSED'
          WHEN idx_scan < 10 THEN 'LOW_USAGE'
          WHEN idx_scan < 100 THEN 'MODERATE_USAGE'
          ELSE 'HIGH_USAGE'
        END as usage_level
      FROM pg_stat_user_indexes
      ORDER BY idx_scan DESC;
    `;

    const indexSizeQuery = `
      SELECT
        schemaname,
        relname as tablename,
        indexrelname as indexname,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
        pg_relation_size(indexrelid) as index_size_bytes
      FROM pg_stat_user_indexes
      ORDER BY pg_relation_size(indexrelid) DESC;
    `;

    const duplicateIndexQuery = `
      SELECT
        'No duplicates found' as message
      LIMIT 0;
    `;

    try {
      const [usageResult, sizeResult, duplicateResult] = await Promise.all([
        this.pool.query(indexUsageQuery),
        this.pool.query(indexSizeQuery),
        this.pool.query(duplicateIndexQuery)
      ]);

      return {
        indexUsage: usageResult.rows,
        indexSizes: sizeResult.rows,
        duplicateIndexes: duplicateResult.rows
      };
    } catch (error) {
      console.error('Error analyzing index usage:', error.message);
      throw error;
    }
  }

  /**
   * Analyze slow queries and performance bottlenecks
   */
  async analyzeSlowQueries() {
    // Enable pg_stat_statements if available
    const enableStatsQuery = `
      SELECT EXISTS (
        SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
      ) as stats_enabled;
    `;

    const slowQueriesQuery = `
      SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
      FROM pg_stat_statements 
      WHERE query NOT LIKE '%pg_stat_statements%'
      ORDER BY total_time DESC 
      LIMIT 20;
    `;

    const connectionStatsQuery = `
      SELECT 
        state,
        COUNT(*) as connection_count,
        AVG(EXTRACT(EPOCH FROM (now() - state_change))) as avg_duration_seconds
      FROM pg_stat_activity 
      WHERE pid != pg_backend_pid()
      GROUP BY state
      ORDER BY connection_count DESC;
    `;

    try {
      const statsEnabledResult = await this.pool.query(enableStatsQuery);
      const statsEnabled = statsEnabledResult.rows[0].stats_enabled;

      let slowQueries = [];
      if (statsEnabled) {
        const slowQueriesResult = await this.pool.query(slowQueriesQuery);
        slowQueries = slowQueriesResult.rows;
      }

      const connectionStatsResult = await this.pool.query(connectionStatsQuery);

      return {
        statsEnabled,
        slowQueries,
        connectionStats: connectionStatsResult.rows
      };
    } catch (error) {
      console.error('Error analyzing slow queries:', error.message);
      throw error;
    }
  }

  /**
   * Test specific multi-tenant queries for performance
   */
  async testMultiTenantQueries() {
    const testQueries = [
      {
        name: 'Institute User Lookup',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT u.*, ur.permissions 
          FROM users u 
          LEFT JOIN user_roles ur ON u.id = ur.user_id 
          WHERE u.institute_id = $1 AND u.email = $2
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3', '<EMAIL>']
      },
      {
        name: 'Institute Students List',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT u.*, sp.student_id, sp.major, sp.gpa 
          FROM users u 
          LEFT JOIN student_profiles sp ON u.id = sp.user_id 
          WHERE u.institute_id = $1 AND u.role = 'student' 
          ORDER BY u.created_at DESC 
          LIMIT 20
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Active Sessions by Institute',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT us.*, u.email, u.first_name, u.last_name 
          FROM user_sessions us 
          JOIN users u ON us.user_id = u.id 
          WHERE us.institute_id = $1 AND us.is_active = true 
          AND us.expires_at > CURRENT_TIMESTAMP
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Security Audit Log Query',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT sal.* 
          FROM security_audit_log sal 
          JOIN users u ON sal.user_id = u.id 
          WHERE u.institute_id = $1 
          AND sal.created_at >= CURRENT_DATE - INTERVAL '7 days' 
          ORDER BY sal.created_at DESC 
          LIMIT 50
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Institute Domain Lookup',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT i.*, id.domain, id.is_verified, id.ssl_status 
          FROM institutes i 
          LEFT JOIN institute_domains id ON i.id = id.institute_id 
          WHERE id.domain = $1 AND id.is_active = true
        `,
        params: ['example.edu']
      }
    ];

    const results = [];

    for (const testQuery of testQueries) {
      try {
        const result = await this.pool.query(testQuery.query, testQuery.params);
        const plan = result.rows[0]['QUERY PLAN'][0];
        
        results.push({
          name: testQuery.name,
          executionTime: plan['Execution Time'],
          planningTime: plan['Planning Time'],
          totalCost: plan.Plan['Total Cost'],
          actualRows: plan.Plan['Actual Rows'],
          plan: plan
        });
      } catch (error) {
        results.push({
          name: testQuery.name,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(analysisResults) {
    const recommendations = [];

    // Analyze table sizes
    const largeTables = analysisResults.tableSizes
      .filter(table => table.size_bytes > 100 * 1024 * 1024) // > 100MB
      .slice(0, 5);

    if (largeTables.length > 0) {
      recommendations.push({
        category: 'Table Size',
        priority: 'medium',
        issue: `Large tables detected: ${largeTables.map(t => `${t.table_name} (${t.size})`).join(', ')}`,
        recommendation: 'Consider partitioning large tables by institute_id or date for better performance'
      });
    }

    // Analyze unused indexes
    const unusedIndexes = analysisResults.indexUsage
      .filter(idx => idx.usage_level === 'UNUSED' && !idx.indexname.includes('pkey'))
      .slice(0, 10);

    if (unusedIndexes.length > 0) {
      recommendations.push({
        category: 'Index Optimization',
        priority: 'low',
        issue: `${unusedIndexes.length} unused indexes found`,
        recommendation: `Consider dropping unused indexes: ${unusedIndexes.map(i => i.indexname).join(', ')}`
      });
    }

    // Analyze slow queries
    if (analysisResults.slowQueries && analysisResults.slowQueries.length > 0) {
      const slowestQuery = analysisResults.slowQueries[0];
      recommendations.push({
        category: 'Query Performance',
        priority: 'high',
        issue: `Slowest query takes ${slowestQuery.mean_time.toFixed(2)}ms on average`,
        recommendation: 'Review and optimize the slowest queries, consider adding indexes or rewriting queries'
      });
    }

    // Analyze multi-tenant query performance
    const slowMultiTenantQueries = analysisResults.multiTenantQueries
      .filter(q => q.executionTime > 10) // > 10ms
      .sort((a, b) => b.executionTime - a.executionTime);

    if (slowMultiTenantQueries.length > 0) {
      recommendations.push({
        category: 'Multi-Tenant Performance',
        priority: 'high',
        issue: `${slowMultiTenantQueries.length} multi-tenant queries are slow`,
        recommendation: `Optimize queries: ${slowMultiTenantQueries.map(q => q.name).join(', ')}`
      });
    }

    return recommendations;
  }

  /**
   * Run complete performance analysis
   */
  async runCompleteAnalysis() {
    console.log('🔍 Starting comprehensive database performance analysis...\n');

    try {
      console.log('📊 Analyzing table sizes and activity...');
      const tableSizes = await this.analyzeTableSizes();

      console.log('📈 Analyzing index usage and effectiveness...');
      const indexUsage = await this.analyzeIndexUsage();

      console.log('🐌 Analyzing slow queries...');
      const slowQueries = await this.analyzeSlowQueries();

      console.log('🏢 Testing multi-tenant query performance...');
      const multiTenantQueries = await this.testMultiTenantQueries();

      const analysisResults = {
        ...tableSizes,
        ...indexUsage,
        ...slowQueries,
        multiTenantQueries
      };

      console.log('💡 Generating recommendations...');
      const recommendations = this.generateRecommendations(analysisResults);

      const report = {
        timestamp: new Date().toISOString(),
        analysisResults,
        recommendations,
        summary: {
          totalTables: tableSizes.tableSizes.length,
          totalIndexes: indexUsage.indexUsage.length,
          unusedIndexes: indexUsage.indexUsage.filter(i => i.usage_level === 'UNUSED').length,
          slowQueries: slowQueries.slowQueries.length,
          recommendations: recommendations.length
        }
      };

      // Save report to file
      const reportPath = path.join(__dirname, '../reports/performance-analysis.json');
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

      console.log('\n📋 Performance Analysis Summary:');
      console.log(`📊 Total Tables: ${report.summary.totalTables}`);
      console.log(`📈 Total Indexes: ${report.summary.totalIndexes}`);
      console.log(`🚫 Unused Indexes: ${report.summary.unusedIndexes}`);
      console.log(`🐌 Slow Queries: ${report.summary.slowQueries}`);
      console.log(`💡 Recommendations: ${report.summary.recommendations}`);
      console.log(`📄 Report saved to: ${reportPath}`);

      if (recommendations.length > 0) {
        console.log('\n🔧 Top Recommendations:');
        recommendations.slice(0, 3).forEach((rec, index) => {
          console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.category}: ${rec.recommendation}`);
        });
      }

      return report;

    } catch (error) {
      console.error('❌ Performance analysis failed:', error.message);
      throw error;
    } finally {
      await this.pool.end();
    }
  }
}

// Run analysis if script is executed directly
if (require.main === module) {
  const analyzer = new DatabasePerformanceAnalyzer();
  analyzer.runCompleteAnalysis()
    .then(() => {
      console.log('\n✅ Performance analysis completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Performance analysis failed:', error.message);
      process.exit(1);
    });
}

module.exports = { DatabasePerformanceAnalyzer };
