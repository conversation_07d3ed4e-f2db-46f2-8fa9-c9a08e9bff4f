const { tokenUtils, sessionUtils, responseUtils } = require('../utils/auth');
const { query } = require('../database/connection');

/**
 * Authentication Middleware
 * Handles JWT verification, role-based access control, and session management
 */

/**
 * Verify JWT token and attach user to request
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Extract token from request
    const token = sessionUtils.extractTokenFromRequest(req);
    
    if (!token) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'Access token is required',
          'TOKEN_MISSING'
        )
      );
    }

    // Verify token
    const tokenResult = tokenUtils.verifyToken(token);
    
    if (!tokenResult.isValid) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'Invalid or expired token',
          'TOKEN_INVALID',
          tokenResult.error
        )
      );
    }

    // Get user from database
    const userResult = await query(
      'SELECT id, email, first_name, last_name, role, institute_id, is_active, is_email_verified FROM users WHERE id = $1',
      [tokenResult.payload.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'User not found',
          'USER_NOT_FOUND'
        )
      );
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'User account is deactivated',
          'USER_DEACTIVATED'
        )
      );
    }

    // Attach user to request
    req.user = user;
    req.tokenPayload = tokenResult.payload;

    // Update last accessed time for session tracking
    if (req.user.id) {
      // Fire and forget - don't wait for this update
      query(
        'UPDATE user_sessions SET last_accessed_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND session_token = $2',
        [req.user.id, token]
      ).catch(err => console.warn('Failed to update session access time:', err.message));
    }

    next();
  } catch (error) {
    console.error('Authentication middleware error:', error.message);
    return res.status(500).json(
      responseUtils.createErrorResponse(
        'Authentication failed',
        'AUTH_ERROR',
        error.message
      )
    );
  }
};

/**
 * Require specific role(s)
 */
const requireRole = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'Authentication required',
          'AUTH_REQUIRED'
        )
      );
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(
        responseUtils.createErrorResponse(
          `Access denied. Required role: ${allowedRoles.join(' or ')}`,
          'INSUFFICIENT_PERMISSIONS',
          { userRole: req.user.role, requiredRoles: allowedRoles }
        )
      );
    }

    next();
  };
};

/**
 * Require email verification
 */
const requireEmailVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(
      responseUtils.createErrorResponse(
        'Authentication required',
        'AUTH_REQUIRED'
      )
    );
  }

  if (!req.user.is_email_verified) {
    return res.status(403).json(
      responseUtils.createErrorResponse(
        'Email verification required',
        'EMAIL_NOT_VERIFIED'
      )
    );
  }

  next();
};

/**
 * Require super admin role
 */
const requireSuperAdmin = [
  authenticateToken,
  requireRole('super_admin')
];

/**
 * Require institute admin role
 */
const requireInstituteAdmin = [
  authenticateToken,
  requireRole('institute_admin', 'super_admin')
];

/**
 * Require teacher role
 */
const requireTeacher = [
  authenticateToken,
  requireRole('teacher', 'institute_admin', 'super_admin')
];

/**
 * Require student role
 */
const requireStudent = [
  authenticateToken,
  requireRole('student', 'teacher', 'institute_admin', 'super_admin')
];

/**
 * Optional authentication (attach user if token is present)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = sessionUtils.extractTokenFromRequest(req);
    
    if (!token) {
      return next(); // No token, continue without user
    }

    const tokenResult = tokenUtils.verifyToken(token);
    
    if (!tokenResult.isValid) {
      return next(); // Invalid token, continue without user
    }

    // Get user from database
    const userResult = await query(
      'SELECT id, email, first_name, last_name, role, institute_id, is_active, is_email_verified FROM users WHERE id = $1',
      [tokenResult.payload.userId]
    );

    if (userResult.rows.length > 0 && userResult.rows[0].is_active) {
      req.user = userResult.rows[0];
      req.tokenPayload = tokenResult.payload;
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error.message);
    next(); // Continue without user on error
  }
};

/**
 * Validate user belongs to same institute (for tenant isolation)
 */
const validateInstituteAccess = async (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(
      responseUtils.createErrorResponse(
        'Authentication required',
        'AUTH_REQUIRED'
      )
    );
  }

  // Super admins have access to all institutes
  if (req.user.role === 'super_admin') {
    return next();
  }

  // Check if user's institute matches the tenant context
  if (req.tenant && req.tenant.instituteId && req.user.institute_id !== req.tenant.instituteId) {
    return res.status(403).json(
      responseUtils.createErrorResponse(
        'Access denied. User does not belong to this institute.',
        'INSTITUTE_ACCESS_DENIED',
        {
          userInstitute: req.user.institute_id,
          requestedInstitute: req.tenant.instituteId
        }
      )
    );
  }

  next();
};

/**
 * Rate limiting for authentication endpoints
 */
const createAuthRateLimit = (windowMs = 15 * 60 * 1000, max = 5) => {
  const rateLimit = require('express-rate-limit');
  
  return rateLimit({
    windowMs,
    max,
    message: responseUtils.createErrorResponse(
      'Too many authentication attempts, please try again later',
      'RATE_LIMIT_EXCEEDED'
    ),
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use IP + email for more specific rate limiting
      const email = req.body?.email || 'unknown';
      return `auth:${req.ip}:${email}`;
    }
  });
};

/**
 * Middleware to log authentication events
 */
const logAuthEvent = (eventType) => {
  return (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log authentication event
      const logData = {
        event: eventType,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        email: req.body?.email || req.user?.email,
        success: res.statusCode < 400,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString()
      };
      
      console.log('🔐 Auth Event:', JSON.stringify(logData));
      
      // Call original send
      originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * Session cleanup middleware (remove expired sessions)
 */
const cleanupExpiredSessions = async (req, res, next) => {
  try {
    // Clean up expired sessions (fire and forget)
    query(
      'DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP'
    ).catch(err => console.warn('Failed to cleanup expired sessions:', err.message));
    
    next();
  } catch (error) {
    console.warn('Session cleanup error:', error.message);
    next();
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireEmailVerification,
  requireSuperAdmin,
  requireInstituteAdmin,
  requireTeacher,
  requireStudent,
  optionalAuth,
  validateInstituteAccess,
  createAuthRateLimit,
  logAuthEvent,
  cleanupExpiredSessions
};
