import React from 'react'
import { ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from './button'
import { useBreakpoint } from '@/hooks/useResponsive'

export interface TableColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  render?: (value: any, row: T, index: number) => React.ReactNode
  className?: string
  headerClassName?: string
  width?: string
  align?: 'left' | 'center' | 'right'
  priority?: 'high' | 'medium' | 'low' // For responsive hiding
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  emptyMessage?: string
  caption?: string
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
  onSort?: (column: string, direction: 'asc' | 'desc') => void
  className?: string
  rowClassName?: (row: T, index: number) => string
  onRowClick?: (row: T, index: number) => void
  selectable?: boolean
  selectedRows?: Set<string | number>
  onSelectionChange?: (selectedRows: Set<string | number>) => void
  getRowId?: (row: T, index: number) => string | number
}

export function DataTable<T = any>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  caption,
  sortBy,
  sortDirection,
  onSort,
  className,
  rowClassName,
  onRowClick,
  selectable = false,
  selectedRows = new Set(),
  onSelectionChange,
  getRowId = (_, index: number) => index,
}: TableProps<T>) {
  const { isMobile, isTablet } = useBreakpoint()

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return

    const columnKey = String(column.key)
    const newDirection = 
      sortBy === columnKey && sortDirection === 'asc' ? 'desc' : 'asc'
    
    onSort(columnKey, newDirection)
  }

  const handleSelectAll = () => {
    if (!onSelectionChange) return

    const allSelected = data.every((row, index) => 
      selectedRows.has(getRowId(row, index))
    )

    if (allSelected) {
      onSelectionChange(new Set())
    } else {
      const newSelection = new Set(
        data.map((row, index) => getRowId(row, index))
      )
      onSelectionChange(newSelection)
    }
  }

  const handleRowSelect = (row: T, index: number) => {
    if (!onSelectionChange) return

    const rowId = getRowId(row, index)
    const newSelection = new Set(selectedRows)

    if (newSelection.has(rowId)) {
      newSelection.delete(rowId)
    } else {
      newSelection.add(rowId)
    }

    onSelectionChange(newSelection)
  }

  const getVisibleColumns = () => {
    if (!isMobile && !isTablet) return columns

    // On mobile/tablet, prioritize columns
    return columns.filter(col => {
      if (isMobile) {
        return col.priority === 'high' || !col.priority
      }
      if (isTablet) {
        return col.priority !== 'low'
      }
      return true
    })
  }

  const visibleColumns = getVisibleColumns()
  const allSelected = data.length > 0 && data.every((row, index) => 
    selectedRows.has(getRowId(row, index))
  )
  const someSelected = selectedRows.size > 0 && !allSelected

  if (loading) {
    return (
      <div className="w-full">
        <div className="rounded-md border">
          <div className="p-8 text-center">
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span className="text-muted-foreground">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('w-full', className)}>
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full" role="table">
            {caption && (
              <caption className="sr-only">
                {caption}
              </caption>
            )}
            
            <thead className="bg-muted/50">
              <tr role="row">
                {selectable && (
                  <th 
                    className="w-12 px-4 py-3 text-left"
                    role="columnheader"
                  >
                    <input
                      type="checkbox"
                      checked={allSelected}
                      ref={(input) => {
                        if (input) input.indeterminate = someSelected
                      }}
                      onChange={handleSelectAll}
                      className="rounded border-input"
                      aria-label="Select all rows"
                    />
                  </th>
                )}
                
                {visibleColumns.map((column) => (
                  <th
                    key={String(column.key)}
                    className={cn(
                      'px-4 py-3 text-left text-sm font-medium text-muted-foreground',
                      column.headerClassName,
                      column.align === 'center' && 'text-center',
                      column.align === 'right' && 'text-right'
                    )}
                    style={{ width: column.width }}
                    role="columnheader"
                    aria-sort={
                      sortBy === String(column.key)
                        ? sortDirection === 'asc' ? 'ascending' : 'descending'
                        : column.sortable ? 'none' : undefined
                    }
                  >
                    {column.sortable ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 font-medium hover:bg-transparent"
                        onClick={() => handleSort(column)}
                      >
                        <span>{column.label}</span>
                        <span className="ml-2">
                          {sortBy === String(column.key) ? (
                            sortDirection === 'asc' ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )
                          ) : (
                            <ChevronsUpDown className="h-4 w-4" />
                          )}
                        </span>
                      </Button>
                    ) : (
                      column.label
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            
            <tbody>
              {data.length === 0 ? (
                <tr>
                  <td
                    colSpan={visibleColumns.length + (selectable ? 1 : 0)}
                    className="px-4 py-8 text-center text-muted-foreground"
                  >
                    {emptyMessage}
                  </td>
                </tr>
              ) : (
                data.map((row, index) => {
                  const rowId = getRowId(row, index)
                  const isSelected = selectedRows.has(rowId)
                  
                  return (
                    <tr
                      key={rowId}
                      className={cn(
                        'border-t transition-colors',
                        'hover:bg-muted/50',
                        onRowClick && 'cursor-pointer',
                        isSelected && 'bg-muted',
                        rowClassName?.(row, index)
                      )}
                      onClick={() => onRowClick?.(row, index)}
                      role="row"
                      aria-selected={selectable ? isSelected : undefined}
                    >
                      {selectable && (
                        <td className="px-4 py-3" role="gridcell">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handleRowSelect(row, index)}
                            onClick={(e) => e.stopPropagation()}
                            className="rounded border-input"
                            aria-label={`Select row ${index + 1}`}
                          />
                        </td>
                      )}
                      
                      {visibleColumns.map((column) => {
                        const value = row[column.key as keyof T]
                        const cellContent = column.render
                          ? column.render(value, row, index)
                          : String(value || '')

                        return (
                          <td
                            key={String(column.key)}
                            className={cn(
                              'px-4 py-3 text-sm',
                              column.className,
                              column.align === 'center' && 'text-center',
                              column.align === 'right' && 'text-right'
                            )}
                            role="gridcell"
                          >
                            {cellContent}
                          </td>
                        )
                      })}
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Mobile card view for better UX on small screens */}
      {isMobile && data.length > 0 && (
        <div className="mt-4 space-y-4 md:hidden">
          {data.map((row, index) => {
            const rowId = getRowId(row, index)
            const isSelected = selectedRows.has(rowId)
            
            return (
              <div
                key={rowId}
                className={cn(
                  'rounded-lg border p-4 space-y-2',
                  onRowClick && 'cursor-pointer hover:bg-muted/50',
                  isSelected && 'bg-muted'
                )}
                onClick={() => onRowClick?.(row, index)}
              >
                {selectable && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleRowSelect(row, index)}
                      onClick={(e) => e.stopPropagation()}
                      className="rounded border-input"
                    />
                    <span className="text-sm text-muted-foreground">
                      Select this item
                    </span>
                  </div>
                )}
                
                {columns.map((column) => {
                  const value = row[column.key as keyof T]
                  const cellContent = column.render
                    ? column.render(value, row, index)
                    : String(value || '')

                  return (
                    <div key={String(column.key)} className="flex justify-between">
                      <span className="font-medium text-sm">{column.label}:</span>
                      <span className="text-sm text-muted-foreground">
                        {cellContent}
                      </span>
                    </div>
                  )
                })}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
