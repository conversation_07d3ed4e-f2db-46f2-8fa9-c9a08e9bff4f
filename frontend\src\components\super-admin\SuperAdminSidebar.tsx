'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard,
  Building2,
  CreditCard,
  BarChart3,
  Users,
  Settings,
  Shield,
  Globe,
  Database,
  Activity
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/super-admin',
    icon: LayoutDashboard,
    description: 'Platform overview and key metrics'
  },
  {
    name: 'Institutes',
    href: '/super-admin/institutes',
    icon: Building2,
    description: 'Manage educational institutions'
  },
  {
    name: 'Subscriptions',
    href: '/super-admin/subscriptions',
    icon: CreditCard,
    description: 'Manage subscription plans and billing'
  },
  {
    name: 'Analytics',
    href: '/super-admin/analytics',
    icon: BarChart3,
    description: 'Platform analytics and insights'
  },
  {
    name: 'Users',
    href: '/super-admin/users',
    icon: Users,
    description: 'Platform-wide user management'
  },
  {
    name: 'System',
    href: '/super-admin/system',
    icon: Database,
    description: 'System health and monitoring'
  },
  {
    name: 'Settings',
    href: '/super-admin/settings',
    icon: Settings,
    description: 'Platform configuration'
  },
]

export default function SuperAdminSidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 h-screen">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <Shield className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">LMS SAAS</h1>
            <p className="text-xs text-gray-600">Super Admin</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon
          const isActive = pathname === item.href || 
            (item.href !== '/super-admin' && pathname.startsWith(item.href))

          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                ${isActive
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <Icon
                className={`
                  mr-3 h-5 w-5 transition-colors
                  ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'}
                `}
              />
              <div className="flex-1">
                <div className="font-medium">{item.name}</div>
                <div className={`
                  text-xs mt-0.5 
                  ${isActive ? 'text-blue-600' : 'text-gray-500'}
                `}>
                  {item.description}
                </div>
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Platform Status */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <div>
            <p className="text-sm font-medium text-green-900">Platform Status</p>
            <p className="text-xs text-green-700">All systems operational</p>
          </div>
        </div>
      </div>
    </div>
  )
}
