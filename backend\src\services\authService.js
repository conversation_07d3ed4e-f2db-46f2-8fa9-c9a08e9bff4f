const { query, transaction } = require('../database/connection');
const { passwordUtils, tokenUtils, verificationUtils, responseUtils, sessionUtils } = require('../utils/auth');
const { v4: uuidv4 } = require('uuid');

/**
 * Authentication Service
 * Handles user registration, login, email verification, and password reset
 */

class AuthService {
  /**
   * Register a new user
   */
  async registerUser(userData, userType = 'student') {
    const { email, password, firstName, lastName, phone, instituteId } = userData;

    try {
      // Check if user already exists
      const existingUser = await this.getUserByEmail(email, instituteId);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Hash password
      const passwordHash = await passwordUtils.hashPassword(password);

      // Generate email verification token
      const emailVerification = verificationUtils.generateEmailVerificationToken();

      // Create user in transaction
      const result = await transaction(async (client) => {
        // Insert user
        const userResult = await client.query(`
          INSERT INTO users (id, institute_id, email, password_hash, first_name, last_name, phone, role, is_email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING *
        `, [
          uuidv4(),
          instituteId,
          email.toLowerCase(),
          passwordHash,
          firstName,
          lastName,
          phone,
          userType,
          false // Email not verified initially
        ]);

        const user = userResult.rows[0];

        // Insert email verification token
        await client.query(`
          INSERT INTO email_verifications (id, user_id, institute_id, email, verification_token, token_type, expires_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          uuidv4(),
          user.id,
          instituteId,
          email.toLowerCase(),
          emailVerification.token,
          'email_verification',
          emailVerification.expires
        ]);

        return { user, verificationToken: emailVerification.token };
      });

      return {
        user: result.user,
        verificationToken: result.verificationToken,
        requiresVerification: true
      };

    } catch (error) {
      console.error('User registration error:', error.message);
      throw error;
    }
  }

  /**
   * Register institute admin and create institute
   */
  async registerInstituteAdmin(userData) {
    const {
      email, password, firstName, lastName, phone,
      instituteName, instituteEmail, institutePhone, instituteAddress, instituteWebsite
    } = userData;

    try {
      // Check if institute email already exists
      const existingInstitute = await query(
        'SELECT id FROM institutes WHERE email = $1',
        [instituteEmail.toLowerCase()]
      );

      if (existingInstitute.rows.length > 0) {
        throw new Error('Institute with this email already exists');
      }

      // Create institute and admin user in transaction
      const result = await transaction(async (client) => {
        // Create institute
        const instituteSlug = instituteName
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');

        const instituteResult = await client.query(`
          INSERT INTO institutes (id, name, slug, email, phone, address, website, subscription_plan, is_active)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING *
        `, [
          uuidv4(),
          instituteName,
          instituteSlug,
          instituteEmail.toLowerCase(),
          institutePhone,
          instituteAddress,
          instituteWebsite,
          'basic',
          true
        ]);

        const institute = instituteResult.rows[0];

        // Register admin user
        const adminData = {
          email,
          password,
          firstName,
          lastName,
          phone,
          instituteId: institute.id
        };

        // Hash password
        const passwordHash = await passwordUtils.hashPassword(password);

        // Generate email verification token
        const emailVerification = verificationUtils.generateEmailVerificationToken();

        // Insert admin user
        const userResult = await client.query(`
          INSERT INTO users (id, institute_id, email, password_hash, first_name, last_name, phone, role, is_email_verified)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING *
        `, [
          uuidv4(),
          institute.id,
          email.toLowerCase(),
          passwordHash,
          firstName,
          lastName,
          phone,
          'institute_admin',
          false
        ]);

        const user = userResult.rows[0];

        // Insert email verification token
        await client.query(`
          INSERT INTO email_verifications (id, user_id, institute_id, email, verification_token, token_type, expires_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          uuidv4(),
          user.id,
          institute.id,
          email.toLowerCase(),
          emailVerification.token,
          'email_verification',
          emailVerification.expires
        ]);

        return {
          institute,
          user,
          verificationToken: emailVerification.token
        };
      });

      return result;

    } catch (error) {
      console.error('Institute admin registration error:', error.message);
      throw error;
    }
  }

  /**
   * Login user
   */
  async loginUser(email, password, req) {
    try {
      // Get user by email
      const user = await this.getUserByEmail(email, req.tenant?.instituteId);
      
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.is_active) {
        throw new Error('User account is deactivated');
      }

      // Verify password
      const isPasswordValid = await passwordUtils.comparePassword(password, user.password_hash);
      
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Generate tokens
      const tokenPayload = tokenUtils.createUserPayload(user);
      const accessToken = tokenUtils.generateToken(tokenPayload);
      const refreshToken = tokenUtils.generateRefreshToken();

      // Create session
      const sessionData = sessionUtils.createSessionData(user, req);
      sessionData.refreshToken = refreshToken;

      await query(`
        INSERT INTO user_sessions (id, user_id, institute_id, session_token, refresh_token, ip_address, user_agent, expires_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        uuidv4(),
        user.id,
        user.institute_id,
        accessToken,
        refreshToken,
        sessionData.ipAddress,
        sessionData.userAgent,
        sessionData.expiresAt
      ]);

      // Update last login time
      await query(
        'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
        [user.id]
      );

      return {
        user,
        accessToken,
        refreshToken
      };

    } catch (error) {
      console.error('User login error:', error.message);
      throw error;
    }
  }

  /**
   * Verify email address
   */
  async verifyEmail(token, email = null) {
    try {
      // Find verification token
      let query_sql = 'SELECT * FROM email_verifications WHERE verification_token = $1 AND token_type = $2 AND is_used = false AND expires_at > CURRENT_TIMESTAMP';
      let params = [token, 'email_verification'];

      if (email) {
        query_sql += ' AND email = $3';
        params.push(email.toLowerCase());
      }

      const tokenResult = await query(query_sql, params);

      if (tokenResult.rows.length === 0) {
        throw new Error('Invalid or expired verification token');
      }

      const verificationRecord = tokenResult.rows[0];

      // Update user and mark token as used in transaction
      const result = await transaction(async (client) => {
        // Mark user as verified
        await client.query(
          'UPDATE users SET is_email_verified = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
          [verificationRecord.user_id]
        );

        // Mark token as used
        await client.query(
          'UPDATE email_verifications SET is_used = true, used_at = CURRENT_TIMESTAMP WHERE id = $1',
          [verificationRecord.id]
        );

        // Get updated user
        const userResult = await client.query(
          'SELECT * FROM users WHERE id = $1',
          [verificationRecord.user_id]
        );

        return userResult.rows[0];
      });

      return result;

    } catch (error) {
      console.error('Email verification error:', error.message);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email, instituteId = null) {
    try {
      // Find user
      const user = await this.getUserByEmail(email, instituteId);
      
      if (!user) {
        // Don't reveal if user exists or not
        return { success: true, message: 'If the email exists, a reset link has been sent' };
      }

      // Generate reset token
      const resetToken = verificationUtils.generatePasswordResetToken();

      // Store reset token
      await query(`
        INSERT INTO email_verifications (id, user_id, institute_id, email, verification_token, token_type, expires_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        uuidv4(),
        user.id,
        user.institute_id,
        email.toLowerCase(),
        resetToken.token,
        'password_reset',
        resetToken.expires
      ]);

      return {
        success: true,
        resetToken: resetToken.token,
        userId: user.id,
        message: 'Password reset token generated'
      };

    } catch (error) {
      console.error('Password reset request error:', error.message);
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token, newPassword) {
    try {
      // Find valid reset token
      const tokenResult = await query(
        'SELECT * FROM email_verifications WHERE verification_token = $1 AND token_type = $2 AND is_used = false AND expires_at > CURRENT_TIMESTAMP',
        [token, 'password_reset']
      );

      if (tokenResult.rows.length === 0) {
        throw new Error('Invalid or expired reset token');
      }

      const resetRecord = tokenResult.rows[0];

      // Hash new password
      const passwordHash = await passwordUtils.hashPassword(newPassword);

      // Update password and mark token as used in transaction
      await transaction(async (client) => {
        // Update password
        await client.query(
          'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [passwordHash, resetRecord.user_id]
        );

        // Mark token as used
        await client.query(
          'UPDATE email_verifications SET is_used = true, used_at = CURRENT_TIMESTAMP WHERE id = $1',
          [resetRecord.id]
        );

        // Invalidate all existing sessions for security
        await client.query(
          'UPDATE user_sessions SET is_active = false WHERE user_id = $1',
          [resetRecord.user_id]
        );
      });

      return { success: true, message: 'Password reset successfully' };

    } catch (error) {
      console.error('Password reset error:', error.message);
      throw error;
    }
  }

  /**
   * Change password for authenticated user
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Get user
      const userResult = await query('SELECT * FROM users WHERE id = $1', [userId]);
      
      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Verify current password
      const isCurrentPasswordValid = await passwordUtils.comparePassword(currentPassword, user.password_hash);
      
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const passwordHash = await passwordUtils.hashPassword(newPassword);

      // Update password
      await query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [passwordHash, userId]
      );

      return { success: true, message: 'Password changed successfully' };

    } catch (error) {
      console.error('Change password error:', error.message);
      throw error;
    }
  }

  /**
   * Logout user (invalidate session)
   */
  async logoutUser(userId, sessionToken) {
    try {
      await query(
        'UPDATE user_sessions SET is_active = false WHERE user_id = $1 AND session_token = $2',
        [userId, sessionToken]
      );

      return { success: true, message: 'Logged out successfully' };

    } catch (error) {
      console.error('Logout error:', error.message);
      throw error;
    }
  }

  /**
   * Get user by email (tenant-scoped)
   */
  async getUserByEmail(email, instituteId = null) {
    try {
      let query_sql = 'SELECT * FROM users WHERE email = $1';
      let params = [email.toLowerCase()];

      if (instituteId) {
        query_sql += ' AND institute_id = $2';
        params.push(instituteId);
      } else {
        // For super admin (no institute)
        query_sql += ' AND institute_id IS NULL';
      }

      const result = await query(query_sql, params);
      return result.rows[0] || null;

    } catch (error) {
      console.error('Get user by email error:', error.message);
      throw error;
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(refreshToken) {
    try {
      // Find valid session with refresh token
      const sessionResult = await query(
        'SELECT s.*, u.* FROM user_sessions s JOIN users u ON s.user_id = u.id WHERE s.refresh_token = $1 AND s.is_active = true AND s.expires_at > CURRENT_TIMESTAMP',
        [refreshToken]
      );

      if (sessionResult.rows.length === 0) {
        throw new Error('Invalid or expired refresh token');
      }

      const session = sessionResult.rows[0];

      // Generate new access token
      const tokenPayload = tokenUtils.createUserPayload(session);
      const newAccessToken = tokenUtils.generateToken(tokenPayload);

      // Update session with new access token
      await query(
        'UPDATE user_sessions SET session_token = $1, last_accessed_at = CURRENT_TIMESTAMP WHERE id = $2',
        [newAccessToken, session.id]
      );

      return {
        accessToken: newAccessToken,
        user: {
          id: session.user_id,
          email: session.email,
          firstName: session.first_name,
          lastName: session.last_name,
          role: session.role,
          instituteId: session.institute_id
        }
      };

    } catch (error) {
      console.error('Refresh token error:', error.message);
      throw error;
    }
  }
}

module.exports = new AuthService();
