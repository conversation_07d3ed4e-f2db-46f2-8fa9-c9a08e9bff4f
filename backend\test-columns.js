const { query } = require('./src/database/connection');

async function checkColumns() {
  try {
    const result = await query('SELECT * FROM pg_stat_user_indexes WHERE schemaname = $1 LIMIT 1', ['public']);
    console.log('Sample row:', result.rows[0]);
    console.log('Columns:', Object.keys(result.rows[0] || {}));
  } catch (error) {
    console.log('Error:', error.message);
  }
  process.exit(0);
}

checkColumns();
