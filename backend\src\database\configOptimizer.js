const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

/**
 * PostgreSQL Configuration Optimizer
 * Analyzes system resources and generates optimized PostgreSQL configuration
 */

class PostgreSQLConfigOptimizer {
  constructor() {
    this.pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'lte_lms',
      password: process.env.DB_PASSWORD || '1234',
      port: process.env.DB_PORT || 5432,
    });

    this.systemInfo = this.getSystemInfo();
  }

  /**
   * Get system information for optimization calculations
   */
  getSystemInfo() {
    const totalMemoryGB = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    const cpuCores = os.cpus().length;
    const platform = os.platform();

    return {
      totalMemoryGB,
      cpuCores,
      platform,
      availableMemoryGB: Math.round(os.freemem() / (1024 * 1024 * 1024))
    };
  }

  /**
   * Get current PostgreSQL configuration
   */
  async getCurrentConfig() {
    try {
      const configQuery = `
        SELECT 
          name,
          setting,
          unit,
          category,
          short_desc,
          context,
          vartype,
          source,
          min_val,
          max_val,
          boot_val,
          reset_val
        FROM pg_settings 
        WHERE name IN (
          'max_connections',
          'shared_buffers',
          'effective_cache_size',
          'maintenance_work_mem',
          'checkpoint_completion_target',
          'wal_buffers',
          'default_statistics_target',
          'random_page_cost',
          'effective_io_concurrency',
          'work_mem',
          'min_wal_size',
          'max_wal_size',
          'max_worker_processes',
          'max_parallel_workers_per_gather',
          'max_parallel_workers',
          'max_parallel_maintenance_workers'
        )
        ORDER BY category, name;
      `;

      const result = await this.pool.query(configQuery);
      return result.rows;

    } catch (error) {
      console.error('Error getting current configuration:', error.message);
      throw error;
    }
  }

  /**
   * Calculate optimized configuration values
   */
  calculateOptimizedConfig() {
    const { totalMemoryGB, cpuCores } = this.systemInfo;

    // Memory-based calculations (conservative for multi-tenant environment)
    const sharedBuffersGB = Math.max(1, Math.min(8, Math.floor(totalMemoryGB * 0.25)));
    const effectiveCacheSizeGB = Math.max(2, Math.floor(totalMemoryGB * 0.75));
    const maintenanceWorkMemMB = Math.max(64, Math.min(2048, Math.floor(totalMemoryGB * 64)));
    const workMemMB = Math.max(4, Math.min(256, Math.floor(totalMemoryGB * 4)));

    // Connection-based calculations
    const maxConnections = Math.max(100, Math.min(400, cpuCores * 25));

    // WAL-based calculations
    const walBuffersMB = Math.max(16, Math.min(64, sharedBuffersGB * 16));
    const minWalSizeGB = Math.max(1, Math.min(4, sharedBuffersGB));
    const maxWalSizeGB = Math.max(4, Math.min(16, sharedBuffersGB * 4));

    // CPU-based calculations
    const maxWorkerProcesses = Math.max(8, cpuCores * 2);
    const maxParallelWorkers = Math.max(2, Math.min(cpuCores, 8));
    const maxParallelWorkersPerGather = Math.max(2, Math.min(4, Math.floor(cpuCores / 2)));
    const maxParallelMaintenanceWorkers = Math.max(2, Math.min(4, Math.floor(cpuCores / 2)));

    // I/O-based calculations
    const effectiveIoConcurrency = this.systemInfo.platform === 'win32' ? 1 : Math.min(200, cpuCores * 2);
    const randomPageCost = this.systemInfo.platform === 'win32' ? 4.0 : 1.1; // Assume SSD on non-Windows

    return {
      // Memory settings
      shared_buffers: `${sharedBuffersGB}GB`,
      effective_cache_size: `${effectiveCacheSizeGB}GB`,
      maintenance_work_mem: `${maintenanceWorkMemMB}MB`,
      work_mem: `${workMemMB}MB`,

      // Connection settings
      max_connections: maxConnections,

      // WAL settings
      wal_buffers: `${walBuffersMB}MB`,
      min_wal_size: `${minWalSizeGB}GB`,
      max_wal_size: `${maxWalSizeGB}GB`,

      // Checkpoint settings
      checkpoint_completion_target: 0.9,

      // Query planner settings
      default_statistics_target: 100,
      random_page_cost: randomPageCost,
      effective_io_concurrency: effectiveIoConcurrency,

      // Parallel query settings
      max_worker_processes: maxWorkerProcesses,
      max_parallel_workers: maxParallelWorkers,
      max_parallel_workers_per_gather: maxParallelWorkersPerGather,
      max_parallel_maintenance_workers: maxParallelMaintenanceWorkers,

      // Additional optimizations for multi-tenant LMS
      log_min_duration_statement: 1000, // Log slow queries
      log_checkpoints: 'on',
      log_connections: 'on',
      log_disconnections: 'on',
      log_lock_waits: 'on',
      log_statement: 'ddl', // Log DDL statements
      shared_preload_libraries: 'pg_stat_statements', // Enable query statistics
      track_activity_query_size: 2048,
      track_counts: 'on',
      track_functions: 'all',
      track_io_timing: 'on'
    };
  }

  /**
   * Generate PostgreSQL configuration file
   */
  generateConfigFile(optimizedConfig) {
    const { totalMemoryGB, cpuCores, platform } = this.systemInfo;

    let configContent = `# PostgreSQL Configuration - Optimized for LMS SAAS
# Generated on: ${new Date().toISOString()}
# System: ${platform}, ${totalMemoryGB}GB RAM, ${cpuCores} CPU cores
# Optimized for multi-tenant LMS workload

# -----------------------------
# CONNECTIONS AND AUTHENTICATION
# -----------------------------

max_connections = ${optimizedConfig.max_connections}
listen_addresses = '*'
port = 5432

# SSL Configuration (recommended for production)
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'

# -----------------------------
# RESOURCE USAGE (except WAL)
# -----------------------------

# Memory
shared_buffers = ${optimizedConfig.shared_buffers}
work_mem = ${optimizedConfig.work_mem}
maintenance_work_mem = ${optimizedConfig.maintenance_work_mem}
effective_cache_size = ${optimizedConfig.effective_cache_size}

# Kernel Resource Usage
max_worker_processes = ${optimizedConfig.max_worker_processes}
max_parallel_workers = ${optimizedConfig.max_parallel_workers}
max_parallel_workers_per_gather = ${optimizedConfig.max_parallel_workers_per_gather}
max_parallel_maintenance_workers = ${optimizedConfig.max_parallel_maintenance_workers}

# -----------------------------
# WRITE AHEAD LOG
# -----------------------------

wal_buffers = ${optimizedConfig.wal_buffers}
min_wal_size = ${optimizedConfig.min_wal_size}
max_wal_size = ${optimizedConfig.max_wal_size}
checkpoint_completion_target = ${optimizedConfig.checkpoint_completion_target}

# WAL archiving (for backup and replication)
archive_mode = on
archive_command = 'copy "%p" "C:\\\\postgres_archive\\\\%f"'  # Windows
# archive_command = 'cp %p /var/lib/postgresql/archive/%f'    # Linux

# -----------------------------
# QUERY TUNING
# -----------------------------

# Planner Cost Constants
random_page_cost = ${optimizedConfig.random_page_cost}
effective_io_concurrency = ${optimizedConfig.effective_io_concurrency}

# Planner Method Configuration
enable_partitionwise_join = on
enable_partitionwise_aggregate = on

# Other Planner Options
default_statistics_target = ${optimizedConfig.default_statistics_target}

# -----------------------------
# REPORTING AND LOGGING
# -----------------------------

# What to log
log_min_duration_statement = ${optimizedConfig.log_min_duration_statement}
log_checkpoints = ${optimizedConfig.log_checkpoints}
log_connections = ${optimizedConfig.log_connections}
log_disconnections = ${optimizedConfig.log_disconnections}
log_lock_waits = ${optimizedConfig.log_lock_waits}
log_statement = '${optimizedConfig.log_statement}'

# Where to log
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB

# What to log (detailed)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_timezone = 'UTC'

# -----------------------------
# RUNTIME STATISTICS
# -----------------------------

shared_preload_libraries = '${optimizedConfig.shared_preload_libraries}'
track_activity_query_size = ${optimizedConfig.track_activity_query_size}
track_counts = ${optimizedConfig.track_counts}
track_functions = ${optimizedConfig.track_functions}
track_io_timing = ${optimizedConfig.track_io_timing}

# pg_stat_statements configuration
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# -----------------------------
# AUTOVACUUM PARAMETERS
# -----------------------------

autovacuum = on
autovacuum_max_workers = ${Math.min(6, Math.max(3, Math.floor(cpuCores / 2)))}
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05

# -----------------------------
# CLIENT CONNECTION DEFAULTS
# -----------------------------

timezone = 'UTC'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'
default_text_search_config = 'pg_catalog.english'

# -----------------------------
# LOCK MANAGEMENT
# -----------------------------

deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# -----------------------------
# VERSION/PLATFORM COMPATIBILITY
# -----------------------------

# (none currently required)

# -----------------------------
# CUSTOMIZED OPTIONS
# -----------------------------

# Multi-tenant specific settings
row_security = on  # Enable row-level security
shared_buffers_ring_size = 256kB  # Optimize for many small queries

# Performance monitoring
log_parser_stats = off
log_planner_stats = off
log_executor_stats = off
log_statement_stats = off

# Security settings
password_encryption = scram-sha-256
ssl_prefer_server_ciphers = on
ssl_min_protocol_version = 'TLSv1.2'
`;

    return configContent;
  }

  /**
   * Generate pg_hba.conf for secure authentication
   */
  generateHbaConfig() {
    return `# PostgreSQL Client Authentication Configuration File
# Generated on: ${new Date().toISOString()}
# Optimized for LMS SAAS security

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             postgres                                peer

# IPv4 local connections:
host    all             postgres        127.0.0.1/32            scram-sha-256
host    lte_lms         postgres        127.0.0.1/32            scram-sha-256

# IPv6 local connections:
host    all             postgres        ::1/128                 scram-sha-256
host    lte_lms         postgres        ::1/128                 scram-sha-256

# Application connections (adjust IP ranges as needed)
host    lte_lms         postgres        10.0.0.0/8              scram-sha-256
host    lte_lms         postgres        **********/12           scram-sha-256
host    lte_lms         postgres        ***********/16          scram-sha-256

# SSL connections for production
hostssl lte_lms         postgres        0.0.0.0/0               scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject
`;
  }

  /**
   * Run complete configuration optimization
   */
  async optimizeConfiguration() {
    console.log('🔧 Starting PostgreSQL configuration optimization...\n');

    try {
      console.log('📊 System Information:');
      console.log(`💾 Total Memory: ${this.systemInfo.totalMemoryGB}GB`);
      console.log(`🖥️ CPU Cores: ${this.systemInfo.cpuCores}`);
      console.log(`🖥️ Platform: ${this.systemInfo.platform}`);
      console.log(`💾 Available Memory: ${this.systemInfo.availableMemoryGB}GB\n`);

      console.log('🔍 Analyzing current PostgreSQL configuration...');
      const currentConfig = await this.getCurrentConfig();

      console.log('⚡ Calculating optimized configuration...');
      const optimizedConfig = this.calculateOptimizedConfig();

      console.log('📝 Generating configuration files...');
      const postgresqlConf = this.generateConfigFile(optimizedConfig);
      const pgHbaConf = this.generateHbaConfig();

      // Create config directory
      const configDir = path.join(__dirname, '../config');
      await fs.mkdir(configDir, { recursive: true });

      // Save configuration files
      const postgresqlConfPath = path.join(configDir, 'postgresql.conf');
      const pgHbaConfPath = path.join(configDir, 'pg_hba.conf');
      const currentConfigPath = path.join(configDir, 'current-config.json');
      const optimizedConfigPath = path.join(configDir, 'optimized-config.json');

      await Promise.all([
        fs.writeFile(postgresqlConfPath, postgresqlConf),
        fs.writeFile(pgHbaConfPath, pgHbaConf),
        fs.writeFile(currentConfigPath, JSON.stringify(currentConfig, null, 2)),
        fs.writeFile(optimizedConfigPath, JSON.stringify(optimizedConfig, null, 2))
      ]);

      console.log('\n📄 Generated Configuration Files:');
      console.log(`📝 PostgreSQL Config: ${postgresqlConfPath}`);
      console.log(`🔐 Authentication Config: ${pgHbaConfPath}`);
      console.log(`📊 Current Settings: ${currentConfigPath}`);
      console.log(`⚡ Optimized Settings: ${optimizedConfigPath}`);

      console.log('\n🔧 Key Optimizations:');
      console.log(`🔗 Max Connections: ${optimizedConfig.max_connections}`);
      console.log(`💾 Shared Buffers: ${optimizedConfig.shared_buffers}`);
      console.log(`💾 Effective Cache Size: ${optimizedConfig.effective_cache_size}`);
      console.log(`💾 Work Memory: ${optimizedConfig.work_mem}`);
      console.log(`⚡ Max Worker Processes: ${optimizedConfig.max_worker_processes}`);

      console.log('\n⚠️ Next Steps:');
      console.log('1. Review the generated postgresql.conf file');
      console.log('2. Backup your current PostgreSQL configuration');
      console.log('3. Apply the new configuration (requires PostgreSQL restart)');
      console.log('4. Monitor performance after applying changes');
      console.log('5. Adjust settings based on actual workload patterns');

      return {
        currentConfig,
        optimizedConfig,
        configFiles: {
          postgresql: postgresqlConfPath,
          pgHba: pgHbaConfPath
        }
      };

    } catch (error) {
      console.error('❌ Configuration optimization failed:', error.message);
      throw error;
    } finally {
      await this.pool.end();
    }
  }
}

// Run optimization if script is executed directly
if (require.main === module) {
  const optimizer = new PostgreSQLConfigOptimizer();
  optimizer.optimizeConfiguration()
    .then(() => {
      console.log('\n✅ PostgreSQL configuration optimization completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Configuration optimization failed:', error.message);
      process.exit(1);
    });
}

module.exports = { PostgreSQLConfigOptimizer };
