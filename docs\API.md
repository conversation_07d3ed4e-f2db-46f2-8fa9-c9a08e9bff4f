# API Documentation

This document describes the REST API endpoints for the LMS SAAS platform.

## Base URL

- Development: `http://localhost:5010`
- Production: `https://api.exampllms.com`

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {},
  "message": "Optional message",
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

Error responses:

```json
{
  "success": false,
  "error": "Error message",
  "errors": ["Detailed error messages"],
  "code": "ERROR_CODE"
}
```

## Endpoints

### Authentication

#### POST /api/auth/register
Register a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "role": "student",
  "instituteId": "inst-123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "student",
      "isActive": true
    },
    "token": "jwt-token-here"
  }
}
```

#### POST /api/auth/login
Authenticate a user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "student"
    },
    "token": "jwt-token-here",
    "refreshToken": "refresh-token-here"
  }
}
```

#### POST /api/auth/refresh
Refresh an access token.

**Request Body:**
```json
{
  "refreshToken": "refresh-token-here"
}
```

#### POST /api/auth/logout
Logout a user (invalidate tokens).

**Headers:** `Authorization: Bearer <token>`

#### POST /api/auth/forgot-password
Request password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST /api/auth/reset-password
Reset password with token.

**Request Body:**
```json
{
  "token": "reset-token",
  "password": "newpassword123"
}
```

### Users

#### GET /api/users/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "student",
    "instituteId": "inst-123",
    "profile": {
      "studentId": "STU001",
      "major": "Computer Science"
    }
  }
}
```

#### PUT /api/users/profile
Update current user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "profile": {
    "major": "Computer Science"
  }
}
```

#### GET /api/users
Get all users (admin only).

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `role` (string): Filter by role
- `search` (string): Search by name or email
- `instituteId` (string): Filter by institute

#### GET /api/users/:id
Get user by ID.

**Headers:** `Authorization: Bearer <token>`

#### PUT /api/users/:id
Update user (admin only).

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/users/:id
Delete user (admin only).

**Headers:** `Authorization: Bearer <token>`

### Institutes

#### GET /api/institutes
Get all institutes (super admin only).

**Headers:** `Authorization: Bearer <token>`

#### POST /api/institutes
Create new institute (super admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "Example University",
  "email": "<EMAIL>",
  "website": "https://example.edu",
  "subscriptionPlan": "premium"
}
```

#### GET /api/institutes/:id
Get institute by ID.

**Headers:** `Authorization: Bearer <token>`

#### PUT /api/institutes/:id
Update institute.

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/institutes/:id
Delete institute (super admin only).

**Headers:** `Authorization: Bearer <token>`

#### GET /api/institutes/:id/branding
Get institute branding.

**Headers:** `Authorization: Bearer <token>`

#### PUT /api/institutes/:id/branding
Update institute branding.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "primaryColor": "#3B82F6",
  "secondaryColor": "#1E40AF",
  "accentColor": "#F59E0B",
  "logoUrl": "https://example.com/logo.png"
}
```

### Courses

#### GET /api/courses
Get courses.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `status` (string): Filter by status
- `teacherId` (string): Filter by teacher
- `search` (string): Search by title or code

#### POST /api/courses
Create new course (teacher/admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "Introduction to Computer Science",
  "description": "Basic concepts of computer science",
  "code": "CS101",
  "credits": 3,
  "maxStudents": 30
}
```

#### GET /api/courses/:id
Get course by ID.

**Headers:** `Authorization: Bearer <token>`

#### PUT /api/courses/:id
Update course (teacher/admin only).

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/courses/:id
Delete course (admin only).

**Headers:** `Authorization: Bearer <token>`

#### POST /api/courses/:id/enroll
Enroll in course (student only).

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/courses/:id/enroll
Unenroll from course.

**Headers:** `Authorization: Bearer <token>`

#### GET /api/courses/:id/students
Get course students (teacher/admin only).

**Headers:** `Authorization: Bearer <token>`

### File Upload

#### POST /api/upload
Upload file.

**Headers:** 
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Request Body:**
- `file`: File to upload
- `type`: File type (avatar, document, etc.)

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/uploads/file.jpg",
    "filename": "file.jpg",
    "size": 1024,
    "mimeType": "image/jpeg"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource already exists |
| `RATE_LIMITED` | Too many requests |
| `SERVER_ERROR` | Internal server error |

## Rate Limiting

API endpoints are rate limited:
- Authentication endpoints: 5 requests per minute
- General endpoints: 100 requests per 15 minutes
- Upload endpoints: 10 requests per minute

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

**Response includes pagination info:**
```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Filtering and Sorting

Many endpoints support filtering and sorting:

**Query Parameters:**
- `search`: Search term
- `sortBy`: Field to sort by
- `sortOrder`: `asc` or `desc`
- `filter[field]`: Filter by field value

Example:
```
GET /api/users?search=john&sortBy=createdAt&sortOrder=desc&filter[role]=student
```

## WebSocket Events

Real-time events are available via WebSocket connection:

**Connection:** `ws://localhost:5010/ws`

**Events:**
- `user:online` - User came online
- `user:offline` - User went offline
- `course:updated` - Course was updated
- `notification:new` - New notification

## SDK and Libraries

Official SDKs are available for:
- JavaScript/TypeScript
- Python
- PHP
- Java

## Postman Collection

Import our Postman collection for easy API testing:
[Download Collection](./postman/LMS-SAAS-API.postman_collection.json)
