const { query, transaction } = require('../database/connection');

/**
 * Super Admin Service
 * Handles platform management operations, analytics, and administrative functions
 */

class SuperAdminService {
  /**
   * Log super admin activity
   */
  async logActivity(adminId, actionType, targetType, targetId, actionDetails = {}, req = null) {
    try {
      const ipAddress = req?.ip || null;
      const userAgent = req?.get('User-Agent') || null;

      await query(`
        INSERT INTO super_admin_activity_log (
          admin_id, action_type, target_type, target_id, 
          action_details, ip_address, user_agent
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        adminId,
        actionType,
        targetType,
        targetId,
        JSON.stringify(actionDetails),
        ipAddress,
        userAgent
      ]);

    } catch (error) {
      console.error('Failed to log super admin activity:', error.message);
    }
  }

  /**
   * Get platform health metrics
   */
  async getPlatformHealth() {
    try {
      const healthMetrics = await query(`
        SELECT 
          -- Database health
          (SELECT COUNT(*) FROM institutes WHERE is_active = TRUE) as active_institutes,
          (SELECT COUNT(*) FROM users WHERE is_active = TRUE) as active_users,
          (SELECT COUNT(*) FROM user_sessions WHERE is_active = TRUE) as active_sessions,
          
          -- Security health
          (SELECT COUNT(*) FROM blocked_ips WHERE expires_at > CURRENT_TIMESTAMP) as blocked_ips,
          (SELECT COUNT(*) FROM security_audit_log WHERE severity = 'critical' AND created_at >= CURRENT_DATE) as critical_security_events,
          (SELECT COUNT(*) FROM failed_login_attempts WHERE attempt_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour') as recent_failed_logins,
          
          -- System health
          (SELECT COUNT(*) FROM maintenance_windows WHERE status = 'in_progress') as active_maintenance,
          (SELECT COUNT(*) FROM platform_notifications WHERE severity = 'critical' AND NOT is_dismissed) as critical_notifications
      `);

      const metrics = healthMetrics.rows[0];

      // Calculate health score (0-100)
      let healthScore = 100;
      
      // Deduct points for issues
      if (metrics.blocked_ips > 10) healthScore -= 10;
      if (metrics.critical_security_events > 0) healthScore -= 20;
      if (metrics.recent_failed_logins > 50) healthScore -= 15;
      if (metrics.active_maintenance > 0) healthScore -= 5;
      if (metrics.critical_notifications > 0) healthScore -= 10;

      // Ensure score doesn't go below 0
      healthScore = Math.max(0, healthScore);

      return {
        healthScore,
        status: healthScore >= 90 ? 'excellent' : 
                healthScore >= 70 ? 'good' : 
                healthScore >= 50 ? 'warning' : 'critical',
        metrics
      };

    } catch (error) {
      console.error('Failed to get platform health:', error.message);
      return {
        healthScore: 0,
        status: 'error',
        metrics: {},
        error: error.message
      };
    }
  }

  /**
   * Generate daily analytics cache
   */
  async generateDailyAnalytics(date = new Date()) {
    try {
      const targetDate = date.toISOString().split('T')[0]; // YYYY-MM-DD format

      const dailyStats = await query(`
        SELECT 
          -- Institute stats
          (SELECT COUNT(*) FROM institutes WHERE DATE(created_at) = $1) as new_institutes,
          (SELECT COUNT(*) FROM institutes WHERE is_active = TRUE) as total_active_institutes,
          
          -- User stats
          (SELECT COUNT(*) FROM users WHERE DATE(created_at) = $1) as new_users,
          (SELECT COUNT(*) FROM users WHERE is_active = TRUE) as total_active_users,
          (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = TRUE) as active_students,
          (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = TRUE) as active_teachers,
          
          -- Activity stats
          (SELECT COUNT(*) FROM user_sessions WHERE DATE(created_at) = $1) as new_sessions,
          (SELECT COUNT(*) FROM security_audit_log WHERE DATE(created_at) = $1) as security_events,
          (SELECT COUNT(*) FROM failed_login_attempts WHERE DATE(attempt_time) = $1) as failed_logins,
          
          -- Subscription stats
          (SELECT COUNT(*) FROM institutes WHERE subscription_plan = 'free' AND is_active = TRUE) as free_subscriptions,
          (SELECT COUNT(*) FROM institutes WHERE subscription_plan = 'basic' AND is_active = TRUE) as basic_subscriptions,
          (SELECT COUNT(*) FROM institutes WHERE subscription_plan = 'premium' AND is_active = TRUE) as premium_subscriptions,
          (SELECT COUNT(*) FROM institutes WHERE subscription_plan = 'enterprise' AND is_active = TRUE) as enterprise_subscriptions
      `, [targetDate]);

      const stats = dailyStats.rows[0];

      // Cache the analytics
      await query(`
        INSERT INTO platform_analytics_cache (metric_type, metric_date, metric_data)
        VALUES ('daily_stats', $1, $2)
        ON CONFLICT (metric_type, metric_date)
        DO UPDATE SET 
          metric_data = EXCLUDED.metric_data,
          updated_at = CURRENT_TIMESTAMP
      `, [targetDate, JSON.stringify(stats)]);

      return stats;

    } catch (error) {
      console.error('Failed to generate daily analytics:', error.message);
      throw error;
    }
  }

  /**
   * Create platform notification
   */
  async createNotification(notificationData) {
    try {
      const {
        notificationType,
        title,
        message,
        severity = 'info',
        targetAudience = 'super_admin',
        targetInstituteId = null,
        expiresAt = null,
        createdBy
      } = notificationData;

      const result = await query(`
        INSERT INTO platform_notifications (
          notification_type, title, message, severity, 
          target_audience, target_institute_id, expires_at, created_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `, [
        notificationType,
        title,
        message,
        severity,
        targetAudience,
        targetInstituteId,
        expiresAt,
        createdBy
      ]);

      return result.rows[0];

    } catch (error) {
      console.error('Failed to create notification:', error.message);
      throw error;
    }
  }

  /**
   * Get institute usage statistics
   */
  async getInstituteUsageStats(instituteId, startDate, endDate) {
    try {
      const stats = await query(`
        SELECT 
          stat_date,
          active_users,
          new_users,
          total_logins,
          storage_used_mb,
          api_requests,
          feature_usage
        FROM institute_usage_stats
        WHERE institute_id = $1
        AND stat_date BETWEEN $2 AND $3
        ORDER BY stat_date
      `, [instituteId, startDate, endDate]);

      return stats.rows;

    } catch (error) {
      console.error('Failed to get institute usage stats:', error.message);
      throw error;
    }
  }

  /**
   * Update institute usage statistics
   */
  async updateInstituteUsageStats(instituteId, date, statsData) {
    try {
      const {
        activeUsers = 0,
        newUsers = 0,
        totalLogins = 0,
        storageUsedMb = 0,
        apiRequests = 0,
        featureUsage = {}
      } = statsData;

      await query(`
        INSERT INTO institute_usage_stats (
          institute_id, stat_date, active_users, new_users, 
          total_logins, storage_used_mb, api_requests, feature_usage
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (institute_id, stat_date)
        DO UPDATE SET
          active_users = EXCLUDED.active_users,
          new_users = EXCLUDED.new_users,
          total_logins = EXCLUDED.total_logins,
          storage_used_mb = EXCLUDED.storage_used_mb,
          api_requests = EXCLUDED.api_requests,
          feature_usage = EXCLUDED.feature_usage
      `, [
        instituteId,
        date,
        activeUsers,
        newUsers,
        totalLogins,
        storageUsedMb,
        apiRequests,
        JSON.stringify(featureUsage)
      ]);

    } catch (error) {
      console.error('Failed to update institute usage stats:', error.message);
      throw error;
    }
  }

  /**
   * Check feature flag for institute
   */
  async checkFeatureFlag(featureName, instituteId = null, subscriptionPlan = null) {
    try {
      const result = await query(`
        SELECT 
          is_enabled,
          rollout_percentage,
          target_subscription_plans,
          target_institutes
        FROM platform_feature_flags
        WHERE feature_name = $1
      `, [featureName]);

      if (result.rows.length === 0) {
        return false; // Feature doesn't exist
      }

      const flag = result.rows[0];

      // Check if feature is globally disabled
      if (!flag.is_enabled) {
        return false;
      }

      // Check subscription plan targeting
      if (subscriptionPlan && flag.target_subscription_plans) {
        const targetPlans = flag.target_subscription_plans;
        if (!targetPlans.includes(subscriptionPlan)) {
          return false;
        }
      }

      // Check institute targeting
      if (instituteId && flag.target_institutes) {
        const targetInstitutes = flag.target_institutes;
        if (!targetInstitutes.includes(instituteId)) {
          return false;
        }
      }

      // Check rollout percentage (simple random check)
      if (flag.rollout_percentage < 100) {
        const random = Math.random() * 100;
        if (random > flag.rollout_percentage) {
          return false;
        }
      }

      return true;

    } catch (error) {
      console.error('Failed to check feature flag:', error.message);
      return false;
    }
  }

  /**
   * Get platform revenue analytics
   */
  async getRevenueAnalytics(period = '30d') {
    try {
      let interval;
      switch (period) {
        case '7d':
          interval = '7 days';
          break;
        case '30d':
          interval = '30 days';
          break;
        case '90d':
          interval = '90 days';
          break;
        case '1y':
          interval = '1 year';
          break;
        default:
          interval = '30 days';
      }

      // Revenue calculation based on subscription plans
      const revenueData = await query(`
        SELECT 
          subscription_plan,
          COUNT(*) as institute_count,
          CASE 
            WHEN subscription_plan = 'free' THEN 0
            WHEN subscription_plan = 'basic' THEN COUNT(*) * 29
            WHEN subscription_plan = 'premium' THEN COUNT(*) * 99
            WHEN subscription_plan = 'enterprise' THEN COUNT(*) * 299
            ELSE 0
          END as monthly_revenue
        FROM institutes
        WHERE is_active = TRUE
        AND subscription_status = 'active'
        GROUP BY subscription_plan
        ORDER BY monthly_revenue DESC
      `);

      // Growth trends
      const growthTrends = await query(`
        SELECT 
          DATE(created_at) as date,
          subscription_plan,
          COUNT(*) as new_subscriptions
        FROM institutes
        WHERE created_at >= CURRENT_DATE - INTERVAL '${interval}'
        AND subscription_plan != 'free'
        GROUP BY DATE(created_at), subscription_plan
        ORDER BY date, subscription_plan
      `);

      const totalRevenue = revenueData.rows.reduce((sum, row) => sum + parseInt(row.monthly_revenue), 0);

      return {
        period,
        totalMonthlyRevenue: totalRevenue,
        revenueByPlan: revenueData.rows,
        growthTrends: growthTrends.rows
      };

    } catch (error) {
      console.error('Failed to get revenue analytics:', error.message);
      throw error;
    }
  }

  /**
   * Cleanup expired data
   */
  async cleanupExpiredData() {
    try {
      const results = {
        expiredNotifications: 0,
        oldAnalyticsCache: 0,
        completedMaintenance: 0
      };

      // Clean up expired notifications
      const notificationsResult = await query(`
        DELETE FROM platform_notifications 
        WHERE expires_at < CURRENT_TIMESTAMP
        RETURNING id
      `);
      results.expiredNotifications = notificationsResult.rows.length;

      // Clean up old analytics cache (keep for 1 year)
      const analyticsResult = await query(`
        DELETE FROM platform_analytics_cache 
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year'
        RETURNING id
      `);
      results.oldAnalyticsCache = analyticsResult.rows.length;

      // Clean up completed maintenance windows (keep for 90 days)
      const maintenanceResult = await query(`
        DELETE FROM maintenance_windows 
        WHERE status = 'completed' 
        AND end_time < CURRENT_TIMESTAMP - INTERVAL '90 days'
        RETURNING id
      `);
      results.completedMaintenance = maintenanceResult.rows.length;

      console.log('Cleanup completed:', results);
      return results;

    } catch (error) {
      console.error('Failed to cleanup expired data:', error.message);
      throw error;
    }
  }
}

module.exports = new SuperAdminService();
