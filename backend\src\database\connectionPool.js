const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced PostgreSQL Connection Pool Configuration
 * Provides secure, optimized connection pooling for the LMS SAAS application
 */

class DatabaseConnectionPool {
  constructor() {
    this.pools = new Map();
    this.config = this.loadConfiguration();
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingClients: 0,
      totalQueries: 0,
      errorCount: 0,
      avgQueryTime: 0
    };
    
    this.setupMainPool();
    this.setupMonitoring();
  }

  /**
   * Load database configuration from environment and defaults
   */
  loadConfiguration() {
    return {
      // Main database configuration
      main: {
        user: process.env.DB_USER || 'postgres',
        host: process.env.DB_HOST || 'localhost',
        database: process.env.DB_NAME || 'lte_lms',
        password: process.env.DB_PASSWORD || '1234',
        port: parseInt(process.env.DB_PORT) || 5432,
        
        // Connection pool settings
        max: parseInt(process.env.DB_POOL_MAX) || 20,
        min: parseInt(process.env.DB_POOL_MIN) || 5,
        idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
        connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 10000,
        maxUses: parseInt(process.env.DB_MAX_USES) || 7500,
        
        // SSL configuration
        ssl: process.env.DB_SSL === 'true' ? {
          rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
          ca: process.env.DB_SSL_CA ? fs.readFileSync(process.env.DB_SSL_CA) : undefined,
          cert: process.env.DB_SSL_CERT ? fs.readFileSync(process.env.DB_SSL_CERT) : undefined,
          key: process.env.DB_SSL_KEY ? fs.readFileSync(process.env.DB_SSL_KEY) : undefined
        } : false,
        
        // Query timeout
        query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,
        
        // Statement timeout (PostgreSQL setting)
        statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || 60000,
        
        // Application name for monitoring
        application_name: process.env.DB_APPLICATION_NAME || 'lms-saas-backend'
      },
      
      // Read replica configuration (for future scaling)
      replica: {
        enabled: process.env.DB_REPLICA_ENABLED === 'true',
        host: process.env.DB_REPLICA_HOST,
        port: parseInt(process.env.DB_REPLICA_PORT) || 5432,
        max: parseInt(process.env.DB_REPLICA_POOL_MAX) || 10
      }
    };
  }

  /**
   * Setup main connection pool with optimized settings
   */
  setupMainPool() {
    const config = this.config.main;
    
    // Enhanced pool configuration
    const poolConfig = {
      ...config,
      
      // Connection lifecycle hooks
      onConnect: async (client) => {
        // Set session-level configurations
        await client.query(`SET statement_timeout = ${config.statement_timeout}`);
        await client.query(`SET application_name = '${config.application_name}'`);
        await client.query(`SET timezone = 'UTC'`);
        
        // Enable query logging for slow queries
        if (process.env.NODE_ENV === 'development') {
          await client.query(`SET log_min_duration_statement = 1000`);
        }
        
        this.metrics.totalConnections++;
        console.log(`📊 New database connection established (Total: ${this.metrics.totalConnections})`);
      },
      
      onRemove: (client) => {
        this.metrics.totalConnections--;
        console.log(`📊 Database connection removed (Total: ${this.metrics.totalConnections})`);
      }
    };

    this.mainPool = new Pool(poolConfig);
    this.pools.set('main', this.mainPool);

    // Handle pool errors
    this.mainPool.on('error', (err, client) => {
      console.error('❌ Unexpected error on idle client:', err.message);
      this.metrics.errorCount++;
    });

    // Handle pool connection events
    this.mainPool.on('connect', (client) => {
      this.metrics.activeConnections++;
    });

    this.mainPool.on('acquire', (client) => {
      this.metrics.idleConnections = Math.max(0, this.metrics.idleConnections - 1);
    });

    this.mainPool.on('release', (client) => {
      this.metrics.idleConnections++;
    });

    console.log('✅ Main database connection pool initialized');
    console.log(`📊 Pool configuration: max=${config.max}, min=${config.min}, timeout=${config.connectionTimeoutMillis}ms`);
  }

  /**
   * Setup read replica pool (for future scaling)
   */
  setupReplicaPool() {
    if (!this.config.replica.enabled) {
      return;
    }

    const replicaConfig = {
      ...this.config.main,
      host: this.config.replica.host,
      port: this.config.replica.port,
      max: this.config.replica.max,
      application_name: `${this.config.main.application_name}-replica`
    };

    this.replicaPool = new Pool(replicaConfig);
    this.pools.set('replica', this.replicaPool);

    console.log('✅ Read replica connection pool initialized');
  }

  /**
   * Setup connection pool monitoring
   */
  setupMonitoring() {
    // Monitor pool health every 30 seconds
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
      this.logPoolHealth();
    }, 30000);

    // Log initial pool status
    setTimeout(() => {
      this.logPoolHealth();
    }, 1000);
  }

  /**
   * Update connection pool metrics
   */
  updateMetrics() {
    if (this.mainPool) {
      this.metrics.totalConnections = this.mainPool.totalCount;
      this.metrics.activeConnections = this.mainPool.totalCount - this.mainPool.idleCount;
      this.metrics.idleConnections = this.mainPool.idleCount;
      this.metrics.waitingClients = this.mainPool.waitingCount;
    }
  }

  /**
   * Log pool health information
   */
  logPoolHealth() {
    this.updateMetrics();
    
    const poolUsage = this.mainPool ? 
      ((this.metrics.totalConnections / this.config.main.max) * 100).toFixed(1) : 0;

    console.log('\n📊 Database Pool Health Report:');
    console.log(`🔗 Total Connections: ${this.metrics.totalConnections}/${this.config.main.max} (${poolUsage}%)`);
    console.log(`⚡ Active Connections: ${this.metrics.activeConnections}`);
    console.log(`💤 Idle Connections: ${this.metrics.idleConnections}`);
    console.log(`⏳ Waiting Clients: ${this.metrics.waitingClients}`);
    console.log(`📈 Total Queries: ${this.metrics.totalQueries}`);
    console.log(`❌ Error Count: ${this.metrics.errorCount}`);
    
    if (this.metrics.avgQueryTime > 0) {
      console.log(`⏱️ Avg Query Time: ${this.metrics.avgQueryTime.toFixed(2)}ms`);
    }
  }

  /**
   * Execute query with metrics tracking
   */
  async query(text, params = [], poolName = 'main') {
    const pool = this.pools.get(poolName);
    if (!pool) {
      throw new Error(`Pool '${poolName}' not found`);
    }

    const startTime = Date.now();
    
    try {
      const result = await pool.query(text, params);
      
      // Update metrics
      const queryTime = Date.now() - startTime;
      this.metrics.totalQueries++;
      this.metrics.avgQueryTime = (this.metrics.avgQueryTime * (this.metrics.totalQueries - 1) + queryTime) / this.metrics.totalQueries;
      
      // Log slow queries
      if (queryTime > 1000) {
        console.warn(`🐌 Slow query detected (${queryTime}ms): ${text.substring(0, 100)}...`);
      }
      
      // Log query in development
      if (process.env.NODE_ENV === 'development' && process.env.LOG_QUERIES === 'true') {
        console.log(`📝 Executed query { text: '${text.substring(0, 50)}...', duration: ${queryTime}, rows: ${result.rowCount} }`);
      }
      
      return result;
      
    } catch (error) {
      this.metrics.errorCount++;
      console.error(`❌ Query error (${Date.now() - startTime}ms): ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute transaction with automatic rollback on error
   */
  async transaction(callback, poolName = 'main') {
    const pool = this.pools.get(poolName);
    if (!pool) {
      throw new Error(`Pool '${poolName}' not found`);
    }

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get pool statistics
   */
  getPoolStats() {
    this.updateMetrics();
    
    return {
      main: {
        total: this.metrics.totalConnections,
        active: this.metrics.activeConnections,
        idle: this.metrics.idleConnections,
        waiting: this.metrics.waitingClients,
        max: this.config.main.max,
        usage: ((this.metrics.totalConnections / this.config.main.max) * 100).toFixed(1)
      },
      queries: {
        total: this.metrics.totalQueries,
        avgTime: this.metrics.avgQueryTime,
        errors: this.metrics.errorCount
      }
    };
  }

  /**
   * Test database connectivity
   */
  async testConnection() {
    try {
      const result = await this.query('SELECT NOW() as current_time, version() as db_version');
      console.log('✅ Database connection test successful');
      console.log(`🕐 Server time: ${result.rows[0].current_time}`);
      console.log(`🗄️ Database version: ${result.rows[0].db_version}`);
      return true;
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      return false;
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🛑 Shutting down database connection pools...');
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    const shutdownPromises = Array.from(this.pools.values()).map(pool => pool.end());
    
    try {
      await Promise.all(shutdownPromises);
      console.log('✅ All database connections closed gracefully');
    } catch (error) {
      console.error('❌ Error during database shutdown:', error.message);
    }
  }
}

// Create singleton instance
const connectionPool = new DatabaseConnectionPool();

// Graceful shutdown handling
process.on('SIGINT', async () => {
  await connectionPool.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await connectionPool.shutdown();
  process.exit(0);
});

module.exports = {
  query: (text, params) => connectionPool.query(text, params),
  transaction: (callback) => connectionPool.transaction(callback),
  getPoolStats: () => connectionPool.getPoolStats(),
  testConnection: () => connectionPool.testConnection(),
  shutdown: () => connectionPool.shutdown(),
  connectionPool
};
