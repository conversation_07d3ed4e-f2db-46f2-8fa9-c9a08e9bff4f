const express = require('express');
const profileService = require('../services/profileService');
const { authenticateToken, requireEmailVerification } = require('../middleware/authMiddleware');
const { requireAnyPermission } = require('../middleware/rbacMiddleware');
const { PERMISSIONS } = require('../services/rbacService');
const { responseUtils } = require('../utils/auth');
const yup = require('yup');

const router = express.Router();

// Apply authentication to all profile routes
router.use(authenticateToken);

/**
 * Input validation schemas
 */
const updateProfileSchema = yup.object({
  firstName: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .nullable(),
  lastName: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .nullable(),
  phone: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
    .max(20, 'Phone number must be less than 20 characters')
    .trim()
    .nullable(),
  avatarUrl: yup
    .string()
    .url('Please provide a valid avatar URL')
    .max(500, 'Avatar URL must be less than 500 characters')
    .trim()
    .nullable(),
  studentProfile: yup.object({
    studentId: yup
      .string()
      .min(3, 'Student ID must be at least 3 characters')
      .max(50, 'Student ID must be less than 50 characters')
      .matches(/^[a-zA-Z0-9_-]+$/, 'Student ID can only contain letters, numbers, hyphens, and underscores')
      .trim()
      .nullable(),
    major: yup
      .string()
      .max(100, 'Major must be less than 100 characters')
      .trim()
      .nullable(),
    graduationYear: yup
      .number()
      .integer('Graduation year must be an integer')
      .min(2020, 'Graduation year must be 2020 or later')
      .max(2050, 'Graduation year must be 2050 or earlier')
      .nullable(),
    academicStatus: yup
      .string()
      .oneOf(['active', 'probation', 'suspended', 'graduated', 'withdrawn'], 'Invalid academic status')
      .nullable()
  }).nullable(),
  teacherProfile: yup.object({
    employeeId: yup
      .string()
      .min(3, 'Employee ID must be at least 3 characters')
      .max(50, 'Employee ID must be less than 50 characters')
      .matches(/^[a-zA-Z0-9_-]+$/, 'Employee ID can only contain letters, numbers, hyphens, and underscores')
      .trim()
      .nullable(),
    department: yup
      .string()
      .max(100, 'Department must be less than 100 characters')
      .trim()
      .nullable(),
    specialization: yup
      .string()
      .max(255, 'Specialization must be less than 255 characters')
      .trim()
      .nullable(),
    officeLocation: yup
      .string()
      .max(100, 'Office location must be less than 100 characters')
      .trim()
      .nullable(),
    officeHours: yup
      .string()
      .max(255, 'Office hours must be less than 255 characters')
      .trim()
      .nullable(),
    bio: yup
      .string()
      .max(1000, 'Bio must be less than 1000 characters')
      .trim()
      .nullable(),
    qualifications: yup
      .string()
      .max(1000, 'Qualifications must be less than 1000 characters')
      .trim()
      .nullable()
  }).nullable()
});

const changePasswordSchema = yup.object({
  currentPassword: yup
    .string()
    .min(1, 'Current password is required')
    .max(128, 'Current password is too long')
    .required('Current password is required'),
  newPassword: yup
    .string()
    .min(8, 'New password must be at least 8 characters long')
    .max(128, 'New password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/,
      'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('New password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Passwords must match')
    .required('Password confirmation is required')
});

/**
 * @route GET /api/profile
 * @desc Get current user's profile
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    const profile = await profileService.getUserProfile(
      req.user.id,
      req.user.id,
      req.user.role
    );

    res.json({
      success: true,
      profile
    });
  } catch (error) {
    console.error('Get profile error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get profile', 'PROFILE_GET_ERROR')
    );
  }
});

/**
 * @route GET /api/profile/:userId
 * @desc Get user profile by ID (admin access)
 * @access Private (Admin)
 */
router.get('/:userId',
  requireAnyPermission([PERMISSIONS.USER_READ]),
  async (req, res) => {
    try {
      const { userId } = req.params;

      const profile = await profileService.getUserProfile(
        userId,
        req.user.id,
        req.user.role
      );

      res.json({
        success: true,
        profile
      });
    } catch (error) {
      console.error('Get user profile error:', error.message);

      if (error.message.includes('Access denied')) {
        return res.status(403).json(
          responseUtils.createErrorResponse(error.message, 'ACCESS_DENIED')
        );
      }

      if (error.message.includes('not found')) {
        return res.status(404).json(
          responseUtils.createErrorResponse(error.message, 'USER_NOT_FOUND')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get user profile', 'PROFILE_GET_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/profile
 * @desc Update current user's profile
 * @access Private
 */
router.put('/',
  requireEmailVerification,
  async (req, res) => {
    try {
      // Validate input
      const validatedData = await updateProfileSchema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      const updatedProfile = await profileService.updateUserProfile(
        req.user.id,
        validatedData,
        req.user.id,
        req.user.role
      );

      res.json({
        success: true,
        message: 'Profile updated successfully',
        profile: {
          id: updatedProfile.id,
          email: updatedProfile.email,
          firstName: updatedProfile.first_name,
          lastName: updatedProfile.last_name,
          phone: updatedProfile.phone,
          avatarUrl: updatedProfile.avatar_url,
          role: updatedProfile.role
        }
      });
    } catch (error) {
      console.error('Update profile error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      if (error.message.includes('Access denied')) {
        return res.status(403).json(
          responseUtils.createErrorResponse(error.message, 'ACCESS_DENIED')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update profile', 'PROFILE_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/profile/:userId
 * @desc Update user profile by ID (admin access)
 * @access Private (Admin)
 */
router.put('/:userId',
  requireAnyPermission([PERMISSIONS.USER_UPDATE]),
  async (req, res) => {
    try {
      const { userId } = req.params;

      // Validate input
      const validatedData = await updateProfileSchema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      const updatedProfile = await profileService.updateUserProfile(
        userId,
        validatedData,
        req.user.id,
        req.user.role
      );

      res.json({
        success: true,
        message: 'User profile updated successfully',
        profile: {
          id: updatedProfile.id,
          email: updatedProfile.email,
          firstName: updatedProfile.first_name,
          lastName: updatedProfile.last_name,
          phone: updatedProfile.phone,
          avatarUrl: updatedProfile.avatar_url,
          role: updatedProfile.role
        }
      });
    } catch (error) {
      console.error('Update user profile error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      if (error.message.includes('Access denied')) {
        return res.status(403).json(
          responseUtils.createErrorResponse(error.message, 'ACCESS_DENIED')
        );
      }

      if (error.message.includes('not found')) {
        return res.status(404).json(
          responseUtils.createErrorResponse(error.message, 'USER_NOT_FOUND')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update user profile', 'PROFILE_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/profile/change-password
 * @desc Change user password
 * @access Private
 */
router.post('/change-password',
  requireEmailVerification,
  async (req, res) => {
    try {
      // Validate input
      const validatedData = await changePasswordSchema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      const { currentPassword, newPassword } = validatedData;

      const result = await profileService.changePassword(
        req.user.id,
        currentPassword,
        newPassword
      );

      res.json(result);
    } catch (error) {
      console.error('Change password error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      if (error.message.includes('incorrect')) {
        return res.status(400).json(
          responseUtils.createErrorResponse(error.message, 'INCORRECT_PASSWORD')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to change password', 'PASSWORD_CHANGE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/profile/activity
 * @desc Get user activity summary
 * @access Private
 */
router.get('/activity', async (req, res) => {
  try {
    const activity = await profileService.getUserActivitySummary(req.user.id);

    res.json({
      success: true,
      activity
    });
  } catch (error) {
    console.error('Get activity error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get activity', 'ACTIVITY_GET_ERROR')
    );
  }
});

/**
 * @route POST /api/profile/deactivate
 * @desc Deactivate user account
 * @access Private
 */
router.post('/deactivate', async (req, res) => {
  try {
    const result = await profileService.deactivateAccount(
      req.user.id,
      req.user.id,
      req.user.role
    );

    res.json(result);
  } catch (error) {
    console.error('Deactivate account error:', error.message);

    if (error.message.includes('Access denied')) {
      return res.status(403).json(
        responseUtils.createErrorResponse(error.message, 'ACCESS_DENIED')
      );
    }

    res.status(500).json(
      responseUtils.createErrorResponse('Failed to deactivate account', 'DEACTIVATE_ERROR')
    );
  }
});

module.exports = router;
