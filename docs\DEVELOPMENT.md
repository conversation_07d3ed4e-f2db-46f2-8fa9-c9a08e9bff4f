# Development Guide

This guide covers the development workflow, coding standards, and best practices for the LMS SAAS platform.

## 🚀 Quick Start

### Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- PostgreSQL 12 or higher
- Git

### Setup

1. **Clone and setup the project:**
   ```bash
   git clone <repository-url>
   cd lms-saas-platform
   
   # Run setup script (Linux/Mac)
   chmod +x scripts/setup-dev.sh
   ./scripts/setup-dev.sh
   
   # Or on Windows
   scripts\setup-dev.bat
   ```

2. **Start development servers:**
   ```bash
   npm run dev
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5010
   - API Documentation: http://localhost:5010/api-docs

## 📁 Project Structure

```
lms-saas-platform/
├── frontend/                 # Next.js frontend
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # Reusable components
│   │   ├── hooks/           # Custom hooks
│   │   ├── lib/             # Utilities
│   │   ├── store/           # State management
│   │   ├── styles/          # Global styles
│   │   └── types/           # TypeScript types
│   └── public/              # Static assets
├── backend/                  # Express.js backend
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Database models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   └── utils/           # Utilities
│   ├── migrations/          # Database migrations
│   └── seeds/               # Database seeds
├── docs/                     # Documentation
├── scripts/                  # Development scripts
└── .github/                  # GitHub workflows
```

## 🛠️ Development Workflow

### 1. Feature Development

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes following our coding standards**

3. **Run tests and checks:**
   ```bash
   npm run lint
   npm run type-check
   npm run test
   ```

4. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push and create a pull request:**
   ```bash
   git push origin feature/your-feature-name
   ```

### 2. Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

Examples:
```
feat: add user authentication system
fix: resolve database connection issue
docs: update API documentation
refactor: optimize database queries
test: add unit tests for user service
```

### 3. Code Review Process

1. All changes must be submitted via pull requests
2. At least one approval required before merging
3. All CI checks must pass
4. Code must follow our style guidelines
5. Tests must be included for new features

## 📝 Coding Standards

### TypeScript

- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper type annotations
- Avoid `any` type when possible

```typescript
// Good
interface User {
  id: string
  email: string
  role: UserRole
}

const createUser = (userData: Partial<User>): Promise<User> => {
  // Implementation
}

// Avoid
const createUser = (userData: any): any => {
  // Implementation
}
```

### React/Next.js

- Use functional components with hooks
- Implement proper error boundaries
- Use TypeScript for all components
- Follow the component structure:

```typescript
interface ComponentProps {
  // Props interface
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // Hooks
  const [state, setState] = useState()
  
  // Event handlers
  const handleClick = () => {
    // Implementation
  }
  
  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}
```

### Backend/Express

- Use async/await for asynchronous operations
- Implement proper error handling
- Use middleware for common functionality
- Follow the controller pattern:

```typescript
export const createUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body
    const user = await userService.createUser(userData)
    res.status(201).json({ success: true, data: user })
  } catch (error) {
    next(error)
  }
}
```

### Database

- Use migrations for schema changes
- Write seeds for test data
- Use transactions for multi-step operations
- Implement proper indexing

## 🧪 Testing

### Frontend Testing

```bash
# Run all frontend tests
npm run test:frontend

# Run tests in watch mode
npm run test:frontend -- --watch

# Run tests with coverage
npm run test:frontend -- --coverage
```

### Backend Testing

```bash
# Run all backend tests
npm run test:backend

# Run specific test file
npm run test:backend -- user.test.ts

# Run tests with coverage
npm run test:backend -- --coverage
```

### Test Structure

```typescript
describe('UserService', () => {
  beforeEach(() => {
    // Setup
  })
  
  afterEach(() => {
    // Cleanup
  })
  
  describe('createUser', () => {
    it('should create a user successfully', async () => {
      // Arrange
      const userData = { email: '<EMAIL>' }
      
      // Act
      const result = await userService.createUser(userData)
      
      // Assert
      expect(result).toBeDefined()
      expect(result.email).toBe(userData.email)
    })
    
    it('should throw error for invalid email', async () => {
      // Test error cases
    })
  })
})
```

## 🔧 Available Scripts

### Root Level

```bash
npm run dev              # Start both frontend and backend
npm run build            # Build both applications
npm run start            # Start both in production
npm run lint             # Lint all code
npm run lint:fix         # Fix linting issues
npm run format           # Format code with Prettier
npm run type-check       # Run TypeScript checks
npm run test             # Run all tests
npm run clean            # Clean build artifacts
```

### Frontend Specific

```bash
npm run dev:frontend     # Start frontend dev server
npm run build:frontend   # Build frontend
npm run start:frontend   # Start frontend in production
npm run lint:frontend    # Lint frontend code
npm run test:frontend    # Run frontend tests
```

### Backend Specific

```bash
npm run dev:backend      # Start backend dev server
npm run build:backend    # Build backend
npm run start:backend    # Start backend in production
npm run lint:backend     # Lint backend code
npm run test:backend     # Run backend tests
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database
npm run db:reset         # Reset database
```

## 🐛 Debugging

### Frontend Debugging

1. **Browser DevTools:**
   - Use React Developer Tools
   - Check Network tab for API calls
   - Use Console for logging

2. **VS Code Debugging:**
   - Set breakpoints in your code
   - Use the built-in debugger
   - Configure launch.json for debugging

### Backend Debugging

1. **Console Logging:**
   ```typescript
   console.log('Debug info:', data)
   ```

2. **VS Code Debugging:**
   - Set breakpoints
   - Use the Node.js debugger
   - Debug with nodemon

3. **Database Debugging:**
   ```bash
   # Enable SQL logging
   DEBUG_SQL=true npm run dev:backend
   ```

## 🔍 Performance Monitoring

### Frontend Performance

- Use Next.js built-in performance metrics
- Monitor Core Web Vitals
- Use React Profiler for component performance

### Backend Performance

- Monitor API response times
- Use database query analysis
- Implement proper caching strategies

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Express.js Documentation](https://expressjs.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [React Documentation](https://react.dev/)

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Kill process on port 3000
   npx kill-port 3000
   
   # Kill process on port 5010
   npx kill-port 5010
   ```

2. **Database connection issues:**
   - Check PostgreSQL is running
   - Verify connection credentials in .env
   - Check database exists

3. **Module not found errors:**
   ```bash
   # Clear node_modules and reinstall
   npm run clean
   npm run install:all
   ```

4. **TypeScript errors:**
   ```bash
   # Run type checking
   npm run type-check
   
   # Clear TypeScript cache
   rm -rf node_modules/.cache
   ```

For more help, check the [FAQ](./FAQ.md) or create an issue in the repository.
