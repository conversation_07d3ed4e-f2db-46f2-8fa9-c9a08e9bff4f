'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  Shield, 
  ArrowLeft, 
  Home, 
  Mail,
  AlertTriangle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { usePermissions } from '@/components/auth/ProtectedRoute'

export default function UnauthorizedPage() {
  const router = useRouter()
  const { user, isStudent, isTeacher, isAdmin } = usePermissions()

  const getRedirectPath = () => {
    if (!user) return '/auth/login'
    
    if (isStudent()) return '/student/dashboard'
    if (isTeacher()) return '/teacher/dashboard'
    if (isAdmin()) return '/admin/dashboard'
    
    return '/dashboard'
  }

  const getRoleBasedMessage = () => {
    if (!user) {
      return 'Please log in to access this resource.'
    }

    const roleMessages = {
      student: 'This page is only accessible to teachers and administrators.',
      teacher: 'This page is only accessible to administrators.',
      institute_admin: 'This page is only accessible to super administrators.',
      super_admin: 'You have full access. This error should not occur.',
    }

    return roleMessages[user.role as keyof typeof roleMessages] || 'You do not have permission to access this page.'
  }

  const getSuggestedActions = () => {
    if (!user) {
      return [
        {
          label: 'Log In',
          href: '/auth/login',
          icon: Shield,
          primary: true,
        },
        {
          label: 'Register',
          href: '/auth/register',
          icon: Mail,
          primary: false,
        },
      ]
    }

    const actions = [
      {
        label: 'Go to Dashboard',
        href: getRedirectPath(),
        icon: Home,
        primary: true,
      },
      {
        label: 'Go Back',
        onClick: () => router.back(),
        icon: ArrowLeft,
        primary: false,
      },
    ]

    // Add role-specific suggestions
    if (isStudent()) {
      actions.push({
        label: 'Browse Courses',
        href: '/courses',
        icon: Shield,
        primary: false,
      })
    }

    if (isTeacher()) {
      actions.push({
        label: 'My Courses',
        href: '/teacher/courses',
        icon: Shield,
        primary: false,
      })
    }

    return actions
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Icon */}
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-red-100 rounded-full">
            <AlertTriangle className="h-12 w-12 text-red-600" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-center text-3xl font-bold text-gray-900 mb-2">
          Access Denied
        </h1>

        {/* Subtitle */}
        <p className="text-center text-lg text-gray-600 mb-8">
          You don't have permission to view this page
        </p>

        {/* User Info */}
        {user && (
          <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {user.firstName[0]}{user.lastName[0]}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-xs text-gray-600 capitalize">
                  {user.role.replace('_', ' ')}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Message */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <Shield className="h-5 w-5 text-red-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-red-800 mb-1">
                Insufficient Permissions
              </h3>
              <p className="text-sm text-red-700">
                {getRoleBasedMessage()}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          {getSuggestedActions().map((action, index) => {
            const Icon = action.icon
            
            if (action.onClick) {
              return (
                <Button
                  key={index}
                  onClick={action.onClick}
                  variant={action.primary ? 'default' : 'outline'}
                  className="w-full"
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {action.label}
                </Button>
              )
            }

            return (
              <Link key={index} href={action.href!}>
                <Button
                  variant={action.primary ? 'default' : 'outline'}
                  className="w-full"
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {action.label}
                </Button>
              </Link>
            )
          })}
        </div>

        {/* Help Section */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex">
            <Mail className="h-5 w-5 text-blue-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-800 mb-1">
                Need Access?
              </h3>
              <p className="text-sm text-blue-700 mb-2">
                If you believe you should have access to this page, please contact your administrator.
              </p>
              <ul className="text-xs text-blue-600 space-y-1">
                <li>• Check if you're logged in with the correct account</li>
                <li>• Verify your role and permissions with your administrator</li>
                <li>• Contact support if you continue having issues</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Links */}
        <div className="mt-6 text-center space-x-4">
          <Link 
            href="/support" 
            className="text-sm text-blue-600 hover:underline"
          >
            Contact Support
          </Link>
          <span className="text-gray-300">•</span>
          <Link 
            href="/help" 
            className="text-sm text-blue-600 hover:underline"
          >
            Help Center
          </Link>
        </div>
      </div>
    </div>
  )
}
