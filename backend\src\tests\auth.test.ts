import request from 'supertest'
import jwt from 'jsonwebtoken'
import app from '../server'
import { UserRole, Permission } from '../types/auth'
import { generateAccessToken } from '../utils/jwt'

// Mock user data for testing
const mockUsers = {
  superAdmin: {
    id: 'super-admin-1',
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'Admin',
    role: UserRole.SUPER_ADMIN,
    isActive: true,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  instituteAdmin: {
    id: 'institute-admin-1',
    email: '<EMAIL>',
    firstName: 'Institute',
    lastName: 'Admin',
    role: UserRole.INSTITUTE_ADMIN,
    instituteId: 'inst-123',
    isActive: true,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  teacher: {
    id: 'teacher-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Teacher',
    role: UserRole.TEACHER,
    instituteId: 'inst-123',
    isActive: true,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  student: {
    id: 'student-1',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Student',
    role: UserRole.STUDENT,
    instituteId: 'inst-123',
    isActive: true,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
}

// Helper function to generate tokens for testing
const generateTestToken = (user: any) => {
  return generateAccessToken(user)
}

describe('Authentication & Authorization', () => {
  beforeAll(() => {
    // Set up test environment variables
    process.env.JWT_SECRET = 'test-secret-key'
    process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key'
  })

  describe('Authentication Middleware', () => {
    it('should reject requests without token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.code).toBe('UNAUTHORIZED')
    })

    it('should reject requests with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.code).toBe('INVALID_TOKEN')
    })

    it('should accept requests with valid token', async () => {
      const token = generateTestToken(mockUsers.student)
      
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.user.role).toBe(UserRole.STUDENT)
    })

    it('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: 'test', email: '<EMAIL>', role: UserRole.STUDENT },
        process.env.JWT_SECRET!,
        { expiresIn: '-1h' }
      )

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401)

      expect(response.body.code).toBe('TOKEN_EXPIRED')
    })
  })

  describe('Role-Based Access Control', () => {
    describe('Super Admin Access', () => {
      it('should allow super admin to access platform stats', async () => {
        const token = generateTestToken(mockUsers.superAdmin)
        
        const response = await request(app)
          .get('/api/admin/platform/stats')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toHaveProperty('totalInstitutes')
      })

      it('should allow super admin to view all institutes', async () => {
        const token = generateTestToken(mockUsers.superAdmin)
        
        const response = await request(app)
          .get('/api/admin/institutes')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toBeInstanceOf(Array)
      })

      it('should allow super admin to create institutes', async () => {
        const token = generateTestToken(mockUsers.superAdmin)
        
        const newInstitute = {
          name: 'Test University',
          email: '<EMAIL>',
          website: 'https://test.edu',
          subscriptionPlan: 'premium'
        }

        const response = await request(app)
          .post('/api/admin/institutes')
          .set('Authorization', `Bearer ${token}`)
          .send(newInstitute)
          .expect(201)

        expect(response.body.success).toBe(true)
        expect(response.body.data.name).toBe(newInstitute.name)
      })
    })

    describe('Institute Admin Access', () => {
      it('should allow institute admin to access dashboard', async () => {
        const token = generateTestToken(mockUsers.instituteAdmin)
        
        const response = await request(app)
          .get('/api/admin/institute/dashboard')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toHaveProperty('institute')
      })

      it('should allow institute admin to view institute users', async () => {
        const token = generateTestToken(mockUsers.instituteAdmin)
        
        const response = await request(app)
          .get('/api/admin/institute/users')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toBeInstanceOf(Array)
      })

      it('should allow institute admin to manage settings', async () => {
        const token = generateTestToken(mockUsers.instituteAdmin)
        
        const response = await request(app)
          .get('/api/admin/institute/settings')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toHaveProperty('general')
      })

      it('should deny institute admin access to platform stats', async () => {
        const token = generateTestToken(mockUsers.instituteAdmin)
        
        const response = await request(app)
          .get('/api/admin/platform/stats')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })
    })

    describe('Teacher Access', () => {
      it('should allow teacher to view courses', async () => {
        const token = generateTestToken(mockUsers.teacher)
        
        const response = await request(app)
          .get('/api/admin/courses')
          .set('Authorization', `Bearer ${token}`)
          .expect(200)

        expect(response.body.success).toBe(true)
        expect(response.body.data).toBeInstanceOf(Array)
      })

      it('should deny teacher access to institute dashboard', async () => {
        const token = generateTestToken(mockUsers.teacher)
        
        const response = await request(app)
          .get('/api/admin/institute/dashboard')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })

      it('should deny teacher access to platform stats', async () => {
        const token = generateTestToken(mockUsers.teacher)
        
        const response = await request(app)
          .get('/api/admin/platform/stats')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })
    })

    describe('Student Access', () => {
      it('should deny student access to admin courses', async () => {
        const token = generateTestToken(mockUsers.student)
        
        const response = await request(app)
          .get('/api/admin/courses')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })

      it('should deny student access to institute dashboard', async () => {
        const token = generateTestToken(mockUsers.student)
        
        const response = await request(app)
          .get('/api/admin/institute/dashboard')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })

      it('should deny student access to platform stats', async () => {
        const token = generateTestToken(mockUsers.student)
        
        const response = await request(app)
          .get('/api/admin/platform/stats')
          .set('Authorization', `Bearer ${token}`)
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.code).toBe('FORBIDDEN')
      })
    })
  })

  describe('Cross-Institute Access Control', () => {
    it('should deny access to different institute resources', async () => {
      const differentInstituteAdmin = {
        ...mockUsers.instituteAdmin,
        instituteId: 'different-institute'
      }
      const token = generateTestToken(differentInstituteAdmin)
      
      const response = await request(app)
        .get('/api/admin/institute/users')
        .set('Authorization', `Bearer ${token}`)
        .query({ instituteId: 'inst-123' })
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.code).toBe('FORBIDDEN')
    })

    it('should allow super admin to access any institute', async () => {
      const token = generateTestToken(mockUsers.superAdmin)
      
      const response = await request(app)
        .get('/api/admin/institutes')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)

      expect(response.body.success).toBe(true)
    })
  })

  describe('Permission-Based Access Control', () => {
    it('should enforce permission requirements', async () => {
      const token = generateTestToken(mockUsers.instituteAdmin)
      
      const response = await request(app)
        .put('/api/admin/institute/users/user-123/role')
        .set('Authorization', `Bearer ${token}`)
        .send({ role: UserRole.TEACHER })
        .expect(200)

      expect(response.body.success).toBe(true)
    })

    it('should deny permission escalation', async () => {
      const token = generateTestToken(mockUsers.instituteAdmin)
      
      const response = await request(app)
        .put('/api/admin/institute/users/user-123/role')
        .set('Authorization', `Bearer ${token}`)
        .send({ role: UserRole.SUPER_ADMIN })
        .expect(403)

      expect(response.body.success).toBe(false)
    })
  })
})
