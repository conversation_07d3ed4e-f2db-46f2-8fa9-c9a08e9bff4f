const profileService = require('../services/profileService');
const authService = require('../services/authService');
const { query } = require('../database/connection');

/**
 * Profile Management Test Suite
 * Tests student and teacher registration, profile management, and domain validation
 */

class ProfileTester {
  constructor() {
    this.testResults = [];
    this.testData = {
      testInstitute: null,
      testUsers: {},
      testProfiles: {}
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test student profile creation and management
   */
  async testStudentProfileManagement() {
    console.log('\n👨‍🎓 Testing Student Profile Management...');

    try {
      // Create test institute and student
      await this.createTestData();

      if (!this.testData.testUsers.student) {
        this.logResult('Student Profile Management', false, 'No test student available');
        return;
      }

      const studentId = this.testData.testUsers.student.id;

      // Test getting initial profile
      const initialProfile = await profileService.getUserProfile(studentId, studentId, 'student');
      
      this.logResult(
        'Get Initial Student Profile',
        initialProfile && initialProfile.email === this.testData.testUsers.student.email,
        `Profile retrieved for ${initialProfile.email}`
      );

      // Test updating student profile
      const studentProfileData = {
        firstName: 'Updated',
        lastName: 'Student',
        phone: '+1234567890',
        studentProfile: {
          studentId: 'STU001',
          major: 'Computer Science',
          graduationYear: 2025,
          academicStatus: 'active'
        }
      };

      const updatedProfile = await profileService.updateUserProfile(
        studentId,
        studentProfileData,
        studentId,
        'student'
      );

      this.logResult(
        'Update Student Profile',
        updatedProfile.first_name === 'Updated' && updatedProfile.last_name === 'Student',
        `Profile updated: ${updatedProfile.first_name} ${updatedProfile.last_name}`
      );

      // Test getting updated profile with student data
      const fullProfile = await profileService.getUserProfile(studentId, studentId, 'student');
      
      this.logResult(
        'Get Full Student Profile',
        fullProfile.studentProfile && fullProfile.studentProfile.major === 'Computer Science',
        `Student profile includes major: ${fullProfile.studentProfile?.major}`
      );

      this.testData.testProfiles.student = fullProfile;

    } catch (error) {
      this.logResult('Student Profile Management', false, error.message);
    }
  }

  /**
   * Test teacher profile creation and management
   */
  async testTeacherProfileManagement() {
    console.log('\n👨‍🏫 Testing Teacher Profile Management...');

    try {
      if (!this.testData.testUsers.teacher) {
        this.logResult('Teacher Profile Management', false, 'No test teacher available');
        return;
      }

      const teacherId = this.testData.testUsers.teacher.id;

      // Test updating teacher profile
      const teacherProfileData = {
        firstName: 'Updated',
        lastName: 'Teacher',
        phone: '+1987654321',
        teacherProfile: {
          employeeId: 'EMP001',
          department: 'Computer Science',
          specialization: 'Machine Learning',
          officeLocation: 'Room 101',
          officeHours: 'Mon-Fri 2-4 PM',
          bio: 'Experienced computer science professor',
          qualifications: 'PhD in Computer Science'
        }
      };

      const updatedProfile = await profileService.updateUserProfile(
        teacherId,
        teacherProfileData,
        teacherId,
        'teacher'
      );

      this.logResult(
        'Update Teacher Profile',
        updatedProfile.first_name === 'Updated' && updatedProfile.last_name === 'Teacher',
        `Profile updated: ${updatedProfile.first_name} ${updatedProfile.last_name}`
      );

      // Test getting full teacher profile
      const fullProfile = await profileService.getUserProfile(teacherId, teacherId, 'teacher');
      
      this.logResult(
        'Get Full Teacher Profile',
        fullProfile.teacherProfile && fullProfile.teacherProfile.department === 'Computer Science',
        `Teacher profile includes department: ${fullProfile.teacherProfile?.department}`
      );

      this.testData.testProfiles.teacher = fullProfile;

    } catch (error) {
      this.logResult('Teacher Profile Management', false, error.message);
    }
  }

  /**
   * Test password change functionality
   */
  async testPasswordChange() {
    console.log('\n🔒 Testing Password Change...');

    try {
      if (!this.testData.testUsers.student) {
        this.logResult('Password Change', false, 'No test user available');
        return;
      }

      const userId = this.testData.testUsers.student.id;
      const currentPassword = 'TestPassword123!';
      const newPassword = 'NewTestPassword123!';

      // Test password change
      const result = await profileService.changePassword(userId, currentPassword, newPassword);

      this.logResult(
        'Change Password',
        result.success === true,
        result.message
      );

      // Test login with new password
      const mockReq = {
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test-Agent' : null,
        tenant: { instituteId: this.testData.testInstitute.id }
      };

      const loginResult = await authService.loginUser(
        this.testData.testUsers.student.email,
        newPassword,
        mockReq
      );

      this.logResult(
        'Login with New Password',
        loginResult.user && loginResult.accessToken,
        'Login successful with new password'
      );

    } catch (error) {
      this.logResult('Password Change', false, error.message);
    }
  }

  /**
   * Test profile access control
   */
  async testProfileAccessControl() {
    console.log('\n🛡️ Testing Profile Access Control...');

    try {
      if (!this.testData.testUsers.student || !this.testData.testUsers.teacher) {
        this.logResult('Profile Access Control', false, 'Insufficient test users');
        return;
      }

      const studentId = this.testData.testUsers.student.id;
      const teacherId = this.testData.testUsers.teacher.id;

      // Test student accessing own profile
      try {
        const ownProfile = await profileService.getUserProfile(studentId, studentId, 'student');
        this.logResult(
          'Student Access Own Profile',
          ownProfile && ownProfile.id === studentId,
          'Student can access own profile'
        );
      } catch (error) {
        this.logResult('Student Access Own Profile', false, error.message);
      }

      // Test student accessing other's profile (should fail)
      try {
        await profileService.getUserProfile(teacherId, studentId, 'student');
        this.logResult('Student Access Other Profile', false, 'Should have been denied');
      } catch (error) {
        this.logResult(
          'Student Access Other Profile',
          error.message.includes('Access denied'),
          'Access correctly denied'
        );
      }

      // Test admin accessing any profile
      try {
        const adminProfile = await profileService.getUserProfile(
          studentId,
          this.testData.testUsers.admin.id,
          'institute_admin'
        );
        this.logResult(
          'Admin Access Any Profile',
          adminProfile && adminProfile.id === studentId,
          'Admin can access any profile'
        );
      } catch (error) {
        this.logResult('Admin Access Any Profile', false, error.message);
      }

    } catch (error) {
      this.logResult('Profile Access Control', false, error.message);
    }
  }

  /**
   * Test user activity tracking
   */
  async testUserActivityTracking() {
    console.log('\n📊 Testing User Activity Tracking...');

    try {
      if (!this.testData.testUsers.student) {
        this.logResult('User Activity Tracking', false, 'No test user available');
        return;
      }

      const userId = this.testData.testUsers.student.id;

      // Get activity summary
      const activity = await profileService.getUserActivitySummary(userId);

      this.logResult(
        'Get Activity Summary',
        activity && typeof activity.total_sessions !== 'undefined',
        `Total sessions: ${activity.total_sessions}, Recent sessions: ${activity.recent_sessions}`
      );

    } catch (error) {
      this.logResult('User Activity Tracking', false, error.message);
    }
  }

  /**
   * Test account deactivation
   */
  async testAccountDeactivation() {
    console.log('\n🚫 Testing Account Deactivation...');

    try {
      // Create a separate test user for deactivation
      const testUserData = {
        email: `deactivate${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Deactivate',
        instituteId: this.testData.testInstitute.id
      };

      const userResult = await authService.registerUser(testUserData, 'student');
      const testUserId = userResult.user.id;

      // Test account deactivation
      const deactivationResult = await profileService.deactivateAccount(
        testUserId,
        testUserId,
        'student'
      );

      this.logResult(
        'Account Deactivation',
        deactivationResult.success === true,
        deactivationResult.message
      );

      // Verify user is deactivated
      const userCheck = await query('SELECT is_active FROM users WHERE id = $1', [testUserId]);
      const isActive = userCheck.rows[0]?.is_active;

      this.logResult(
        'Verify Deactivation',
        isActive === false,
        `User active status: ${isActive}`
      );

      // Clean up test user
      await query('DELETE FROM users WHERE id = $1', [testUserId]);

    } catch (error) {
      this.logResult('Account Deactivation', false, error.message);
    }
  }

  /**
   * Test avatar update functionality
   */
  async testAvatarUpdate() {
    console.log('\n🖼️ Testing Avatar Update...');

    try {
      if (!this.testData.testUsers.student) {
        this.logResult('Avatar Update', false, 'No test user available');
        return;
      }

      const userId = this.testData.testUsers.student.id;
      const avatarUrl = 'https://example.com/avatar.jpg';

      // Test avatar update
      const result = await profileService.updateAvatar(userId, avatarUrl);

      this.logResult(
        'Update Avatar',
        result.success === true && result.avatarUrl === avatarUrl,
        `Avatar URL: ${result.avatarUrl}`
      );

    } catch (error) {
      this.logResult('Avatar Update', false, error.message);
    }
  }

  /**
   * Create test data
   */
  async createTestData() {
    try {
      // Create test institute
      const instituteData = {
        email: `admin${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Admin',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `contact${Date.now()}@testinstitute.edu`,
        instituteAddress: '123 Test Street'
      };

      const instituteResult = await authService.registerInstituteAdmin(instituteData);
      this.testData.testInstitute = instituteResult.institute;
      this.testData.testUsers.admin = instituteResult.user;

      // Create test student
      const studentData = {
        email: `student${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Student',
        instituteId: this.testData.testInstitute.id
      };

      const studentResult = await authService.registerUser(studentData, 'student');
      this.testData.testUsers.student = studentResult.user;

      // Create test teacher
      const teacherData = {
        email: `teacher${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Teacher',
        instituteId: this.testData.testInstitute.id
      };

      const teacherResult = await authService.registerUser(teacherData, 'teacher');
      this.testData.testUsers.teacher = teacherResult.user;

      console.log('✅ Test data created successfully');

    } catch (error) {
      console.error('❌ Test data creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test profiles
      for (const [role, user] of Object.entries(this.testData.testUsers)) {
        if (user?.id) {
          await query('DELETE FROM student_profiles WHERE user_id = $1', [user.id]);
          await query('DELETE FROM teacher_profiles WHERE user_id = $1', [user.id]);
          await query('DELETE FROM email_verifications WHERE user_id = $1', [user.id]);
          await query('DELETE FROM user_sessions WHERE user_id = $1', [user.id]);
          await query('DELETE FROM users WHERE id = $1', [user.id]);
        }
      }

      // Delete test institute
      if (this.testData.testInstitute?.id) {
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.testInstitute.id]);
      }

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all profile management tests
   */
  async runAllTests() {
    console.log('🚀 Starting Profile Management Tests...\n');

    await this.testStudentProfileManagement();
    await this.testTeacherProfileManagement();
    await this.testPasswordChange();
    await this.testProfileAccessControl();
    await this.testUserActivityTracking();
    await this.testAccountDeactivation();
    await this.testAvatarUpdate();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new ProfileTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Profile management testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { ProfileTester };
