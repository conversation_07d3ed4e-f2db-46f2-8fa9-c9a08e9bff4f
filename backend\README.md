# LMS SAAS Backend API

A comprehensive Learning Management System (LMS) backend built with Node.js, Express, and PostgreSQL, designed for multi-tenant SAAS architecture.

## 🚀 Features

- **Multi-tenant Architecture**: Complete data isolation between institutes
- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Custom Domain Support**: Each institute can use their own domain
- **Secure by Design**: Input validation, rate limiting, SQL injection prevention
- **PostgreSQL Database**: Robust relational database with optimized indexes
- **RESTful API**: Clean, documented API endpoints

## 🛠️ Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL
- **Authentication**: JWT + bcrypt
- **Validation**: Yup
- **Security**: Helmet, CORS, Rate Limiting
- **Environment**: dotenv

## 📋 Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## 🔧 Installation

1. **Clone the repository**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Set up PostgreSQL database**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE lte_lms;
   CREATE USER postgres WITH PASSWORD '1234';
   GRANT ALL PRIVILEGES ON DATABASE lte_lms TO postgres;
   ```

5. **Run database migrations**
   ```bash
   npm run migrate
   ```

6. **Seed the database (optional)**
   ```bash
   npm run seed
   ```

## 🚀 Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on `http://localhost:5000`

## 📊 Database Schema

### Core Tables

1. **institutes** - Multi-tenant institute management
2. **users** - All user types (super_admin, institute_admin, teacher, student)
3. **user_roles** - Role-based access control
4. **custom_domains** - Custom domain management per institute
5. **email_verifications** - Email verification and password reset tokens
6. **user_sessions** - Session management and tracking
7. **institute_settings** - Institute-specific configurations

### Multi-Tenancy

- Each institute has complete data isolation
- Users belong to specific institutes (except super admins)
- All queries are automatically scoped by institute_id
- Custom domains map to specific institutes

## 🔐 Authentication & Authorization

### User Roles

- **super_admin**: Platform-wide access, manages all institutes
- **institute_admin**: Institute-level access, manages institute settings
- **teacher**: Course creation and student management
- **student**: Course access and learning activities

### JWT Token Structure
```json
{
  "userId": "uuid",
  "email": "<EMAIL>",
  "role": "student",
  "instituteId": "uuid",
  "iat": **********,
  "exp": **********
}
```

## 🌐 API Endpoints

### Health Checks
- `GET /health` - Server health status
- `GET /health/db` - Database connection status

### Authentication (Coming Soon)
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Password reset confirmation

### Institutes (Coming Soon)
- `GET /api/institutes` - List institutes (super admin only)
- `POST /api/institutes` - Create new institute
- `GET /api/institutes/:id` - Get institute details
- `PUT /api/institutes/:id` - Update institute
- `DELETE /api/institutes/:id` - Delete institute

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📝 Sample Data

After running the seed script, you'll have:

- **Super Admin**: <EMAIL> (password: admin123)
- **Institute**: ABC Engineering College
- **Institute Admin**: <EMAIL> (password: admin123)
- **Teacher**: <EMAIL> (password: teacher123)
- **Student**: <EMAIL> (password: student123)

## 🔒 Security Features

- **Password Hashing**: bcrypt with 12 rounds
- **JWT Tokens**: Secure token-based authentication
- **Rate Limiting**: Prevents brute force attacks
- **Input Validation**: Yup schema validation
- **SQL Injection Prevention**: Parameterized queries
- **CORS Protection**: Configurable cross-origin requests
- **Helmet**: Security headers

## 🚀 Deployment

### Environment Variables for Production

```env
NODE_ENV=production
DB_HOST=your-production-db-host
DB_NAME=lte_lms
DB_USER=your-db-user
DB_PASSWORD=your-secure-password
JWT_SECRET=your-super-secure-jwt-secret
CORS_ORIGIN=https://your-frontend-domain.com
```

### Docker Support (Coming Soon)

```dockerfile
# Dockerfile will be added for containerized deployment
```

## 📚 Documentation

- API documentation will be available at `/api/docs` (Coming Soon)
- Database schema documentation in `/docs/database.md`
- Architecture documentation in `/docs/architecture.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
