'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Bell,
  Search,
  User,
  LogOut,
  Settings,
  Shield,
  ChevronDown,
  Globe,
  Activity
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { usePermissions } from '@/components/auth/ProtectedRoute'

export default function SuperAdminHeader() {
  const router = useRouter()
  const { user } = usePermissions()
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)
  const [notifications] = useState([
    {
      id: 1,
      title: 'New Institute Registration',
      message: 'Harvard Medical School has requested access',
      time: '5 minutes ago',
      type: 'info'
    },
    {
      id: 2,
      title: 'System Alert',
      message: 'High CPU usage detected on server cluster',
      time: '15 minutes ago',
      type: 'warning'
    },
    {
      id: 3,
      title: 'Subscription Renewal',
      message: 'MIT subscription renewed for 1 year',
      time: '1 hour ago',
      type: 'success'
    }
  ])

  const handleLogout = () => {
    // TODO: Implement logout logic
    console.log('Logging out...')
    router.push('/auth/login')
  }

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Search */}
        <div className="flex-1 max-w-lg">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search institutes, users, or analytics..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Platform Stats */}
          <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4" />
              <span>156 Institutes</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>12.4K Users</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-500" />
              <span>99.9% Uptime</span>
            </div>
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-5 w-5" />
              {notifications.length > 0 && (
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notifications.length}
                </span>
              )}
            </Button>
          </div>

          {/* Profile Menu */}
          <div className="relative">
            <button
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-gray-900">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-600">Super Administrator</p>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </button>

            {/* Profile Dropdown */}
            {isProfileMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="text-sm font-medium text-gray-900">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-xs text-gray-600">{user?.email}</p>
                </div>
                
                <div className="py-1">
                  <button
                    onClick={() => router.push('/profile')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <User className="mr-3 h-4 w-4" />
                    Profile Settings
                  </button>
                  
                  <button
                    onClick={() => router.push('/super-admin/settings')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <Settings className="mr-3 h-4 w-4" />
                    Platform Settings
                  </button>
                  
                  <button
                    onClick={() => router.push('/super-admin/system')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <Globe className="mr-3 h-4 w-4" />
                    System Status
                  </button>
                </div>
                
                <div className="border-t border-gray-100 py-1">
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    <LogOut className="mr-3 h-4 w-4" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
