const crypto = require('crypto');
const { query } = require('../database/connection');
const validator = require('validator');

/**
 * Security Service
 * Handles security-related operations including threat detection, audit logging, and security monitoring
 */

class SecurityService {
  constructor() {
    this.suspiciousActivityThreshold = 10; // Number of suspicious activities before blocking
    this.blockDuration = 60 * 60 * 1000; // 1 hour in milliseconds
  }

  /**
   * Log security events for monitoring and audit
   */
  async logSecurityEvent(eventData) {
    try {
      const {
        eventType,
        severity,
        ipAddress,
        userAgent,
        userId = null,
        details = {},
        endpoint = null
      } = eventData;

      await query(`
        INSERT INTO security_audit_log (
          event_type, severity, ip_address, user_agent, user_id, 
          details, endpoint, created_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      `, [
        eventType,
        severity,
        ipAddress,
        userAgent,
        userId,
        JSON.stringify(details),
        endpoint
      ]);

      // Check for suspicious patterns
      await this.checkSuspiciousActivity(ipAddress, eventType);

    } catch (error) {
      console.error('Failed to log security event:', error.message);
    }
  }

  /**
   * Check for suspicious activity patterns
   */
  async checkSuspiciousActivity(ipAddress, eventType) {
    try {
      // Count recent suspicious events from this IP
      const recentEvents = await query(`
        SELECT COUNT(*) as count
        FROM security_audit_log 
        WHERE ip_address = $1 
        AND severity IN ('high', 'critical')
        AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour'
      `, [ipAddress]);

      const suspiciousCount = parseInt(recentEvents.rows[0]?.count || 0);

      if (suspiciousCount >= this.suspiciousActivityThreshold) {
        await this.blockSuspiciousIP(ipAddress, suspiciousCount);
      }

    } catch (error) {
      console.error('Failed to check suspicious activity:', error.message);
    }
  }

  /**
   * Block suspicious IP address
   */
  async blockSuspiciousIP(ipAddress, eventCount) {
    try {
      const expiresAt = new Date(Date.now() + this.blockDuration);

      await query(`
        INSERT INTO blocked_ips (
          ip_address, reason, event_count, expires_at, created_at
        )
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        ON CONFLICT (ip_address)
        DO UPDATE SET
          event_count = EXCLUDED.event_count,
          expires_at = EXCLUDED.expires_at,
          updated_at = CURRENT_TIMESTAMP
      `, [
        ipAddress,
        'Suspicious activity detected',
        eventCount,
        expiresAt
      ]);

      console.warn(`Blocked suspicious IP: ${ipAddress} (${eventCount} events)`);

      // Log the blocking event
      await this.logSecurityEvent({
        eventType: 'ip_blocked',
        severity: 'critical',
        ipAddress,
        userAgent: 'system',
        details: {
          reason: 'Suspicious activity detected',
          eventCount,
          blockDuration: this.blockDuration
        }
      });

    } catch (error) {
      console.error('Failed to block suspicious IP:', error.message);
    }
  }

  /**
   * Check if IP address is blocked
   */
  async isIPBlocked(ipAddress) {
    try {
      const result = await query(`
        SELECT id, reason, expires_at
        FROM blocked_ips 
        WHERE ip_address = $1 
        AND expires_at > CURRENT_TIMESTAMP
      `, [ipAddress]);

      return result.rows.length > 0 ? result.rows[0] : null;

    } catch (error) {
      console.error('Failed to check IP block status:', error.message);
      return null;
    }
  }

  /**
   * Validate and sanitize file uploads
   */
  validateFileUpload(file, allowedTypes = [], maxSize = 5 * 1024 * 1024) {
    const errors = [];

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`);
    }

    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
      errors.push(`File type ${file.mimetype} is not allowed`);
    }

    // Check for dangerous file extensions
    const dangerousExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
      '.php', '.asp', '.aspx', '.jsp', '.sh', '.ps1', '.py', '.rb'
    ];

    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push(`File extension ${fileExtension} is not allowed for security reasons`);
    }

    // Validate filename
    if (!validator.isLength(file.originalname, { min: 1, max: 255 })) {
      errors.push('Filename must be between 1 and 255 characters');
    }

    // Check for path traversal attempts in filename
    if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
      errors.push('Filename contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash sensitive data
   */
  hashSensitiveData(data, salt = null) {
    if (!salt) {
      salt = crypto.randomBytes(16).toString('hex');
    }
    const hash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return { hash, salt };
  }

  /**
   * Verify hashed data
   */
  verifySensitiveData(data, hash, salt) {
    const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return hash === verifyHash;
  }

  /**
   * Clean up expired security records
   */
  async cleanupExpiredRecords() {
    try {
      // Clean up expired blocked IPs
      const blockedIPsResult = await query(`
        DELETE FROM blocked_ips 
        WHERE expires_at < CURRENT_TIMESTAMP
        RETURNING ip_address
      `);

      // Clean up old audit logs (keep for 90 days)
      const auditLogResult = await query(`
        DELETE FROM security_audit_log 
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days'
        RETURNING id
      `);

      console.log(`Security cleanup completed:`, {
        expiredBlockedIPs: blockedIPsResult.rows.length,
        oldAuditLogs: auditLogResult.rows.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Failed to cleanup expired security records:', error.message);
    }
  }

  /**
   * Get security statistics
   */
  async getSecurityStats(timeframe = '24 hours') {
    try {
      const interval = timeframe === '24 hours' ? '24 hours' : 
                      timeframe === '7 days' ? '7 days' : 
                      timeframe === '30 days' ? '30 days' : '24 hours';

      const stats = await query(`
        SELECT 
          event_type,
          severity,
          COUNT(*) as count
        FROM security_audit_log 
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '${interval}'
        GROUP BY event_type, severity
        ORDER BY count DESC
      `);

      const blockedIPs = await query(`
        SELECT COUNT(*) as count
        FROM blocked_ips 
        WHERE expires_at > CURRENT_TIMESTAMP
      `);

      const topIPs = await query(`
        SELECT 
          ip_address,
          COUNT(*) as event_count
        FROM security_audit_log 
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '${interval}'
        GROUP BY ip_address
        ORDER BY event_count DESC
        LIMIT 10
      `);

      return {
        timeframe,
        eventStats: stats.rows,
        activeBlockedIPs: parseInt(blockedIPs.rows[0]?.count || 0),
        topIPs: topIPs.rows
      };

    } catch (error) {
      console.error('Failed to get security stats:', error.message);
      return null;
    }
  }

  /**
   * Detect potential brute force attacks
   */
  async detectBruteForce(identifier, action, timeWindow = 15 * 60 * 1000, threshold = 5) {
    try {
      const windowStart = new Date(Date.now() - timeWindow);

      const attempts = await query(`
        SELECT COUNT(*) as count
        FROM security_audit_log 
        WHERE details->>'identifier' = $1
        AND event_type = $2
        AND severity = 'high'
        AND created_at > $3
      `, [identifier, action, windowStart]);

      const attemptCount = parseInt(attempts.rows[0]?.count || 0);
      
      if (attemptCount >= threshold) {
        await this.logSecurityEvent({
          eventType: 'brute_force_detected',
          severity: 'critical',
          ipAddress: 'system',
          userAgent: 'system',
          details: {
            identifier,
            action,
            attemptCount,
            timeWindow,
            threshold
          }
        });

        return {
          detected: true,
          attemptCount,
          timeWindow,
          threshold
        };
      }

      return {
        detected: false,
        attemptCount,
        timeWindow,
        threshold
      };

    } catch (error) {
      console.error('Failed to detect brute force:', error.message);
      return { detected: false, error: error.message };
    }
  }

  /**
   * Validate API key format and strength
   */
  validateAPIKey(apiKey) {
    const errors = [];

    if (!apiKey || typeof apiKey !== 'string') {
      errors.push('API key is required');
      return { isValid: false, errors };
    }

    // Check length (should be at least 32 characters)
    if (apiKey.length < 32) {
      errors.push('API key must be at least 32 characters long');
    }

    // Check for valid characters (alphanumeric and some special chars)
    if (!/^[a-zA-Z0-9_-]+$/.test(apiKey)) {
      errors.push('API key contains invalid characters');
    }

    // Check for common weak patterns
    const weakPatterns = [
      /^(.)\1+$/, // All same character
      /^(012|123|234|345|456|567|678|789|890|abc|def|ghi)/, // Sequential patterns
      /^(password|secret|key|token|api)/i // Common words
    ];

    for (const pattern of weakPatterns) {
      if (pattern.test(apiKey)) {
        errors.push('API key appears to be weak or predictable');
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = new SecurityService();
