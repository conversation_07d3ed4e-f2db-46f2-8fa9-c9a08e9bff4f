{"name": "lms-backend", "version": "1.0.0", "description": "LMS SAAS Backend API", "main": "src/index.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node src/server.ts", "build": "tsc", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "ts-node src/database/migrate.ts", "db:seed": "ts-node src/database/seed.ts", "db:reset": "ts-node src/database/reset.ts"}, "dependencies": {"@fastify/csrf-protection": "^7.1.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csrf-csrf": "^4.0.3", "csurf": "^1.11.0", "dompurify": "^3.2.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "express-rate-limit": "^7.5.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "sanitize-html": "^2.17.0", "uuid": "^9.0.1", "validator": "^13.15.15", "xss": "^1.0.15", "yup": "^1.3.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/express-validator": "^2.20.33", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.8.10", "@types/pg": "^8.10.7", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.8", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "keywords": ["lms", "saas", "education", "nodejs", "express", "postgresql"], "author": "LMS Team", "license": "MIT"}