'use client'

import React from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { Building2, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormField, SelectField } from '@/components/forms/FormField'
import { Modal } from '@/components/ui/modal'

const instituteSchema = Yup.object().shape({
  name: Yup.string()
    .min(1, 'Institute name is required')
    .max(100, 'Institute name must be less than 100 characters')
    .required('Institute name is required'),
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  website: Yup.string()
    .url('Please enter a valid website URL')
    .nullable(),
  subscriptionPlan: Yup.string()
    .oneOf(['basic', 'premium', 'enterprise'], 'Please select a valid subscription plan')
    .required('Subscription plan is required'),
})

const subscriptionPlans = [
  { value: 'basic', label: 'Basic - $99/month' },
  { value: 'premium', label: 'Premium - $299/month' },
  { value: 'enterprise', label: 'Enterprise - $999/month' },
]

interface InstituteFormData {
  name: string
  email: string
  website: string
  subscriptionPlan: string
}

interface InstituteFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  institute?: any
  mode: 'create' | 'edit'
}

export default function InstituteFormModal({
  isOpen,
  onClose,
  onSuccess,
  institute,
  mode
}: InstituteFormModalProps) {
  const initialValues: InstituteFormData = {
    name: institute?.name || '',
    email: institute?.email || '',
    website: institute?.website || '',
    subscriptionPlan: institute?.subscriptionPlan || 'basic',
  }

  const handleSubmit = async (values: InstituteFormData, { setSubmitting }: any) => {
    try {
      const url = mode === 'create' 
        ? '/api/super-admin/institutes'
        : `/api/super-admin/institutes/${institute?.id}`
      
      const method = mode === 'create' ? 'POST' : 'PUT'

      // TODO: Replace with actual API call
      console.log(`${mode} institute:`, values)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success(`Institute ${mode === 'create' ? 'created' : 'updated'} successfully!`)
      onSuccess()
      onClose()
      
    } catch (error) {
      console.error(`${mode} institute error:`, error)
      toast.error(`Failed to ${mode} institute`)
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'create' ? 'Create New' : 'Edit'} Institute`}
      size="lg"
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <Building2 className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {mode === 'create' ? 'Add New Institute' : 'Edit Institute'}
            </h3>
            <p className="text-sm text-gray-600">
              {mode === 'create' 
                ? 'Create a new educational institution account'
                : 'Update institute information and settings'
              }
            </p>
          </div>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={instituteSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  name="name"
                  label="Institute Name"
                  placeholder="Harvard University"
                  required
                />
                
                <FormField
                  name="email"
                  label="Admin Email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <FormField
                name="website"
                label="Website URL"
                placeholder="https://harvard.edu"
              />

              <SelectField
                name="subscriptionPlan"
                label="Subscription Plan"
                options={subscriptionPlans}
                required
              />

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-800 mb-2">
                  {mode === 'create' ? 'What happens next?' : 'Update Information'}
                </h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  {mode === 'create' ? (
                    <>
                      <li>• Institute will be created with "Pending" status</li>
                      <li>• Admin will receive setup instructions via email</li>
                      <li>• Domain verification will be required</li>
                      <li>• Subscription billing will begin after activation</li>
                    </>
                  ) : (
                    <>
                      <li>• Changes will be applied immediately</li>
                      <li>• Admin will be notified of updates</li>
                      <li>• Billing changes take effect next cycle</li>
                    </>
                  )}
                </ul>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={isSubmitting}
                >
                  {mode === 'create' ? 'Create Institute' : 'Update Institute'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </Modal>
  )
}
