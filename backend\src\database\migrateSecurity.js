const { Pool } = require('pg');

/**
 * Security Tables Migration
 * Creates tables for security audit logging, IP blocking, and threat monitoring
 */

async function migrateSecurityTables() {
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'lte_lms',
    password: process.env.DB_PASSWORD || '1234',
    port: process.env.DB_PORT || 5432,
  });

  try {
    console.log('🚀 Starting security tables migration...');

    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connected successfully');
    console.log(`📊 Connected to database: ${client.database}`);
    console.log(`🏠 Host: ${client.host}:${client.port}`);
    client.release();

    // Security tables SQL
    const securityTablesSQL = `
-- =============================================
-- SECURITY AND AUDIT TABLES
-- =============================================

-- Security audit log table
CREATE TABLE IF NOT EXISTS security_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL, -- 'login_attempt', 'failed_login', 'suspicious_request', etc.
    severity VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    ip_address INET NOT NULL,
    user_agent TEXT,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    endpoint VARCHAR(255), -- API endpoint that was accessed
    details JSONB, -- Additional event details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blocked IPs table
CREATE TABLE IF NOT EXISTS blocked_ips (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL UNIQUE,
    reason TEXT NOT NULL,
    event_count INTEGER DEFAULT 1,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Failed login attempts tracking
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reason VARCHAR(100), -- 'invalid_password', 'user_not_found', 'account_locked', etc.
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE
);

-- API rate limiting tracking
CREATE TABLE IF NOT EXISTS rate_limit_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    violation_count INTEGER DEFAULT 1,
    window_start TIMESTAMP NOT NULL,
    window_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Security configuration table
CREATE TABLE IF NOT EXISTS security_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Session security tracking
CREATE TABLE IF NOT EXISTS session_security (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    ip_address INET NOT NULL,
    user_agent TEXT,
    location_country VARCHAR(2), -- ISO country code
    location_city VARCHAR(100),
    is_suspicious BOOLEAN DEFAULT FALSE,
    risk_score INTEGER DEFAULT 0, -- 0-100 risk score
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Password security history
CREATE TABLE IF NOT EXISTS password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Two-factor authentication table (for future use)
CREATE TABLE IF NOT EXISTS two_factor_auth (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    secret_key VARCHAR(255) NOT NULL,
    backup_codes JSONB, -- Array of backup codes
    is_enabled BOOLEAN DEFAULT FALSE,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Security indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_audit_log_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_severity ON security_audit_log(severity);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_ip_address ON security_audit_log(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_created_at ON security_audit_log(created_at);

CREATE INDEX IF NOT EXISTS idx_blocked_ips_ip_address ON blocked_ips(ip_address);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_expires_at ON blocked_ips(expires_at);

CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_email ON failed_login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_ip_address ON failed_login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_failed_login_attempts_attempt_time ON failed_login_attempts(attempt_time);

CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_ip_address ON rate_limit_violations(ip_address);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_endpoint ON rate_limit_violations(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_window_start ON rate_limit_violations(window_start);

CREATE INDEX IF NOT EXISTS idx_security_config_config_key ON security_config(config_key);
CREATE INDEX IF NOT EXISTS idx_security_config_is_active ON security_config(is_active);

CREATE INDEX IF NOT EXISTS idx_session_security_session_id ON session_security(session_id);
CREATE INDEX IF NOT EXISTS idx_session_security_user_id ON session_security(user_id);
CREATE INDEX IF NOT EXISTS idx_session_security_ip_address ON session_security(ip_address);
CREATE INDEX IF NOT EXISTS idx_session_security_is_suspicious ON session_security(is_suspicious);
CREATE INDEX IF NOT EXISTS idx_session_security_last_activity ON session_security(last_activity);

CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);

CREATE INDEX IF NOT EXISTS idx_two_factor_auth_user_id ON two_factor_auth(user_id);
CREATE INDEX IF NOT EXISTS idx_two_factor_auth_is_enabled ON two_factor_auth(is_enabled);

-- Insert default security configuration
INSERT INTO security_config (config_key, config_value, description) VALUES
('rate_limit_general', '{"windowMs": 900000, "max": 1000}', 'General API rate limiting configuration'),
('rate_limit_auth', '{"windowMs": 900000, "max": 5}', 'Authentication endpoints rate limiting'),
('rate_limit_password_reset', '{"windowMs": 3600000, "max": 3}', 'Password reset rate limiting'),
('password_policy', '{"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": true, "maxAge": 7776000}', 'Password policy configuration'),
('session_security', '{"maxAge": 86400000, "requireSecure": true, "sameSite": "strict"}', 'Session security configuration'),
('file_upload', '{"maxSize": 5242880, "allowedTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf"]}', 'File upload security configuration')
ON CONFLICT (config_key) DO NOTHING;
`;

    console.log('📄 Executing security tables SQL...');

    // Execute the SQL
    await pool.query(securityTablesSQL);

    console.log('✅ Security tables migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - security_audit_log');
    console.log('   - blocked_ips');
    console.log('   - failed_login_attempts');
    console.log('   - rate_limit_violations');
    console.log('   - security_config');
    console.log('   - session_security');
    console.log('   - password_history');
    console.log('   - two_factor_auth');
    console.log('🔍 Created indexes for performance optimization');
    console.log('⚙️ Inserted default security configuration');

  } catch (error) {
    console.error('❌ Migration error:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('⚠️  Tables already exist, skipping creation');
    } else {
      throw error;
    }
  } finally {
    console.log('🔒 Database pool closed');
    await pool.end();
  }
}

// Run migration if script is executed directly
if (require.main === module) {
  migrateSecurityTables()
    .then(() => {
      console.log('🏁 Security tables migration completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { migrateSecurityTables };
