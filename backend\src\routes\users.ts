import { Router, Request, Response } from 'express'
import { body, param, query, validationResult } from 'express-validator'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'
import crypto from 'crypto'
import { 
  User,
  UserProfile,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UserRegistrationRequest,
  EmailVerificationRequest,
  UserStatus,
  VerificationStatus,
  PASSWORD_MIN_LENGTH,
  PASSWORD_REGEX,
  EMAIL_REGEX,
  EMAIL_VERIFICATION_TOKEN_EXPIRY,
  DEFAULT_USER_PREFERENCES
} from '../types/user'
import { UserRole } from '../types/auth'
import {
  authenticate,
  requireRole,
  requirePermission,
  requireSameInstitute,
  requireActiveAccount,
  requireVerifiedAccount,
  requireInstituteAccess
} from '../middleware/auth'
import { Permission } from '../types/auth'
import { generateAccessToken, generateRefreshToken } from '../utils/jwt'

const router = Router()

// Validation rules
const userRegistrationValidation = [
  body('firstName').trim().isLength({ min: 1, max: 50 }).withMessage('First name is required and must be less than 50 characters'),
  body('lastName').trim().isLength({ min: 1, max: 50 }).withMessage('Last name is required and must be less than 50 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password')
    .isLength({ min: PASSWORD_MIN_LENGTH })
    .withMessage(`Password must be at least ${PASSWORD_MIN_LENGTH} characters`)
    .matches(PASSWORD_REGEX)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('role').isIn(['student', 'teacher']).withMessage('Role must be either student or teacher'),
  body('instituteCode').trim().isLength({ min: 1 }).withMessage('Institute code is required'),
  body('metadata.studentId').optional().trim().isLength({ max: 50 }).withMessage('Student ID must be less than 50 characters'),
  body('metadata.employeeId').optional().trim().isLength({ max: 50 }).withMessage('Employee ID must be less than 50 characters'),
  body('metadata.department').optional().trim().isLength({ max: 100 }).withMessage('Department must be less than 100 characters'),
  body('metadata.yearOfStudy').optional().isInt({ min: 1, max: 10 }).withMessage('Year of study must be between 1 and 10'),
  body('metadata.major').optional().trim().isLength({ max: 100 }).withMessage('Major must be less than 100 characters'),
]

const updateProfileValidation = [
  body('firstName').optional().trim().isLength({ min: 1, max: 50 }).withMessage('First name must be less than 50 characters'),
  body('lastName').optional().trim().isLength({ min: 1, max: 50 }).withMessage('Last name must be less than 50 characters'),
  body('phone').optional().matches(/^\+?[1-9]\d{1,14}$/).withMessage('Valid phone number is required'),
  body('dateOfBirth').optional().isISO8601().withMessage('Valid date of birth is required'),
  body('preferences.language').optional().isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi']).withMessage('Invalid language'),
  body('preferences.timezone').optional().isString().withMessage('Valid timezone is required'),
  body('preferences.theme').optional().isIn(['light', 'dark', 'auto']).withMessage('Theme must be light, dark, or auto'),
]

const changePasswordValidation = [
  body('currentPassword').isLength({ min: 1 }).withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: PASSWORD_MIN_LENGTH })
    .withMessage(`New password must be at least ${PASSWORD_MIN_LENGTH} characters`)
    .matches(PASSWORD_REGEX)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
]

/**
 * POST /api/users/register
 * Register a new student or teacher (Public endpoint)
 */
router.post('/register', userRegistrationValidation, async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array(),
      })
      return
    }

    const data: UserRegistrationRequest = req.body

    // TODO: Validate institute code and get institute ID
    // For now, we'll use a mock institute lookup
    const mockInstitutes = {
      'HARV001': { id: 'inst-1', name: 'Harvard University', domain: 'harvard.edu' },
      'MIT001': { id: 'inst-2', name: 'MIT', domain: 'mit.edu' },
      'STAN001': { id: 'inst-3', name: 'Stanford University', domain: 'stanford.edu' },
      'UNIV001': { id: 'inst-4', name: 'Example University', domain: 'university.edu' },
    }

    const institute = mockInstitutes[data.instituteCode as keyof typeof mockInstitutes]
    if (!institute) {
      res.status(400).json({
        success: false,
        error: 'Invalid institute code',
        code: 'INVALID_INSTITUTE_CODE',
      })
      return
    }

    // Validate email domain matches institute
    const emailDomain = data.email.split('@')[1]?.toLowerCase()
    if (emailDomain !== institute.domain) {
      res.status(400).json({
        success: false,
        error: `Email domain must be @${institute.domain}`,
        code: 'INVALID_EMAIL_DOMAIN',
      })
      return
    }

    // TODO: Check if email already exists
    // Mock check
    if (data.email === '<EMAIL>') {
      res.status(409).json({
        success: false,
        error: 'An account with this email already exists',
        code: 'EMAIL_EXISTS',
      })
      return
    }

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12')
    const hashedPassword = await bcrypt.hash(data.password, saltRounds)

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex')
    const verificationExpires = new Date(Date.now() + EMAIL_VERIFICATION_TOKEN_EXPIRY)

    // Create user
    const userId = uuidv4()
    const newUser: User = {
      id: userId,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      role: data.role === 'student' ? UserRole.STUDENT : UserRole.TEACHER,
      instituteId: institute.id,
      password: hashedPassword,
      status: UserStatus.PENDING,
      emailVerified: false,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationExpires,
      preferences: DEFAULT_USER_PREFERENCES,
      metadata: data.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // TODO: Save user to database
    console.log('Creating user:', { ...newUser, password: '[REDACTED]' })

    // TODO: Send verification email
    console.log('Sending verification email to:', data.email, 'with token:', verificationToken)

    res.status(201).json({
      success: true,
      message: 'Registration successful. Please check your email for verification.',
      data: {
        userId: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        role: newUser.role,
        instituteName: institute.name,
        emailVerificationRequired: true,
      },
    })
  } catch (error) {
    console.error('User registration error:', error)
    res.status(500).json({
      success: false,
      error: 'Registration failed',
      code: 'REGISTRATION_ERROR',
    })
  }
})

/**
 * POST /api/users/verify-email
 * Verify user email address
 */
router.post('/verify-email', 
  body('token').isLength({ min: 1 }).withMessage('Verification token is required'),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: errors.array(),
        })
        return
      }

      const { token }: EmailVerificationRequest = req.body

      // TODO: Find user by verification token and check expiry
      // Mock verification
      if (token === 'invalid-token') {
        res.status(400).json({
          success: false,
          error: 'Invalid or expired verification token',
          code: 'INVALID_TOKEN',
        })
        return
      }

      // TODO: Update user email verification status
      console.log('Verifying email with token:', token)

      res.json({
        success: true,
        message: 'Email verified successfully. You can now log in.',
      })
    } catch (error) {
      console.error('Email verification error:', error)
      res.status(500).json({
        success: false,
        error: 'Email verification failed',
      })
    }
  }
)

/**
 * POST /api/users/resend-verification
 * Resend email verification
 */
router.post('/resend-verification',
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: errors.array(),
        })
        return
      }

      const { email } = req.body

      // TODO: Find user by email and check if verification is needed
      // TODO: Generate new verification token and send email
      console.log('Resending verification email to:', email)

      res.json({
        success: true,
        message: 'Verification email sent. Please check your inbox.',
      })
    } catch (error) {
      console.error('Resend verification error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to resend verification email',
      })
    }
  }
)

/**
 * GET /api/users/profile
 * Get current user profile
 */
router.get('/profile', authenticate, requireVerifiedAccount, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
      })
      return
    }

    // TODO: Fetch full user profile from database
    const mockProfile: UserProfile = {
      id: req.user.id,
      email: req.user.email,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      role: req.user.role,
      instituteId: req.user.instituteId || '',
      instituteName: 'Example University',
      status: UserStatus.ACTIVE,
      emailVerified: req.user.emailVerified,
      lastLoginAt: new Date(),
      preferences: DEFAULT_USER_PREFERENCES,
      createdAt: req.user.createdAt,
      updatedAt: req.user.updatedAt,
    }

    res.json({
      success: true,
      data: mockProfile,
    })
  } catch (error) {
    console.error('Get profile error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch profile',
    })
  }
})

/**
 * PUT /api/users/profile
 * Update current user profile
 */
router.put('/profile',
  authenticate,
  requireActiveAccount,
  updateProfileValidation,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: errors.array(),
        })
        return
      }

      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
        return
      }

      const updateData: UpdateUserRequest = req.body

      // TODO: Update user profile in database
      console.log('Updating profile for user:', req.user.id, updateData)

      res.json({
        success: true,
        message: 'Profile updated successfully',
      })
    } catch (error) {
      console.error('Update profile error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to update profile',
      })
    }
  }
)

/**
 * PUT /api/users/change-password
 * Change user password
 */
router.put('/change-password',
  authenticate,
  requireActiveAccount,
  changePasswordValidation,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: errors.array(),
        })
        return
      }

      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        })
        return
      }

      const { currentPassword, newPassword }: ChangePasswordRequest = req.body

      // TODO: Fetch user from database and verify current password
      // Mock password verification - in real implementation, fetch user with password from DB
      const mockStoredPassword = '$2b$12$mockHashedPassword' // This would come from database
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, mockStoredPassword)
      if (!isCurrentPasswordValid) {
        res.status(400).json({
          success: false,
          error: 'Current password is incorrect',
          code: 'INVALID_PASSWORD',
        })
        return
      }

      // Hash new password
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12')
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds)

      // TODO: Update password in database
      console.log('Changing password for user:', req.user.id)

      res.json({
        success: true,
        message: 'Password changed successfully',
      })
    } catch (error) {
      console.error('Change password error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to change password',
      })
    }
  }
)

/**
 * GET /api/users
 * Get users list (Institute Admin and above)
 */
router.get('/',
  authenticate,
  requireActiveAccount,
  requirePermission(Permission.MANAGE_INSTITUTE_USERS),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('role').optional().isIn(['student', 'teacher']).withMessage('Role must be student or teacher'),
  query('status').optional().isIn(Object.values(UserStatus)).withMessage('Invalid status'),
  async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: errors.array(),
        })
        return
      }

      const { page = 1, limit = 10, role, status, search } = req.query

      // TODO: Fetch users from database with filters and pagination
      const mockUsers: UserProfile[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.STUDENT,
          instituteId: 'inst-1',
          instituteName: 'Example University',
          status: UserStatus.ACTIVE,
          emailVerified: true,
          createdAt: new Date('2023-01-15'),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.TEACHER,
          instituteId: 'inst-1',
          instituteName: 'Example University',
          status: UserStatus.ACTIVE,
          emailVerified: true,
          createdAt: new Date('2023-02-01'),
          updatedAt: new Date(),
        },
      ]

      res.json({
        success: true,
        data: mockUsers,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: mockUsers.length,
          totalPages: Math.ceil(mockUsers.length / Number(limit)),
        },
      })
    } catch (error) {
      console.error('Get users error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to fetch users',
      })
    }
  }
)

export default router
