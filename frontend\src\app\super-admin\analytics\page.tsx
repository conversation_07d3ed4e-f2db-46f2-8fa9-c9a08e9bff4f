'use client'

import React, { useState } from 'react'
import { 
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Building2,
  Calendar,
  ArrowUp,
  ArrowDown,
  Activity
} from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function SuperAdminAnalyticsPage() {
  const [period, setPeriod] = useState('30d')

  // Mock analytics data
  const analyticsData = {
    revenue: {
      total: 89750,
      growth: 15.2,
      byPlan: {
        basic: 12500,
        premium: 34250,
        enterprise: 43000,
      },
    },
    users: {
      total: 12437,
      growth: 8.7,
      active: 11892,
      newRegistrations: 456,
    },
    institutes: {
      total: 156,
      growth: 5.4,
      active: 142,
      pending: 8,
    },
    engagement: {
      averageSessionDuration: '24m 32s',
      dailyActiveUsers: 8234,
      courseCompletionRate: 78.5,
      userSatisfactionScore: 4.6,
    },
  }

  const revenueByMonth = [
    { month: 'Jan', revenue: 65000 },
    { month: 'Feb', revenue: 72000 },
    { month: 'Mar', revenue: 68000 },
    { month: 'Apr', revenue: 78000 },
    { month: 'May', revenue: 85000 },
    { month: 'Jun', revenue: 89750 },
  ]

  const topInstitutes = [
    { name: 'Harvard University', revenue: 12500, users: 15000, growth: 12.3 },
    { name: 'MIT', revenue: 8500, users: 12000, growth: 8.7 },
    { name: 'Stanford University', revenue: 7200, users: 9500, growth: 15.2 },
    { name: 'UC Berkeley', revenue: 6800, users: 8200, growth: -2.1 },
    { name: 'Yale University', revenue: 5900, users: 7800, growth: 9.4 },
  ]

  const userGrowthData = [
    { month: 'Jul', students: 8234, teachers: 1456, admins: 89 },
    { month: 'Aug', students: 8567, teachers: 1523, admins: 92 },
    { month: 'Sep', students: 8901, teachers: 1598, admins: 95 },
    { month: 'Oct', students: 9234, teachers: 1672, admins: 98 },
    { month: 'Nov', students: 9567, teachers: 1745, admins: 101 },
    { month: 'Dec', students: 9892, teachers: 1823, admins: 104 },
  ]

  const engagementMetrics = [
    { metric: 'Daily Active Users', value: 8234, change: '+12.3%', trend: 'up' },
    { metric: 'Course Completion Rate', value: '78.5%', change: '+5.2%', trend: 'up' },
    { metric: 'Session Duration', value: '24m 32s', change: '+8.7%', trend: 'up' },
    { metric: 'User Satisfaction', value: '4.6/5', change: '+2.1%', trend: 'up' },
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Platform performance metrics and insights
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${analyticsData.revenue.total.toLocaleString()}</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">+{analyticsData.revenue.growth}%</span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.users.total.toLocaleString()}</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">+{analyticsData.users.growth}%</span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Institutes</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.institutes.active}</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <Building2 className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">+{analyticsData.institutes.growth}%</span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Satisfaction Score</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.engagement.userSatisfactionScore}/5</p>
            </div>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Activity className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">+2.3%</span>
            <span className="text-sm text-gray-500 ml-2">vs last period</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
          <div className="space-y-4">
            {revenueByMonth.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{item.month}</span>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(item.revenue / 100000) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-16 text-right">
                    ${(item.revenue / 1000).toFixed(0)}k
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* User Growth Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Growth by Role</h3>
          <div className="space-y-4">
            {userGrowthData.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{item.month}</span>
                  <span className="text-gray-900 font-medium">
                    {(item.students + item.teachers + item.admins).toLocaleString()}
                  </span>
                </div>
                <div className="flex space-x-1 h-2">
                  <div
                    className="bg-blue-500 rounded-l"
                    style={{ width: `${(item.students / (item.students + item.teachers + item.admins)) * 100}%` }}
                  ></div>
                  <div
                    className="bg-green-500"
                    style={{ width: `${(item.teachers / (item.students + item.teachers + item.admins)) * 100}%` }}
                  ></div>
                  <div
                    className="bg-purple-500 rounded-r"
                    style={{ width: `${(item.admins / (item.students + item.teachers + item.admins)) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 flex items-center space-x-4 text-xs">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded mr-1"></div>
              <span>Students</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded mr-1"></div>
              <span>Teachers</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-500 rounded mr-1"></div>
              <span>Admins</span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Institutes */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Institutes</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {topInstitutes.map((institute, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex items-center">
                  {institute.growth > 0 ? (
                    <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${institute.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {institute.growth > 0 ? '+' : ''}{institute.growth}%
                  </span>
                </div>
              </div>
              <h4 className="font-medium text-gray-900 mb-1">{institute.name}</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Revenue: ${institute.revenue.toLocaleString()}</div>
                <div>Users: {institute.users.toLocaleString()}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Engagement Metrics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Engagement Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {engagementMetrics.map((metric, index) => (
            <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600 mb-1">{metric.value}</p>
              <p className="text-sm text-gray-600 mb-2">{metric.metric}</p>
              <div className="flex items-center justify-center">
                <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600 font-medium">{metric.change}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Platform Health */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Health</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">System Uptime</span>
              <span className="text-sm font-medium text-green-600">99.9%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">API Response Time</span>
              <span className="text-sm font-medium text-blue-600">145ms</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Error Rate</span>
              <span className="text-sm font-medium text-green-600">0.02%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Connections</span>
              <span className="text-sm font-medium text-purple-600">1,247</span>
            </div>
          </div>
        </div>

        {/* Revenue Breakdown */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue by Plan</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                <span className="text-sm text-gray-600">Basic</span>
              </div>
              <span className="text-sm font-medium text-gray-900">$12.5k</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                <span className="text-sm text-gray-600">Premium</span>
              </div>
              <span className="text-sm font-medium text-gray-900">$34.3k</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                <span className="text-sm text-gray-600">Enterprise</span>
              </div>
              <span className="text-sm font-medium text-gray-900">$43.0k</span>
            </div>
          </div>
        </div>

        {/* User Retention */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Retention</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Day 1</span>
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
                <span className="text-sm font-medium text-gray-900">85%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Day 7</span>
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '73%' }}></div>
                </div>
                <span className="text-sm font-medium text-gray-900">73%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Day 30</span>
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div className="bg-red-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                </div>
                <span className="text-sm font-medium text-gray-900">65%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
