{"shared_buffers": "4GB", "effective_cache_size": "12GB", "maintenance_work_mem": "1024MB", "work_mem": "64MB", "max_connections": 200, "wal_buffers": "64MB", "min_wal_size": "4GB", "max_wal_size": "16GB", "checkpoint_completion_target": 0.9, "default_statistics_target": 100, "random_page_cost": 4, "effective_io_concurrency": 1, "max_worker_processes": 16, "max_parallel_workers": 8, "max_parallel_workers_per_gather": 4, "max_parallel_maintenance_workers": 4, "log_min_duration_statement": 1000, "log_checkpoints": "on", "log_connections": "on", "log_disconnections": "on", "log_lock_waits": "on", "log_statement": "ddl", "shared_preload_libraries": "pg_stat_statements", "track_activity_query_size": 2048, "track_counts": "on", "track_functions": "all", "track_io_timing": "on"}