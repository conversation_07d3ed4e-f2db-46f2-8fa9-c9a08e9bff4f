# PostgreSQL Client Authentication Configuration File
# Generated on: 2025-07-14T05:01:07.336Z
# Optimized for LMS SAAS security

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             postgres                                peer

# IPv4 local connections:
host    all             postgres        127.0.0.1/32            scram-sha-256
host    lte_lms         postgres        127.0.0.1/32            scram-sha-256

# IPv6 local connections:
host    all             postgres        ::1/128                 scram-sha-256
host    lte_lms         postgres        ::1/128                 scram-sha-256

# Application connections (adjust IP ranges as needed)
host    lte_lms         postgres        10.0.0.0/8              scram-sha-256
host    lte_lms         postgres        **********/12           scram-sha-256
host    lte_lms         postgres        ***********/16          scram-sha-256

# SSL connections for production
hostssl lte_lms         postgres        0.0.0.0/0               scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject
