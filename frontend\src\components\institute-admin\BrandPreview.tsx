'use client'

import React from 'react'
import { 
  Home, 
  BookOpen, 
  Users, 
  BarChart3, 
  Settings,
  Bell,
  Search,
  User,
  Star,
  Clock,
  Calendar
} from 'lucide-react'

interface BrandingSettings {
  instituteName: string
  tagline: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  fontFamily: string
  logoUrl: string
  faviconUrl: string
  customCss: string
  enableCustomTheme: boolean
}

interface BrandPreviewProps {
  branding: BrandingSettings
  mode: 'desktop' | 'tablet' | 'mobile'
}

export default function BrandPreview({ branding, mode }: BrandPreviewProps) {
  const getContainerClass = () => {
    switch (mode) {
      case 'mobile':
        return 'w-80 h-96'
      case 'tablet':
        return 'w-96 h-80'
      default:
        return 'w-full h-96'
    }
  }

  const getLogoDisplay = () => {
    if (branding.logoUrl) {
      return (
        <img 
          src={branding.logoUrl} 
          alt={branding.instituteName}
          className="h-8 w-auto max-w-32 object-contain"
        />
      )
    }
    
    // Fallback to initials
    const initials = branding.instituteName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase()
    
    return (
      <div 
        className="w-8 h-8 rounded flex items-center justify-center text-white font-bold text-sm"
        style={{ backgroundColor: branding.primaryColor }}
      >
        {initials}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900">
          {mode.charAt(0).toUpperCase() + mode.slice(1)} Preview
        </h4>
        <div className="text-xs text-gray-500">
          {mode === 'desktop' && '1200px+'}
          {mode === 'tablet' && '768px - 1199px'}
          {mode === 'mobile' && '< 768px'}
        </div>
      </div>

      <div 
        className={`${getContainerClass()} border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm`}
        style={{ fontFamily: branding.fontFamily }}
      >
        {/* Header */}
        <div 
          className="h-16 px-4 flex items-center justify-between border-b"
          style={{ backgroundColor: branding.primaryColor }}
        >
          <div className="flex items-center space-x-3">
            {getLogoDisplay()}
            {mode !== 'mobile' && (
              <div>
                <h1 className="text-white font-semibold text-sm">{branding.instituteName}</h1>
                {branding.tagline && mode === 'desktop' && (
                  <p className="text-white/80 text-xs">{branding.tagline}</p>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {mode === 'desktop' && (
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-white/60" />
                <input 
                  type="text" 
                  placeholder="Search..."
                  className="pl-7 pr-3 py-1 text-xs bg-white/20 text-white placeholder-white/60 rounded border-0 focus:outline-none focus:ring-1 focus:ring-white/40"
                />
              </div>
            )}
            <Bell className="w-4 h-4 text-white/80" />
            <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
              <User className="w-3 h-3 text-white" />
            </div>
          </div>
        </div>

        {/* Navigation */}
        {mode === 'desktop' && (
          <div className="h-12 px-4 flex items-center space-x-6 bg-gray-50 border-b">
            {[
              { icon: Home, label: 'Dashboard' },
              { icon: BookOpen, label: 'Courses' },
              { icon: Users, label: 'Students' },
              { icon: BarChart3, label: 'Analytics' },
              { icon: Settings, label: 'Settings' },
            ].map((item, index) => (
              <div 
                key={index}
                className={`flex items-center space-x-2 px-3 py-1 rounded text-xs ${
                  index === 0 
                    ? 'text-white' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                style={index === 0 ? { backgroundColor: branding.primaryColor } : {}}
              >
                <item.icon className="w-3 h-3" />
                <span>{item.label}</span>
              </div>
            ))}
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 p-4 space-y-4 overflow-hidden">
          {/* Welcome Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-1">
              Welcome to {branding.instituteName}
            </h2>
            {branding.tagline && (
              <p className="text-sm text-gray-600">{branding.tagline}</p>
            )}
          </div>

          {/* Stats Cards */}
          <div className={`grid gap-3 ${mode === 'mobile' ? 'grid-cols-1' : 'grid-cols-2'}`}>
            <div className="p-3 bg-gray-50 rounded border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Total Students</p>
                  <p className="text-lg font-bold text-gray-900">1,247</p>
                </div>
                <div 
                  className="w-8 h-8 rounded flex items-center justify-center"
                  style={{ backgroundColor: branding.primaryColor }}
                >
                  <Users className="w-4 h-4 text-white" />
                </div>
              </div>
            </div>
            
            <div className="p-3 bg-gray-50 rounded border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Active Courses</p>
                  <p className="text-lg font-bold text-gray-900">156</p>
                </div>
                <div 
                  className="w-8 h-8 rounded flex items-center justify-center"
                  style={{ backgroundColor: branding.accentColor }}
                >
                  <BookOpen className="w-4 h-4 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <button 
              className="w-full py-2 px-4 rounded text-white text-sm font-medium transition-colors hover:opacity-90"
              style={{ backgroundColor: branding.primaryColor }}
            >
              Primary Action
            </button>
            
            <button 
              className="w-full py-2 px-4 rounded text-sm font-medium border transition-colors hover:bg-gray-50"
              style={{ 
                borderColor: branding.secondaryColor,
                color: branding.secondaryColor 
              }}
            >
              Secondary Action
            </button>
          </div>

          {/* Recent Activity */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-900">Recent Activity</h3>
            <div className="space-y-2">
              {[
                { icon: Star, text: 'Course completed', time: '2 min ago' },
                { icon: Users, text: 'New student enrolled', time: '5 min ago' },
                { icon: Calendar, text: 'Assignment due', time: '1 hour ago' },
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <div 
                    className="w-4 h-4 rounded flex items-center justify-center"
                    style={{ backgroundColor: branding.accentColor }}
                  >
                    <activity.icon className="w-2 h-2 text-white" />
                  </div>
                  <span className="flex-1 text-gray-700">{activity.text}</span>
                  <span className="text-gray-500">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="h-8 px-4 flex items-center justify-between bg-gray-50 border-t">
          <div className="text-xs text-gray-500">
            © 2024 {branding.instituteName}
          </div>
          {mode === 'desktop' && (
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <Clock className="w-3 h-3" />
              <span>Last updated: Just now</span>
            </div>
          )}
        </div>
      </div>

      {/* Custom CSS Preview */}
      {branding.customCss && branding.enableCustomTheme && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded">
          <h5 className="text-xs font-medium text-gray-900 mb-2">Custom CSS Applied</h5>
          <div className="text-xs text-gray-600 font-mono bg-white p-2 rounded border max-h-20 overflow-y-auto">
            {branding.customCss.split('\n').slice(0, 3).map((line, index) => (
              <div key={index}>{line}</div>
            ))}
            {branding.customCss.split('\n').length > 3 && (
              <div className="text-gray-400">... and more</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
