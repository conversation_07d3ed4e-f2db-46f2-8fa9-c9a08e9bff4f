{"name": "lms-saas-platform", "version": "1.0.0", "description": "Multi-tenant Learning Management System SAAS Platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint -- --fix", "lint:fix:backend": "cd backend && npm run lint -- --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "clean": "npm run clean:frontend && npm run clean:backend && rm -rf node_modules", "clean:frontend": "cd frontend && rm -rf .next node_modules", "clean:backend": "cd backend && rm -rf dist node_modules", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "prepare": "husky", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "concurrently": "^9.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["lms", "saas", "education", "learning-management-system", "multi-tenant", "nextjs", "express", "postgresql", "typescript"], "author": "LMS SAAS Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/lms-saas-platform.git"}, "bugs": {"url": "https://github.com/your-org/lms-saas-platform/issues"}, "homepage": "https://github.com/your-org/lms-saas-platform#readme", "dependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7"}}