const { query, transaction } = require('../database/connection');
const { passwordUtils } = require('../utils/auth');
const securityService = require('./securityService');

/**
 * Institute Admin Service
 * Handles institute administration operations including branding, settings, and student management
 */

class InstituteAdminService {
  /**
   * Get institute dashboard statistics
   */
  async getDashboardStats(instituteId) {
    try {
      const stats = await query(`
        SELECT 
          -- User statistics
          (SELECT COUNT(*) FROM users WHERE institute_id = $1) as total_users,
          (SELECT COUNT(*) FROM users WHERE institute_id = $1 AND role = 'student') as total_students,
          (SELECT COUNT(*) FROM users WHERE institute_id = $1 AND role = 'teacher') as total_teachers,
          (SELECT COUNT(*) FROM users WHERE institute_id = $1 AND is_active = TRUE) as active_users,
          (SELECT COUNT(*) FROM users WHERE institute_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
          
          -- Session statistics
          (SELECT COUNT(*) FROM user_sessions us 
           JOIN users u ON us.user_id = u.id 
           WHERE u.institute_id = $1 AND us.is_active = TRUE) as active_sessions,
          
          -- Security statistics
          (SELECT COUNT(*) FROM failed_login_attempts WHERE institute_id = $1 AND attempt_time >= CURRENT_DATE) as failed_logins_today,
          
          -- Domain statistics
          (SELECT COUNT(*) FROM institute_domains WHERE institute_id = $1 AND is_active = TRUE) as active_domains
      `, [instituteId]);

      return stats.rows[0];

    } catch (error) {
      console.error('Failed to get dashboard stats:', error.message);
      throw error;
    }
  }

  /**
   * Get institute activity feed
   */
  async getActivityFeed(instituteId, limit = 20) {
    try {
      const activities = await query(`
        SELECT 
          'user_registration' as activity_type,
          u.first_name || ' ' || u.last_name as description,
          u.created_at as timestamp,
          json_build_object('userId', u.id, 'role', u.role) as metadata
        FROM users u
        WHERE u.institute_id = $1
        AND u.created_at >= CURRENT_DATE - INTERVAL '7 days'
        
        UNION ALL
        
        SELECT 
          'login_activity' as activity_type,
          'User login from ' || sal.ip_address as description,
          sal.created_at as timestamp,
          json_build_object('userId', sal.user_id, 'ipAddress', sal.ip_address) as metadata
        FROM security_audit_log sal
        JOIN users u ON sal.user_id = u.id
        WHERE u.institute_id = $1
        AND sal.event_type = 'successful_login'
        AND sal.created_at >= CURRENT_DATE - INTERVAL '7 days'
        
        ORDER BY timestamp DESC
        LIMIT $2
      `, [instituteId, limit]);

      return activities.rows;

    } catch (error) {
      console.error('Failed to get activity feed:', error.message);
      throw error;
    }
  }

  /**
   * Bulk import students from CSV data
   */
  async bulkImportStudents(instituteId, studentsData, adminId) {
    try {
      const results = {
        successful: 0,
        failed: 0,
        errors: []
      };

      for (const studentData of studentsData) {
        try {
          await transaction(async (client) => {
            // Generate temporary password
            const tempPassword = securityService.generateSecureToken(12);
            const hashedPassword = await passwordUtils.hashPassword(tempPassword);

            // Create user
            const userResult = await client.query(`
              INSERT INTO users (
                email, password_hash, first_name, last_name, phone,
                role, institute_id, is_active, is_email_verified
              )
              VALUES ($1, $2, $3, $4, $5, $6, $7, TRUE, FALSE)
              RETURNING *
            `, [
              studentData.email,
              hashedPassword,
              studentData.firstName,
              studentData.lastName,
              studentData.phone || null,
              'student',
              instituteId
            ]);

            const user = userResult.rows[0];

            // Create student profile
            if (studentData.studentId || studentData.major || studentData.graduationYear) {
              await client.query(`
                INSERT INTO student_profiles (
                  user_id, student_id, major, graduation_year, academic_status
                )
                VALUES ($1, $2, $3, $4, 'active')
              `, [
                user.id,
                studentData.studentId || null,
                studentData.major || null,
                studentData.graduationYear || null
              ]);
            }

            results.successful++;
          });

        } catch (error) {
          results.failed++;
          results.errors.push({
            email: studentData.email,
            error: error.message
          });
        }
      }

      // Log bulk import activity
      await securityService.logSecurityEvent({
        eventType: 'bulk_student_import',
        severity: 'low',
        ipAddress: '127.0.0.1',
        userAgent: 'system',
        userId: adminId,
        details: {
          instituteId,
          totalRecords: studentsData.length,
          successful: results.successful,
          failed: results.failed
        }
      });

      return results;

    } catch (error) {
      console.error('Failed to bulk import students:', error.message);
      throw error;
    }
  }

  /**
   * Generate institute analytics report
   */
  async generateAnalyticsReport(instituteId, startDate, endDate) {
    try {
      // User growth analytics
      const userGrowth = await query(`
        SELECT 
          DATE(created_at) as date,
          role,
          COUNT(*) as new_users
        FROM users 
        WHERE institute_id = $1
        AND created_at BETWEEN $2 AND $3
        GROUP BY DATE(created_at), role
        ORDER BY date, role
      `, [instituteId, startDate, endDate]);

      // Login activity analytics
      const loginActivity = await query(`
        SELECT 
          DATE(sal.created_at) as date,
          COUNT(*) as total_logins,
          COUNT(DISTINCT sal.user_id) as unique_users
        FROM security_audit_log sal
        JOIN users u ON sal.user_id = u.id
        WHERE u.institute_id = $1
        AND sal.event_type = 'successful_login'
        AND sal.created_at BETWEEN $2 AND $3
        GROUP BY DATE(sal.created_at)
        ORDER BY date
      `, [instituteId, startDate, endDate]);

      // Security events analytics
      const securityEvents = await query(`
        SELECT 
          sal.event_type,
          sal.severity,
          COUNT(*) as count
        FROM security_audit_log sal
        JOIN users u ON sal.user_id = u.id
        WHERE u.institute_id = $1
        AND sal.created_at BETWEEN $2 AND $3
        GROUP BY sal.event_type, sal.severity
        ORDER BY count DESC
      `, [instituteId, startDate, endDate]);

      return {
        period: { startDate, endDate },
        userGrowth: userGrowth.rows,
        loginActivity: loginActivity.rows,
        securityEvents: securityEvents.rows
      };

    } catch (error) {
      console.error('Failed to generate analytics report:', error.message);
      throw error;
    }
  }

  /**
   * Update student status (activate/deactivate)
   */
  async updateStudentStatus(studentId, isActive, adminId, reason = '') {
    try {
      const result = await transaction(async (client) => {
        // Update user status
        const userResult = await client.query(`
          UPDATE users 
          SET is_active = $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2 AND role = 'student'
          RETURNING *
        `, [isActive, studentId]);

        if (userResult.rows.length === 0) {
          throw new Error('Student not found');
        }

        const user = userResult.rows[0];

        // If deactivating, invalidate all sessions
        if (!isActive) {
          await client.query(`
            UPDATE user_sessions 
            SET is_active = FALSE
            WHERE user_id = $1
          `, [studentId]);
        }

        // Log the status change
        await securityService.logSecurityEvent({
          eventType: 'student_status_change',
          severity: 'medium',
          ipAddress: '127.0.0.1',
          userAgent: 'system',
          userId: adminId,
          details: {
            studentId,
            oldStatus: !isActive,
            newStatus: isActive,
            reason
          }
        });

        return user;
      });

      return result;

    } catch (error) {
      console.error('Failed to update student status:', error.message);
      throw error;
    }
  }

  /**
   * Reset student password
   */
  async resetStudentPassword(studentId, adminId) {
    try {
      // Generate new temporary password
      const tempPassword = securityService.generateSecureToken(12);
      const hashedPassword = await passwordUtils.hashPassword(tempPassword);

      const result = await transaction(async (client) => {
        // Update password
        const userResult = await client.query(`
          UPDATE users 
          SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2 AND role = 'student'
          RETURNING email, first_name, last_name
        `, [hashedPassword, studentId]);

        if (userResult.rows.length === 0) {
          throw new Error('Student not found');
        }

        const user = userResult.rows[0];

        // Invalidate all existing sessions
        await client.query(`
          UPDATE user_sessions 
          SET is_active = FALSE
          WHERE user_id = $1
        `, [studentId]);

        // Log password reset
        await securityService.logSecurityEvent({
          eventType: 'admin_password_reset',
          severity: 'medium',
          ipAddress: '127.0.0.1',
          userAgent: 'system',
          userId: adminId,
          details: {
            targetUserId: studentId,
            targetUserEmail: user.email
          }
        });

        return { user, tempPassword };
      });

      return result;

    } catch (error) {
      console.error('Failed to reset student password:', error.message);
      throw error;
    }
  }

  /**
   * Get institute branding for public use
   */
  async getPublicBranding(instituteId) {
    try {
      const brandingResult = await query(`
        SELECT
          primary_color, secondary_color, accent_color,
          logo_url, favicon_url, custom_css
        FROM institute_branding
        WHERE institute_id = $1 AND is_active = TRUE
        ORDER BY created_at DESC
        LIMIT 1
      `, [instituteId]);

      const instituteResult = await query(`
        SELECT name, website
        FROM institutes 
        WHERE id = $1 AND is_active = TRUE
      `, [instituteId]);

      return {
        branding: brandingResult.rows[0] || null,
        institute: instituteResult.rows[0] || null
      };

    } catch (error) {
      console.error('Failed to get public branding:', error.message);
      throw error;
    }
  }

  /**
   * Validate institute domain ownership
   */
  async validateDomainOwnership(instituteId, domain) {
    try {
      const result = await query(`
        SELECT * FROM institute_domains 
        WHERE institute_id = $1 AND domain = $2 AND is_active = TRUE
      `, [instituteId, domain]);

      return result.rows.length > 0;

    } catch (error) {
      console.error('Failed to validate domain ownership:', error.message);
      return false;
    }
  }
}

module.exports = new InstituteAdminService();
