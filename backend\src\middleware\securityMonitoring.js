const securityService = require('../services/securityService');
const { query } = require('../database/connection');

/**
 * Security Monitoring Middleware
 * Monitors requests for security threats and logs suspicious activities
 */

/**
 * IP blocking middleware - checks if IP is blocked
 */
const checkBlockedIP = async (req, res, next) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    const blockInfo = await securityService.isIPBlocked(clientIP);
    
    if (blockInfo) {
      await securityService.logSecurityEvent({
        eventType: 'blocked_ip_access_attempt',
        severity: 'high',
        ipAddress: clientIP,
        userAgent: req.get('User-Agent'),
        endpoint: req.path,
        details: {
          reason: blockInfo.reason,
          expiresAt: blockInfo.expires_at
        }
      });

      return res.status(403).json({
        success: false,
        error: 'Access denied. Your IP address has been temporarily blocked.',
        code: 'IP_BLOCKED',
        retryAfter: Math.ceil((new Date(blockInfo.expires_at) - new Date()) / 1000)
      });
    }

    next();
  } catch (error) {
    console.error('IP blocking check error:', error.message);
    next(); // Continue on error to avoid blocking legitimate users
  }
};

/**
 * Failed login attempt tracking
 */
const trackFailedLogin = async (email, req, reason = 'invalid_credentials') => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');
    const instituteId = req.tenant?.instituteId || null;

    // Log failed attempt
    await query(`
      INSERT INTO failed_login_attempts (
        email, ip_address, user_agent, reason, institute_id
      )
      VALUES ($1, $2, $3, $4, $5)
    `, [email, clientIP, userAgent, reason, instituteId]);

    // Log security event
    await securityService.logSecurityEvent({
      eventType: 'failed_login',
      severity: 'medium',
      ipAddress: clientIP,
      userAgent,
      endpoint: req.path,
      details: {
        email,
        reason,
        instituteId
      }
    });

    // Check for brute force patterns
    const bruteForceCheck = await securityService.detectBruteForce(
      email,
      'failed_login',
      15 * 60 * 1000, // 15 minutes
      5 // 5 attempts
    );

    if (bruteForceCheck.detected) {
      console.warn(`Brute force attack detected for email: ${email} from IP: ${clientIP}`);
    }

  } catch (error) {
    console.error('Failed login tracking error:', error.message);
  }
};

/**
 * Successful login tracking
 */
const trackSuccessfulLogin = async (user, req) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    // Log successful login
    await securityService.logSecurityEvent({
      eventType: 'successful_login',
      severity: 'low',
      ipAddress: clientIP,
      userAgent,
      userId: user.id,
      endpoint: req.path,
      details: {
        email: user.email,
        role: user.role,
        instituteId: user.institute_id
      }
    });

    // Check for unusual login patterns
    await checkUnusualLoginPattern(user.id, clientIP, userAgent);

  } catch (error) {
    console.error('Successful login tracking error:', error.message);
  }
};

/**
 * Check for unusual login patterns
 */
const checkUnusualLoginPattern = async (userId, ipAddress, userAgent) => {
  try {
    // Check if this is a new IP for this user
    const recentLogins = await query(`
      SELECT DISTINCT ip_address
      FROM security_audit_log
      WHERE user_id = $1
      AND event_type = 'successful_login'
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '30 days'
    `, [userId]);

    const knownIPs = recentLogins.rows.map(row => row.ip_address);
    const isNewIP = !knownIPs.includes(ipAddress);

    if (isNewIP && knownIPs.length > 0) {
      await securityService.logSecurityEvent({
        eventType: 'unusual_login_location',
        severity: 'medium',
        ipAddress,
        userAgent,
        userId,
        details: {
          newIP: ipAddress,
          knownIPs: knownIPs.slice(0, 5), // Limit to last 5 IPs
          isFirstTimeIP: true
        }
      });
    }

    // Check for rapid login attempts from different IPs
    const recentLoginIPs = await query(`
      SELECT ip_address, COUNT(*) as count
      FROM security_audit_log
      WHERE user_id = $1
      AND event_type = 'successful_login'
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour'
      GROUP BY ip_address
    `, [userId]);

    if (recentLoginIPs.rows.length > 3) {
      await securityService.logSecurityEvent({
        eventType: 'rapid_login_multiple_ips',
        severity: 'high',
        ipAddress,
        userAgent,
        userId,
        details: {
          uniqueIPs: recentLoginIPs.rows.length,
          ipCounts: recentLoginIPs.rows
        }
      });
    }

  } catch (error) {
    console.error('Unusual login pattern check error:', error.message);
  }
};

/**
 * Session security tracking
 */
const trackSessionSecurity = async (sessionId, user, req) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    // Calculate risk score based on various factors
    let riskScore = 0;

    // Check if IP is from a different country (simplified check)
    const isUnusualLocation = await checkUnusualLocation(user.id, clientIP);
    if (isUnusualLocation) riskScore += 30;

    // Check user agent consistency
    const isUnusualUserAgent = await checkUnusualUserAgent(user.id, userAgent);
    if (isUnusualUserAgent) riskScore += 20;

    // Check login time patterns
    const isUnusualTime = await checkUnusualLoginTime(user.id);
    if (isUnusualTime) riskScore += 15;

    const isSuspicious = riskScore >= 50;

    await query(`
      INSERT INTO session_security (
        session_id, user_id, ip_address, user_agent, 
        is_suspicious, risk_score
      )
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (session_id)
      DO UPDATE SET
        last_activity = CURRENT_TIMESTAMP,
        risk_score = EXCLUDED.risk_score,
        is_suspicious = EXCLUDED.is_suspicious
    `, [sessionId, user.id, clientIP, userAgent, isSuspicious, riskScore]);

    if (isSuspicious) {
      await securityService.logSecurityEvent({
        eventType: 'suspicious_session',
        severity: 'high',
        ipAddress: clientIP,
        userAgent,
        userId: user.id,
        details: {
          sessionId,
          riskScore,
          factors: {
            unusualLocation: isUnusualLocation,
            unusualUserAgent: isUnusualUserAgent,
            unusualTime: isUnusualTime
          }
        }
      });
    }

  } catch (error) {
    console.error('Session security tracking error:', error.message);
  }
};

/**
 * Check for unusual location (simplified implementation)
 */
const checkUnusualLocation = async (userId, ipAddress) => {
  try {
    // In a real implementation, you would use a GeoIP service
    // For now, we'll check if this IP has been used before
    const knownIPs = await query(`
      SELECT ip_address
      FROM session_security
      WHERE user_id = $1
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '30 days'
      GROUP BY ip_address
    `, [userId]);

    const knownIPList = knownIPs.rows.map(row => row.ip_address);
    return !knownIPList.includes(ipAddress) && knownIPList.length > 0;

  } catch (error) {
    console.error('Unusual location check error:', error.message);
    return false;
  }
};

/**
 * Check for unusual user agent
 */
const checkUnusualUserAgent = async (userId, userAgent) => {
  try {
    const knownUserAgents = await query(`
      SELECT user_agent
      FROM session_security
      WHERE user_id = $1
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '30 days'
      GROUP BY user_agent
    `, [userId]);

    const knownAgents = knownUserAgents.rows.map(row => row.user_agent);
    return !knownAgents.includes(userAgent) && knownAgents.length > 0;

  } catch (error) {
    console.error('Unusual user agent check error:', error.message);
    return false;
  }
};

/**
 * Check for unusual login time
 */
const checkUnusualLoginTime = async (userId) => {
  try {
    const currentHour = new Date().getHours();
    
    // Get user's typical login hours
    const loginHours = await query(`
      SELECT EXTRACT(HOUR FROM created_at) as hour, COUNT(*) as count
      FROM security_audit_log
      WHERE user_id = $1
      AND event_type = 'successful_login'
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '30 days'
      GROUP BY EXTRACT(HOUR FROM created_at)
      ORDER BY count DESC
    `, [userId]);

    if (loginHours.rows.length === 0) {
      return false; // No history to compare
    }

    // Check if current hour is in the top 50% of login hours
    const totalLogins = loginHours.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
    const currentHourLogins = loginHours.rows.find(row => parseInt(row.hour) === currentHour);
    
    if (!currentHourLogins) {
      return true; // Never logged in at this hour before
    }

    const currentHourPercentage = (parseInt(currentHourLogins.count) / totalLogins) * 100;
    return currentHourPercentage < 10; // Less than 10% of logins at this hour

  } catch (error) {
    console.error('Unusual login time check error:', error.message);
    return false;
  }
};

/**
 * Rate limit violation tracking
 */
const trackRateLimitViolation = async (req, endpoint, windowStart, windowEnd) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;

    await query(`
      INSERT INTO rate_limit_violations (
        ip_address, endpoint, window_start, window_end
      )
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (ip_address, endpoint, window_start)
      DO UPDATE SET
        violation_count = rate_limit_violations.violation_count + 1
    `, [clientIP, endpoint, windowStart, windowEnd]);

    await securityService.logSecurityEvent({
      eventType: 'rate_limit_violation',
      severity: 'medium',
      ipAddress: clientIP,
      userAgent: req.get('User-Agent'),
      endpoint,
      details: {
        windowStart,
        windowEnd
      }
    });

  } catch (error) {
    console.error('Rate limit violation tracking error:', error.message);
  }
};

module.exports = {
  checkBlockedIP,
  trackFailedLogin,
  trackSuccessfulLogin,
  trackSessionSecurity,
  trackRateLimitViolation,
  checkUnusualLoginPattern
};
