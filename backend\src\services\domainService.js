const { query, transaction } = require('../database/connection');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const dns = require('dns').promises;

/**
 * Domain Management Service
 * Handles custom domain registration, verification, and SSL certificate management
 */

class DomainService {
  /**
   * Add a custom domain for an institute
   */
  async addCustomDomain(instituteId, domainData) {
    const { domain, domainType = 'custom', isPrimary = false } = domainData;

    try {
      // Validate domain format
      if (!this.isValidDomain(domain)) {
        throw new Error('Invalid domain format');
      }

      // Check if domain already exists
      const existingDomain = await query(
        'SELECT id FROM institute_domains WHERE domain = $1',
        [domain.toLowerCase()]
      );

      if (existingDomain.rows.length > 0) {
        throw new Error('Domain already registered');
      }

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationRecord = `lms-verify=${verificationToken}`;

      const result = await transaction(async (client) => {
        // If this is set as primary, unset other primary domains
        if (isPrimary) {
          await client.query(
            'UPDATE institute_domains SET is_primary = FALSE WHERE institute_id = $1',
            [instituteId]
          );
        }

        // Insert new domain
        const domainResult = await client.query(`
          INSERT INTO institute_domains (
            institute_id, domain, domain_type, verification_token, 
            verification_record, is_primary
          )
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `, [
          instituteId,
          domain.toLowerCase(),
          domainType,
          verificationToken,
          verificationRecord,
          isPrimary
        ]);

        return domainResult.rows[0];
      });

      return {
        domain: result,
        verificationInstructions: this.getVerificationInstructions(result)
      };

    } catch (error) {
      console.error('Add custom domain error:', error.message);
      throw error;
    }
  }

  /**
   * Get verification instructions for a domain
   */
  getVerificationInstructions(domainRecord) {
    return {
      method: 'DNS TXT Record',
      domain: domainRecord.domain,
      recordType: 'TXT',
      recordName: '_lms-verification',
      recordValue: domainRecord.verification_record,
      instructions: [
        '1. Log in to your domain registrar or DNS provider',
        '2. Navigate to DNS management for your domain',
        '3. Add a new TXT record with the following details:',
        `   - Name: _lms-verification`,
        `   - Value: ${domainRecord.verification_record}`,
        '4. Save the DNS record and wait for propagation (up to 24 hours)',
        '5. Click "Verify Domain" to check verification status'
      ],
      estimatedPropagationTime: '5-60 minutes (up to 24 hours)',
      verificationToken: domainRecord.verification_token
    };
  }

  /**
   * Verify domain ownership via DNS TXT record
   */
  async verifyDomainOwnership(domainId) {
    try {
      // Get domain record
      const domainResult = await query(
        'SELECT * FROM institute_domains WHERE id = $1',
        [domainId]
      );

      if (domainResult.rows.length === 0) {
        throw new Error('Domain not found');
      }

      const domainRecord = domainResult.rows[0];

      if (domainRecord.is_verified) {
        return { verified: true, message: 'Domain already verified' };
      }

      // Check DNS TXT record
      const verificationResult = await this.checkDNSVerification(
        domainRecord.domain,
        domainRecord.verification_record
      );

      // Log verification attempt
      await this.logVerificationAttempt(domainId, 'dns_txt', verificationResult);

      if (verificationResult.verified) {
        // Update domain as verified
        await query(`
          UPDATE institute_domains 
          SET is_verified = TRUE, verified_at = CURRENT_TIMESTAMP, 
              verification_attempts = verification_attempts + 1,
              last_verification_check = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [domainId]);

        // Initiate SSL certificate request
        await this.requestSSLCertificate(domainId);

        return {
          verified: true,
          message: 'Domain verified successfully! SSL certificate request initiated.',
          nextSteps: ['SSL certificate will be issued automatically', 'Domain will be activated once SSL is ready']
        };
      } else {
        // Update verification attempt count
        await query(`
          UPDATE institute_domains 
          SET verification_attempts = verification_attempts + 1,
              last_verification_check = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [domainId]);

        return {
          verified: false,
          message: 'Domain verification failed',
          error: verificationResult.error,
          troubleshooting: [
            'Ensure the TXT record is added correctly',
            'Wait for DNS propagation (up to 24 hours)',
            'Check with your DNS provider for any issues',
            'Verify the record name is exactly: _lms-verification'
          ]
        };
      }

    } catch (error) {
      console.error('Domain verification error:', error.message);
      throw error;
    }
  }

  /**
   * Check DNS TXT record for verification
   */
  async checkDNSVerification(domain, expectedRecord) {
    try {
      const txtRecords = await dns.resolveTxt(`_lms-verification.${domain}`);
      
      // Flatten TXT records (they come as arrays of arrays)
      const flatRecords = txtRecords.flat();
      
      // Check if our verification record exists
      const verified = flatRecords.some(record => record === expectedRecord);

      return {
        verified,
        records: flatRecords,
        expectedRecord
      };

    } catch (error) {
      return {
        verified: false,
        error: error.code === 'ENOTFOUND' ? 'TXT record not found' : error.message,
        records: []
      };
    }
  }

  /**
   * Log verification attempt
   */
  async logVerificationAttempt(domainId, verificationType, result) {
    try {
      await query(`
        INSERT INTO domain_verification_logs (
          domain_id, verification_type, verification_status, 
          verification_details, error_message
        )
        VALUES ($1, $2, $3, $4, $5)
      `, [
        domainId,
        verificationType,
        result.verified ? 'success' : 'failed',
        JSON.stringify(result),
        result.error || null
      ]);
    } catch (error) {
      console.warn('Failed to log verification attempt:', error.message);
    }
  }

  /**
   * Request SSL certificate for verified domain
   */
  async requestSSLCertificate(domainId) {
    try {
      // Get domain record
      const domainResult = await query(
        'SELECT * FROM institute_domains WHERE id = $1 AND is_verified = TRUE',
        [domainId]
      );

      if (domainResult.rows.length === 0) {
        throw new Error('Domain not found or not verified');
      }

      const domainRecord = domainResult.rows[0];

      // For now, simulate SSL certificate request
      // In production, this would integrate with Let's Encrypt ACME protocol
      const certificateId = `cert_${Date.now()}_${domainRecord.domain}`;
      
      // Create SSL certificate record
      const sslResult = await query(`
        INSERT INTO ssl_certificates (
          domain_id, certificate_authority, issued_at, expires_at, status
        )
        VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '90 days', $3)
        RETURNING *
      `, [domainId, 'letsencrypt', 'issued']);

      // Update domain with SSL information
      await query(`
        UPDATE institute_domains 
        SET ssl_certificate_id = $1, ssl_status = $2, 
            ssl_expires_at = CURRENT_TIMESTAMP + INTERVAL '90 days',
            is_active = TRUE, activated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [certificateId, 'issued', domainId]);

      return {
        success: true,
        certificateId,
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        message: 'SSL certificate issued successfully'
      };

    } catch (error) {
      console.error('SSL certificate request error:', error.message);
      
      // Update SSL status to failed
      await query(
        'UPDATE institute_domains SET ssl_status = $1 WHERE id = $2',
        ['failed', domainId]
      );

      throw error;
    }
  }

  /**
   * Get all domains for an institute
   */
  async getInstituteDomains(instituteId) {
    try {
      const result = await query(`
        SELECT 
          d.*,
          s.expires_at as ssl_expires_at,
          s.status as ssl_certificate_status
        FROM institute_domains d
        LEFT JOIN ssl_certificates s ON d.id = s.domain_id
        WHERE d.institute_id = $1
        ORDER BY d.is_primary DESC, d.created_at ASC
      `, [instituteId]);

      return result.rows;
    } catch (error) {
      console.error('Get institute domains error:', error.message);
      throw error;
    }
  }

  /**
   * Get domain by domain name
   */
  async getDomainByName(domain) {
    try {
      const result = await query(`
        SELECT 
          d.*,
          i.name as institute_name,
          i.slug as institute_slug
        FROM institute_domains d
        JOIN institutes i ON d.institute_id = i.id
        WHERE d.domain = $1 AND d.is_active = TRUE
      `, [domain.toLowerCase()]);

      return result.rows[0] || null;
    } catch (error) {
      console.error('Get domain by name error:', error.message);
      throw error;
    }
  }

  /**
   * Remove a custom domain
   */
  async removeDomain(domainId, instituteId) {
    try {
      const result = await transaction(async (client) => {
        // Check if domain belongs to institute
        const domainResult = await client.query(
          'SELECT * FROM institute_domains WHERE id = $1 AND institute_id = $2',
          [domainId, instituteId]
        );

        if (domainResult.rows.length === 0) {
          throw new Error('Domain not found or access denied');
        }

        const domain = domainResult.rows[0];

        // Cannot remove primary domain if it's the only one
        if (domain.is_primary) {
          const otherDomainsResult = await client.query(
            'SELECT COUNT(*) FROM institute_domains WHERE institute_id = $1 AND id != $2',
            [instituteId, domainId]
          );

          if (parseInt(otherDomainsResult.rows[0].count) === 0) {
            throw new Error('Cannot remove the only domain for the institute');
          }
        }

        // Delete SSL certificates
        await client.query('DELETE FROM ssl_certificates WHERE domain_id = $1', [domainId]);

        // Delete verification logs
        await client.query('DELETE FROM domain_verification_logs WHERE domain_id = $1', [domainId]);

        // Delete domain
        await client.query('DELETE FROM institute_domains WHERE id = $1', [domainId]);

        return domain;
      });

      return { success: true, removedDomain: result.domain };

    } catch (error) {
      console.error('Remove domain error:', error.message);
      throw error;
    }
  }

  /**
   * Validate domain format
   */
  isValidDomain(domain) {
    // Check overall length
    if (domain.length > 253) {
      return false;
    }

    // Check for valid domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;

    // Check each label length (max 63 characters per label)
    const labels = domain.split('.');
    for (const label of labels) {
      if (label.length > 63) {
        return false;
      }
    }

    return domainRegex.test(domain);
  }

  /**
   * Check SSL certificate expiration and renewal
   */
  async checkSSLRenewal() {
    try {
      // Get certificates expiring in the next 30 days
      const expiringCerts = await query(`
        SELECT d.*, s.* 
        FROM institute_domains d
        JOIN ssl_certificates s ON d.id = s.domain_id
        WHERE s.expires_at <= CURRENT_TIMESTAMP + INTERVAL '30 days'
        AND s.auto_renew = TRUE
        AND s.status = 'issued'
      `);

      const renewalResults = [];

      for (const cert of expiringCerts.rows) {
        try {
          // Attempt to renew certificate
          const renewalResult = await this.renewSSLCertificate(cert.domain_id);
          renewalResults.push({
            domain: cert.domain,
            success: true,
            result: renewalResult
          });
        } catch (error) {
          renewalResults.push({
            domain: cert.domain,
            success: false,
            error: error.message
          });
        }
      }

      return renewalResults;

    } catch (error) {
      console.error('SSL renewal check error:', error.message);
      throw error;
    }
  }

  /**
   * Renew SSL certificate
   */
  async renewSSLCertificate(domainId) {
    try {
      // For now, simulate certificate renewal
      // In production, this would use Let's Encrypt ACME protocol
      const newCertificateId = `cert_renewed_${Date.now()}`;
      
      await transaction(async (client) => {
        // Update SSL certificate
        await client.query(`
          UPDATE ssl_certificates 
          SET issued_at = CURRENT_TIMESTAMP,
              expires_at = CURRENT_TIMESTAMP + INTERVAL '90 days',
              renewal_attempts = renewal_attempts + 1,
              last_renewal_attempt = CURRENT_TIMESTAMP
          WHERE domain_id = $1
        `, [domainId]);

        // Update domain SSL info
        await client.query(`
          UPDATE institute_domains 
          SET ssl_certificate_id = $1,
              ssl_expires_at = CURRENT_TIMESTAMP + INTERVAL '90 days',
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [newCertificateId, domainId]);
      });

      return {
        success: true,
        certificateId: newCertificateId,
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
      };

    } catch (error) {
      console.error('SSL certificate renewal error:', error.message);
      throw error;
    }
  }
}

module.exports = new DomainService();
