version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: lms-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: lte_lms_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1234
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - lms-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d lte_lms_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Development
  redis-dev:
    image: redis:7-alpine
    container_name: lms-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - lms-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MailHog for Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: lms-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - lms-dev-network

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: lms-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres-dev:
        condition: service_healthy
    networks:
      - lms-dev-network

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: lms-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      redis-dev:
        condition: service_healthy
    networks:
      - lms-dev-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  lms-dev-network:
    driver: bridge
