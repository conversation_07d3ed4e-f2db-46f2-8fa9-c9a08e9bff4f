'use client'

import React, { useState } from 'react'
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  Download,
  Mail,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { DataTable, TableColumn } from '@/components/ui/data-table'
import { Modal } from '@/components/ui/modal'
import { cn } from '@/lib/utils'

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  studentId: string
  major: string
  enrollmentDate: string
  status: 'active' | 'inactive' | 'suspended'
  gpa: number | null
  coursesEnrolled: number
}

const mockStudents: Student[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    studentId: 'STU001',
    major: 'Computer Science',
    enrollmentDate: '2023-09-01',
    status: 'active',
    gpa: 3.8,
    coursesEnrolled: 5,
  },
  {
    id: '2',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    studentId: 'STU002',
    major: 'Mathematics',
    enrollmentDate: '2023-09-01',
    status: 'active',
    gpa: 3.9,
    coursesEnrolled: 4,
  },
  {
    id: '3',
    firstName: 'Mike',
    lastName: 'Davis',
    email: '<EMAIL>',
    studentId: 'STU003',
    major: 'Physics',
    enrollmentDate: '2023-08-15',
    status: 'inactive',
    gpa: 3.2,
    coursesEnrolled: 3,
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Chen',
    email: '<EMAIL>',
    studentId: 'STU004',
    major: 'Biology',
    enrollmentDate: '2023-09-05',
    status: 'active',
    gpa: 4.0,
    coursesEnrolled: 6,
  },
  {
    id: '5',
    firstName: 'David',
    lastName: 'Wilson',
    email: '<EMAIL>',
    studentId: 'STU005',
    major: 'Chemistry',
    enrollmentDate: '2023-08-20',
    status: 'suspended',
    gpa: 2.8,
    coursesEnrolled: 2,
  },
]

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>(mockStudents)
  const [selectedStudents, setSelectedStudents] = useState<Set<string | number>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)

  const getStatusBadge = (status: Student['status']) => {
    const styles = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      suspended: 'bg-red-100 text-red-800',
    }

    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', styles[status])}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const columns: TableColumn<Student>[] = [
    {
      key: 'studentId',
      label: 'Student ID',
      sortable: true,
      priority: 'high',
    },
    {
      key: 'firstName',
      label: 'Name',
      sortable: true,
      priority: 'high',
      render: (_, student) => (
        <div>
          <div className="font-medium text-gray-900">
            {student.firstName} {student.lastName}
          </div>
          <div className="text-sm text-gray-500">{student.email}</div>
        </div>
      ),
    },
    {
      key: 'major',
      label: 'Major',
      sortable: true,
      priority: 'medium',
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      priority: 'high',
      render: (value) => getStatusBadge(value as Student['status']),
    },
    {
      key: 'gpa',
      label: 'GPA',
      sortable: true,
      priority: 'medium',
      render: (value) => value ? value.toFixed(2) : 'N/A',
    },
    {
      key: 'coursesEnrolled',
      label: 'Courses',
      sortable: true,
      priority: 'low',
    },
    {
      key: 'enrollmentDate',
      label: 'Enrolled',
      sortable: true,
      priority: 'low',
      render: (value) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'actions',
      label: 'Actions',
      priority: 'high',
      render: (_, student) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSelectedStudent(student)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  const filteredStudents = students.filter(student => {
    const matchesSearch = 
      student.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.studentId.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || student.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column)
    setSortDirection(direction)

    const sorted = [...filteredStudents].sort((a, b) => {
      const aValue = a[column as keyof Student]
      const bValue = b[column as keyof Student]

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? -1 : 1
      if (bValue == null) return direction === 'asc' ? 1 : -1

      if (direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    setStudents(sorted)
  }

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} for students:`, Array.from(selectedStudents))
    // TODO: Implement bulk actions
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Students</h1>
          <p className="text-gray-600 mt-1">
            Manage student accounts and enrollment
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add Student
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Students</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.filter(s => s.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Inactive</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.filter(s => s.status === 'inactive').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-red-600 rounded-full"></div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Suspended</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.filter(s => s.status === 'suspended').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedStudents.size > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedStudents.size} student(s) selected
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('email')}
                >
                  <Mail className="mr-1 h-3 w-3" />
                  Email
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('suspend')}
                >
                  Suspend
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('delete')}
                >
                  <Trash2 className="mr-1 h-3 w-3" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Students Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <DataTable
          data={filteredStudents}
          columns={columns}
          selectable
          selectedRows={selectedStudents}
          onSelectionChange={setSelectedStudents}
          getRowId={(student) => student.id}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onSort={handleSort}
          emptyMessage="No students found"
        />
      </div>

      {/* Add Student Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Student"
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Add a new student to your institute. They will receive an email invitation to set up their account.
          </p>
          
          {/* Form would go here */}
          <div className="p-8 border-2 border-dashed border-gray-300 rounded-lg text-center">
            <p className="text-gray-500">
              Student registration form will be implemented here
            </p>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              Cancel
            </Button>
            <Button>
              Send Invitation
            </Button>
          </div>
        </div>
      </Modal>

      {/* Student Details Modal */}
      <Modal
        isOpen={!!selectedStudent}
        onClose={() => setSelectedStudent(null)}
        title="Student Details"
        size="lg"
      >
        {selectedStudent && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Name</label>
                <p className="text-gray-900">{selectedStudent.firstName} {selectedStudent.lastName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Student ID</label>
                <p className="text-gray-900">{selectedStudent.studentId}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="text-gray-900">{selectedStudent.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Major</label>
                <p className="text-gray-900">{selectedStudent.major}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Status</label>
                <div className="mt-1">{getStatusBadge(selectedStudent.status)}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">GPA</label>
                <p className="text-gray-900">{selectedStudent.gpa?.toFixed(2) || 'N/A'}</p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setSelectedStudent(null)}>
                Close
              </Button>
              <Button>
                Edit Student
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}
