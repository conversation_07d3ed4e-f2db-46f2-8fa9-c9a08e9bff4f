const { Pool } = require('pg');

/**
 * User Profile Management Tables Migration
 * Creates tables for student profiles, teacher profiles, and teacher invitations
 */

async function migrateProfileTables() {
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'lte_lms',
    password: process.env.DB_PASSWORD || '1234',
    port: process.env.DB_PORT || 5432,
  });

  try {
    console.log('🚀 Starting user profile tables migration...');

    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connected successfully');
    console.log(`📊 Connected to database: ${client.database}`);
    console.log(`🏠 Host: ${client.host}:${client.port}`);
    client.release();

    // Profile management tables SQL
    const profileTablesSQL = `
-- =============================================
-- USER PROFILE MANAGEMENT TABLES
-- =============================================

-- Student profiles table
CREATE TABLE IF NOT EXISTS student_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    student_id VARCHAR(50), -- Institute-specific student ID
    enrollment_date DATE DEFAULT CURRENT_DATE,
    graduation_year INTEGER,
    major VARCHAR(100),
    gpa DECIMAL(3,2), -- Grade Point Average
    academic_status VARCHAR(20) DEFAULT 'active', -- 'active', 'probation', 'suspended', 'graduated', 'withdrawn'
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(50),
    address TEXT,
    date_of_birth DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teacher profiles table
CREATE TABLE IF NOT EXISTS teacher_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    employee_id VARCHAR(50), -- Institute-specific employee ID
    department VARCHAR(100),
    specialization VARCHAR(255),
    hire_date DATE DEFAULT CURRENT_DATE,
    office_location VARCHAR(100),
    office_hours VARCHAR(255),
    bio TEXT,
    qualifications TEXT,
    salary DECIMAL(10,2),
    employment_status VARCHAR(20) DEFAULT 'active', -- 'active', 'on_leave', 'terminated'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teacher invitations table
CREATE TABLE IF NOT EXISTS teacher_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    invited_by UUID NOT NULL REFERENCES users(id),
    department VARCHAR(100),
    message TEXT,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP,
    used_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Course enrollments table (basic structure for future use)
CREATE TABLE IF NOT EXISTS course_enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL, -- Will reference courses table when created
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'dropped', 'failed'
    grade VARCHAR(5), -- Letter grade (A, B, C, D, F)
    grade_points DECIMAL(3,2), -- Numeric grade
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_accessed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User profile indexes
CREATE INDEX IF NOT EXISTS idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_student_profiles_student_id ON student_profiles(student_id);
CREATE INDEX IF NOT EXISTS idx_student_profiles_academic_status ON student_profiles(academic_status);
CREATE INDEX IF NOT EXISTS idx_student_profiles_graduation_year ON student_profiles(graduation_year);

CREATE INDEX IF NOT EXISTS idx_teacher_profiles_user_id ON teacher_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_teacher_profiles_employee_id ON teacher_profiles(employee_id);
CREATE INDEX IF NOT EXISTS idx_teacher_profiles_department ON teacher_profiles(department);
CREATE INDEX IF NOT EXISTS idx_teacher_profiles_employment_status ON teacher_profiles(employment_status);

CREATE INDEX IF NOT EXISTS idx_teacher_invitations_institute_id ON teacher_invitations(institute_id);
CREATE INDEX IF NOT EXISTS idx_teacher_invitations_email ON teacher_invitations(email);
CREATE INDEX IF NOT EXISTS idx_teacher_invitations_token ON teacher_invitations(token);
CREATE INDEX IF NOT EXISTS idx_teacher_invitations_expires_at ON teacher_invitations(expires_at);

CREATE INDEX IF NOT EXISTS idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_student_id ON course_enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_course_enrollments_status ON course_enrollments(status);
`;

    console.log('📄 Executing user profile tables SQL...');

    // Execute the SQL
    await pool.query(profileTablesSQL);

    console.log('✅ User profile tables migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - student_profiles');
    console.log('   - teacher_profiles');
    console.log('   - teacher_invitations');
    console.log('   - course_enrollments');
    console.log('🔍 Created indexes for performance optimization');

  } catch (error) {
    console.error('❌ Migration error:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('⚠️  Tables already exist, skipping creation');
    } else {
      throw error;
    }
  } finally {
    console.log('🔒 Database pool closed');
    await pool.end();
  }
}

// Run migration if script is executed directly
if (require.main === module) {
  migrateProfileTables()
    .then(() => {
      console.log('🏁 User profile migration completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { migrateProfileTables };
