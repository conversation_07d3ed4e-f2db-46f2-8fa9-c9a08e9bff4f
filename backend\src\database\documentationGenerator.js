const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

/**
 * Database Documentation Generator
 * Generates comprehensive documentation for the LMS SAAS database schema
 */

class DatabaseDocumentationGenerator {
  constructor() {
    this.pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'lte_lms',
      password: process.env.DB_PASSWORD || '1234',
      port: process.env.DB_PORT || 5432,
    });
  }

  /**
   * Get comprehensive table information
   */
  async getTableInformation() {
    const tablesQuery = `
      SELECT 
        t.table_name,
        t.table_type,
        obj_description(c.oid) as table_comment
      FROM information_schema.tables t
      LEFT JOIN pg_class c ON c.relname = t.table_name
      WHERE t.table_schema = 'public'
      AND t.table_type = 'BASE TABLE'
      ORDER BY t.table_name;
    `;

    const columnsQuery = `
      SELECT 
        c.table_name,
        c.column_name,
        c.data_type,
        c.character_maximum_length,
        c.numeric_precision,
        c.numeric_scale,
        c.is_nullable,
        c.column_default,
        col_description(pgc.oid, c.ordinal_position) as column_comment
      FROM information_schema.columns c
      LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
      WHERE c.table_schema = 'public'
      ORDER BY c.table_name, c.ordinal_position;
    `;

    const constraintsQuery = `
      SELECT 
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        rc.update_rule,
        rc.delete_rule
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      LEFT JOIN information_schema.constraint_column_usage ccu 
        ON ccu.constraint_name = tc.constraint_name
      LEFT JOIN information_schema.referential_constraints rc 
        ON tc.constraint_name = rc.constraint_name
      WHERE tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;
    `;

    const indexesQuery = `
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname;
    `;

    try {
      const [tablesResult, columnsResult, constraintsResult, indexesResult] = await Promise.all([
        this.pool.query(tablesQuery),
        this.pool.query(columnsQuery),
        this.pool.query(constraintsQuery),
        this.pool.query(indexesQuery)
      ]);

      return {
        tables: tablesResult.rows,
        columns: columnsResult.rows,
        constraints: constraintsResult.rows,
        indexes: indexesResult.rows
      };
    } catch (error) {
      console.error('Error getting table information:', error.message);
      throw error;
    }
  }

  /**
   * Generate Markdown documentation
   */
  generateMarkdownDocumentation(schemaInfo) {
    let markdown = `# LMS SAAS Database Schema Documentation

Generated on: ${new Date().toISOString()}

## Overview

This document provides comprehensive documentation for the LMS SAAS PostgreSQL database schema. The database is designed with multi-tenancy in mind, using institute-based data isolation for security and scalability.

## Database Architecture

### Multi-Tenancy Strategy
- **Institute-based isolation**: All tenant-specific data is linked to an \`institute_id\`
- **Shared schema**: Single database schema shared across all tenants
- **Data isolation**: Foreign key constraints and application-level filtering ensure data separation
- **Security**: Role-based access control (RBAC) with granular permissions

### Key Features
- UUID primary keys for better distribution and security
- Comprehensive audit logging for security and compliance
- Flexible user role system with JSON-based permissions
- Custom domain support with SSL certificate management
- Session management with security tracking

## Tables Overview

| Table Name | Purpose | Records |
|------------|---------|---------|
`;

    // Group columns by table
    const tableColumns = {};
    schemaInfo.columns.forEach(col => {
      if (!tableColumns[col.table_name]) {
        tableColumns[col.table_name] = [];
      }
      tableColumns[col.table_name].push(col);
    });

    // Group constraints by table
    const tableConstraints = {};
    schemaInfo.constraints.forEach(constraint => {
      if (!tableConstraints[constraint.table_name]) {
        tableConstraints[constraint.table_name] = [];
      }
      tableConstraints[constraint.table_name].push(constraint);
    });

    // Group indexes by table
    const tableIndexes = {};
    schemaInfo.indexes.forEach(index => {
      if (!tableIndexes[index.tablename]) {
        tableIndexes[index.tablename] = [];
      }
      tableIndexes[index.tablename].push(index);
    });

    // Add table descriptions
    const tableDescriptions = {
      'institutes': 'Core tenant table storing institute information and subscription details',
      'users': 'All user types (super_admin, institute_admin, teacher, student) with multi-tenant isolation',
      'user_roles': 'RBAC system with JSON-based permissions for flexible access control',
      'user_sessions': 'Session management with security tracking and automatic cleanup',
      'email_verifications': 'Email verification and password reset token management',
      'institute_settings': 'Flexible key-value settings storage per institute',
      'institute_domains': 'Custom domain management with verification and SSL support',
      'domain_verification_logs': 'Audit trail for domain verification attempts',
      'ssl_certificates': 'SSL certificate management and auto-renewal tracking',
      'institute_branding': 'Institute-specific branding and customization settings',
      'student_profiles': 'Extended student information and academic tracking',
      'teacher_profiles': 'Teacher-specific information and employment details',
      'teacher_invitations': 'Teacher invitation system with token-based verification',
      'course_enrollments': 'Student course enrollment tracking (basic structure)',
      'security_audit_log': 'Comprehensive security event logging and monitoring',
      'blocked_ips': 'IP blocking system for security threat mitigation',
      'failed_login_attempts': 'Failed login tracking for brute force detection',
      'two_factor_auth': 'Two-factor authentication settings and backup codes',
      'subscription_history': 'Audit trail for subscription plan changes',
      'institute_status_history': 'Institute activation/deactivation tracking',
      'platform_analytics_cache': 'Cached analytics data for performance optimization',
      'super_admin_activity_log': 'Super admin action audit trail',
      'platform_notifications': 'System-wide notification management',
      'maintenance_windows': 'Scheduled maintenance tracking',
      'institute_usage_stats': 'Detailed usage statistics per institute',
      'platform_feature_flags': 'Feature rollout and A/B testing management'
    };

    // Add table overview
    schemaInfo.tables.forEach(table => {
      const description = tableDescriptions[table.table_name] || 'No description available';
      markdown += `| ${table.table_name} | ${description} | - |\n`;
    });

    markdown += `\n## Detailed Table Documentation\n\n`;

    // Generate detailed documentation for each table
    schemaInfo.tables.forEach(table => {
      const tableName = table.table_name;
      const columns = tableColumns[tableName] || [];
      const constraints = tableConstraints[tableName] || [];
      const indexes = tableIndexes[tableName] || [];

      markdown += `### ${tableName}\n\n`;
      markdown += `${tableDescriptions[tableName] || 'No description available'}\n\n`;

      // Columns
      markdown += `#### Columns\n\n`;
      markdown += `| Column | Type | Nullable | Default | Description |\n`;
      markdown += `|--------|------|----------|---------|-------------|\n`;

      columns.forEach(col => {
        const dataType = col.character_maximum_length 
          ? `${col.data_type}(${col.character_maximum_length})`
          : col.data_type;
        const nullable = col.is_nullable === 'YES' ? 'Yes' : 'No';
        const defaultValue = col.column_default || '-';
        const description = col.column_comment || '-';

        markdown += `| ${col.column_name} | ${dataType} | ${nullable} | ${defaultValue} | ${description} |\n`;
      });

      // Constraints
      if (constraints.length > 0) {
        markdown += `\n#### Constraints\n\n`;
        markdown += `| Name | Type | Columns | References |\n`;
        markdown += `|------|------|---------|------------|\n`;

        constraints.forEach(constraint => {
          const references = constraint.foreign_table_name 
            ? `${constraint.foreign_table_name}.${constraint.foreign_column_name}`
            : '-';
          
          markdown += `| ${constraint.constraint_name} | ${constraint.constraint_type} | ${constraint.column_name || '-'} | ${references} |\n`;
        });
      }

      // Indexes
      if (indexes.length > 0) {
        markdown += `\n#### Indexes\n\n`;
        markdown += `| Name | Definition |\n`;
        markdown += `|------|------------|\n`;

        indexes.forEach(index => {
          markdown += `| ${index.indexname} | \`${index.indexdef}\` |\n`;
        });
      }

      markdown += `\n---\n\n`;
    });

    // Add relationships section
    markdown += `## Table Relationships\n\n`;
    markdown += `### Foreign Key Relationships\n\n`;

    const foreignKeys = schemaInfo.constraints.filter(c => c.constraint_type === 'FOREIGN KEY');
    const relationships = {};

    foreignKeys.forEach(fk => {
      const key = `${fk.foreign_table_name} -> ${fk.table_name}`;
      if (!relationships[key]) {
        relationships[key] = [];
      }
      relationships[key].push(`${fk.foreign_column_name} -> ${fk.column_name}`);
    });

    Object.entries(relationships).forEach(([relationship, columns]) => {
      markdown += `- **${relationship}**: ${columns.join(', ')}\n`;
    });

    // Add multi-tenancy section
    markdown += `\n## Multi-Tenancy Implementation\n\n`;
    markdown += `### Tenant Isolation Strategy\n\n`;
    markdown += `1. **Institute-based isolation**: All tenant data is linked via \`institute_id\` foreign keys\n`;
    markdown += `2. **Application-level filtering**: All queries include institute context\n`;
    markdown += `3. **RBAC enforcement**: Role-based permissions prevent cross-tenant access\n`;
    markdown += `4. **Session management**: User sessions are scoped to specific institutes\n\n`;

    markdown += `### Tables with Multi-Tenant Data\n\n`;
    const multiTenantTables = schemaInfo.columns
      .filter(col => col.column_name === 'institute_id')
      .map(col => col.table_name);

    multiTenantTables.forEach(tableName => {
      markdown += `- **${tableName}**: Isolated by institute_id\n`;
    });

    // Add security section
    markdown += `\n## Security Features\n\n`;
    markdown += `### Audit Logging\n`;
    markdown += `- **security_audit_log**: Comprehensive security event tracking\n`;
    markdown += `- **super_admin_activity_log**: Super admin action audit trail\n`;
    markdown += `- **failed_login_attempts**: Brute force attack detection\n\n`;

    markdown += `### Access Control\n`;
    markdown += `- **user_roles**: Flexible RBAC with JSON permissions\n`;
    markdown += `- **blocked_ips**: Automated IP blocking for threats\n`;
    markdown += `- **two_factor_auth**: Enhanced authentication security\n\n`;

    markdown += `### Session Security\n`;
    markdown += `- **user_sessions**: Secure session management with expiration\n`;
    markdown += `- **email_verifications**: Token-based email verification\n`;
    markdown += `- IP address and user agent tracking for all sessions\n\n`;

    return markdown;
  }

  /**
   * Generate ER diagram in Mermaid format
   */
  generateMermaidERDiagram(schemaInfo) {
    let mermaid = `erDiagram\n\n`;

    // Define entities
    const tableColumns = {};
    schemaInfo.columns.forEach(col => {
      if (!tableColumns[col.table_name]) {
        tableColumns[col.table_name] = [];
      }
      tableColumns[col.table_name].push(col);
    });

    // Add entities with key columns
    Object.entries(tableColumns).forEach(([tableName, columns]) => {
      mermaid += `    ${tableName.toUpperCase()} {\n`;
      
      columns.slice(0, 8).forEach(col => { // Limit to first 8 columns for readability
        const type = col.data_type.toUpperCase();
        const nullable = col.is_nullable === 'YES' ? '' : ' NOT NULL';
        mermaid += `        ${type} ${col.column_name}${nullable}\n`;
      });
      
      if (columns.length > 8) {
        mermaid += `        ... "and ${columns.length - 8} more columns"\n`;
      }
      
      mermaid += `    }\n\n`;
    });

    // Add relationships
    const foreignKeys = schemaInfo.constraints.filter(c => c.constraint_type === 'FOREIGN KEY');
    
    foreignKeys.forEach(fk => {
      if (fk.foreign_table_name && fk.table_name) {
        const relationship = fk.delete_rule === 'CASCADE' ? '||--o{' : '||--||';
        mermaid += `    ${fk.foreign_table_name.toUpperCase()} ${relationship} ${fk.table_name.toUpperCase()} : "has"\n`;
      }
    });

    return mermaid;
  }

  /**
   * Generate complete documentation
   */
  async generateDocumentation() {
    console.log('📚 Generating database documentation...\n');

    try {
      console.log('🔍 Analyzing database schema...');
      const schemaInfo = await this.getTableInformation();

      console.log('📝 Generating Markdown documentation...');
      const markdownDoc = this.generateMarkdownDocumentation(schemaInfo);

      console.log('🎨 Generating ER diagram...');
      const mermaidDiagram = this.generateMermaidERDiagram(schemaInfo);

      // Create documentation directory
      const docsDir = path.join(__dirname, '../docs');
      await fs.mkdir(docsDir, { recursive: true });

      // Save documentation files
      const markdownPath = path.join(docsDir, 'database-schema.md');
      const mermaidPath = path.join(docsDir, 'er-diagram.mmd');
      const schemaJsonPath = path.join(docsDir, 'schema-info.json');

      await Promise.all([
        fs.writeFile(markdownPath, markdownDoc),
        fs.writeFile(mermaidPath, mermaidDiagram),
        fs.writeFile(schemaJsonPath, JSON.stringify(schemaInfo, null, 2))
      ]);

      console.log('\n📊 Documentation Summary:');
      console.log(`📊 Total Tables: ${schemaInfo.tables.length}`);
      console.log(`📋 Total Columns: ${schemaInfo.columns.length}`);
      console.log(`🔗 Total Constraints: ${schemaInfo.constraints.length}`);
      console.log(`📈 Total Indexes: ${schemaInfo.indexes.length}`);

      console.log('\n📄 Generated Files:');
      console.log(`📝 Markdown Documentation: ${markdownPath}`);
      console.log(`🎨 ER Diagram (Mermaid): ${mermaidPath}`);
      console.log(`📊 Schema JSON: ${schemaJsonPath}`);

      return {
        markdownPath,
        mermaidPath,
        schemaJsonPath,
        schemaInfo
      };

    } catch (error) {
      console.error('❌ Documentation generation failed:', error.message);
      throw error;
    } finally {
      await this.pool.end();
    }
  }
}

// Run documentation generation if script is executed directly
if (require.main === module) {
  const generator = new DatabaseDocumentationGenerator();
  generator.generateDocumentation()
    .then(() => {
      console.log('\n✅ Database documentation generated successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Documentation generation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { DatabaseDocumentationGenerator };
