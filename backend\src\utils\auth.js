const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * Authentication Utilities
 * Handles password hashing, JWT tokens, and verification tokens
 */

// Get JWT secret from environment
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS) || 12;

/**
 * Password hashing utilities
 */
const passwordUtils = {
  /**
   * Hash a password using bcrypt
   */
  async hashPassword(password) {
    try {
      const salt = await bcrypt.genSalt(BCRYPT_ROUNDS);
      const hashedPassword = await bcrypt.hash(password, salt);
      return hashedPassword;
    } catch (error) {
      console.error('Error hashing password:', error.message);
      throw new Error('Password hashing failed');
    }
  },

  /**
   * Compare password with hash
   */
  async comparePassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error comparing password:', error.message);
      throw new Error('Password comparison failed');
    }
  },

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors = [];

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

/**
 * JWT token utilities
 */
const tokenUtils = {
  /**
   * Generate JWT token
   */
  generateToken(payload, expiresIn = JWT_EXPIRES_IN) {
    try {
      const token = jwt.sign(payload, JWT_SECRET, {
        expiresIn,
        issuer: 'lms-saas',
        audience: 'lms-users'
      });
      return token;
    } catch (error) {
      console.error('Error generating JWT token:', error.message);
      throw new Error('Token generation failed');
    }
  },

  /**
   * Verify JWT token
   */
  verifyToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET, {
        issuer: 'lms-saas',
        audience: 'lms-users'
      });
      return { isValid: true, payload: decoded };
    } catch (error) {
      console.error('Error verifying JWT token:', error.message);
      return { isValid: false, error: error.message };
    }
  },

  /**
   * Generate refresh token
   */
  generateRefreshToken() {
    return crypto.randomBytes(64).toString('hex');
  },

  /**
   * Create user token payload
   */
  createUserPayload(user) {
    return {
      userId: user.id,
      email: user.email,
      role: user.role,
      instituteId: user.institute_id,
      isEmailVerified: user.is_email_verified,
      iat: Math.floor(Date.now() / 1000)
    };
  }
};

/**
 * Verification token utilities
 */
const verificationUtils = {
  /**
   * Generate email verification token
   */
  generateEmailVerificationToken() {
    return {
      token: crypto.randomBytes(32).toString('hex'),
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };
  },

  /**
   * Generate password reset token
   */
  generatePasswordResetToken() {
    return {
      token: crypto.randomBytes(32).toString('hex'),
      expires: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
    };
  },

  /**
   * Generate session token
   */
  generateSessionToken() {
    return crypto.randomBytes(64).toString('hex');
  }
};

/**
 * Authentication response utilities
 */
const responseUtils = {
  /**
   * Create successful login response
   */
  createLoginResponse(user, token, refreshToken = null) {
    return {
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        instituteId: user.institute_id,
        isEmailVerified: user.is_email_verified,
        avatarUrl: user.avatar_url
      },
      token,
      refreshToken,
      expiresIn: JWT_EXPIRES_IN
    };
  },

  /**
   * Create successful registration response
   */
  createRegistrationResponse(user, requiresVerification = true) {
    return {
      success: true,
      message: requiresVerification 
        ? 'Registration successful. Please check your email to verify your account.'
        : 'Registration successful. You can now login.',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        instituteId: user.institute_id
      },
      requiresVerification
    };
  },

  /**
   * Create error response
   */
  createErrorResponse(message, code = null, details = null) {
    return {
      success: false,
      error: message,
      code,
      details,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Security utilities
 */
const securityUtils = {
  /**
   * Sanitize user input
   */
  sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  },

  /**
   * Generate secure random string
   */
  generateSecureRandom(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  },

  /**
   * Check if email is valid format
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Rate limiting key generator
   */
  generateRateLimitKey(ip, endpoint, userId = null) {
    return `rate_limit:${endpoint}:${ip}${userId ? `:${userId}` : ''}`;
  }
};

/**
 * Session utilities
 */
const sessionUtils = {
  /**
   * Create session data
   */
  createSessionData(user, req) {
    return {
      userId: user.id,
      instituteId: user.institute_id,
      sessionToken: verificationUtils.generateSessionToken(),
      refreshToken: tokenUtils.generateRefreshToken(),
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent') || 'Unknown',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    };
  },

  /**
   * Extract token from request
   */
  extractTokenFromRequest(req) {
    const authHeader = req.get('Authorization');
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // Also check for token in cookies (if using cookie-based auth)
    if (req.cookies && req.cookies.token) {
      return req.cookies.token;
    }
    
    return null;
  }
};

module.exports = {
  passwordUtils,
  tokenUtils,
  verificationUtils,
  responseUtils,
  securityUtils,
  sessionUtils
};
