# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Coverage
coverage/
.nyc_output/

# Cache
.cache/
.parcel-cache/

# Database
*.db
*.sqlite

# Generated files
*.min.js
*.min.css

# Package lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Documentation
docs/build/

# Temporary files
tmp/
temp/

# Git
.git/
.gitignore

# Husky
.husky/
