'use client'

import React, { useState } from 'react'
import { 
  CreditCard,
  Search,
  Filter,
  TrendingUp,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building2,
  MoreVertical
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import SubscriptionFormModal from '@/components/super-admin/SubscriptionFormModal'

enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
}

enum SubscriptionPlan {
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

interface Subscription {
  id: string
  instituteId: string
  instituteName: string
  plan: SubscriptionPlan
  status: SubscriptionStatus
  monthlyPrice: number
  yearlyPrice: number
  billingCycle: 'monthly' | 'yearly'
  startDate: string
  endDate: string
  nextBillingDate: string
  totalRevenue: number
  usersIncluded: number
  usersUsed: number
  features: string[]
}

export default function SuperAdminSubscriptionsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<SubscriptionStatus | 'all'>('all')
  const [planFilter, setPlanFilter] = useState<SubscriptionPlan | 'all'>('all')
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null)
  const [modalMode, setModalMode] = useState<'edit' | 'status'>('edit')
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])

  // Initialize mock data
  React.useEffect(() => {
    const mockSubscriptions: Subscription[] = [
    {
      id: 'sub-1',
      instituteId: 'inst-1',
      instituteName: 'Harvard University',
      plan: SubscriptionPlan.ENTERPRISE,
      status: SubscriptionStatus.ACTIVE,
      monthlyPrice: 999,
      yearlyPrice: 10990,
      billingCycle: 'yearly',
      startDate: '2023-01-15',
      endDate: '2024-01-15',
      nextBillingDate: '2024-01-15',
      totalRevenue: 10990,
      usersIncluded: 20000,
      usersUsed: 15000,
      features: ['Unlimited courses', 'Custom domain', 'Advanced analytics', 'Priority support']
    },
    {
      id: 'sub-2',
      instituteId: 'inst-2',
      instituteName: 'MIT',
      plan: SubscriptionPlan.PREMIUM,
      status: SubscriptionStatus.ACTIVE,
      monthlyPrice: 299,
      yearlyPrice: 3290,
      billingCycle: 'monthly',
      startDate: '2023-02-20',
      endDate: '2024-02-20',
      nextBillingDate: '2024-01-20',
      totalRevenue: 3290,
      usersIncluded: 5000,
      usersUsed: 4200,
      features: ['Up to 100 courses', 'Custom domain', 'Standard analytics']
    },
    {
      id: 'sub-3',
      instituteId: 'inst-3',
      instituteName: 'Stanford University',
      plan: SubscriptionPlan.ENTERPRISE,
      status: SubscriptionStatus.PENDING,
      monthlyPrice: 999,
      yearlyPrice: 10990,
      billingCycle: 'yearly',
      startDate: '2023-12-01',
      endDate: '2024-12-01',
      nextBillingDate: '2023-12-01',
      totalRevenue: 0,
      usersIncluded: 20000,
      usersUsed: 0,
      features: ['Unlimited courses', 'Custom domain', 'Advanced analytics', 'Priority support']
    },
    {
      id: 'sub-4',
      instituteId: 'inst-4',
      instituteName: 'Berkeley College',
      plan: SubscriptionPlan.BASIC,
      status: SubscriptionStatus.EXPIRED,
      monthlyPrice: 99,
      yearlyPrice: 1090,
      billingCycle: 'monthly',
      startDate: '2023-06-10',
      endDate: '2023-11-10',
      nextBillingDate: '2023-11-10',
      totalRevenue: 495,
      usersIncluded: 1000,
      usersUsed: 800,
      features: ['Up to 10 courses', 'Basic analytics']
    }
    ]
    setSubscriptions(mockSubscriptions)
  }, [])

  const getStatusBadge = (status: SubscriptionStatus) => {
    const statusConfig = {
      [SubscriptionStatus.ACTIVE]: {
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
        text: 'Active',
      },
      [SubscriptionStatus.PENDING]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        text: 'Pending',
      },
      [SubscriptionStatus.EXPIRED]: {
        color: 'bg-red-100 text-red-800',
        icon: AlertTriangle,
        text: 'Expired',
      },
      [SubscriptionStatus.CANCELLED]: {
        color: 'bg-gray-100 text-gray-800',
        icon: AlertTriangle,
        text: 'Cancelled',
      },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const getPlanBadge = (plan: SubscriptionPlan) => {
    const planConfig = {
      [SubscriptionPlan.BASIC]: {
        color: 'bg-blue-100 text-blue-800',
        text: 'Basic',
      },
      [SubscriptionPlan.PREMIUM]: {
        color: 'bg-purple-100 text-purple-800',
        text: 'Premium',
      },
      [SubscriptionPlan.ENTERPRISE]: {
        color: 'bg-orange-100 text-orange-800',
        text: 'Enterprise',
      },
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${planConfig[plan].color}`}>
        {planConfig[plan].text}
      </span>
    )
  }

  const filteredSubscriptions = subscriptions.filter(subscription => {
    const matchesSearch = 
      subscription.instituteName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscription.id.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || subscription.status === statusFilter
    const matchesPlan = planFilter === 'all' || subscription.plan === planFilter

    return matchesSearch && matchesStatus && matchesPlan
  })

  const handleEditSubscription = (subscription: Subscription) => {
    setEditingSubscription(subscription)
    setModalMode('edit')
    setIsFormModalOpen(true)
  }

  const handleUpdateStatus = (subscription: Subscription) => {
    setEditingSubscription(subscription)
    setModalMode('status')
    setIsFormModalOpen(true)
  }

  const handleFormSuccess = () => {
    // Refresh subscriptions list
    console.log('Subscription form submitted successfully')
  }

  const totalRevenue = subscriptions.reduce((sum, sub) => sum + sub.totalRevenue, 0)
  const activeSubscriptions = subscriptions.filter(sub => sub.status === SubscriptionStatus.ACTIVE).length
  const expiringSubscriptions = subscriptions.filter(sub => {
    const endDate = new Date(sub.endDate)
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
    return endDate <= thirtyDaysFromNow && sub.status === SubscriptionStatus.ACTIVE
  }).length

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Management</h1>
          <p className="text-gray-600 mt-1">
            Manage institute subscriptions and billing
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <TrendingUp className="mr-2 h-4 w-4" />
            Revenue Report
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${totalRevenue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
              <p className="text-2xl font-bold text-gray-900">{activeSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Calendar className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-gray-900">{expiringSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Monthly Growth</p>
              <p className="text-2xl font-bold text-gray-900">+12.5%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search subscriptions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as SubscriptionStatus | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value={SubscriptionStatus.ACTIVE}>Active</option>
            <option value={SubscriptionStatus.PENDING}>Pending</option>
            <option value={SubscriptionStatus.EXPIRED}>Expired</option>
            <option value={SubscriptionStatus.CANCELLED}>Cancelled</option>
          </select>
          
          <select
            value={planFilter}
            onChange={(e) => setPlanFilter(e.target.value as SubscriptionPlan | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Plans</option>
            <option value={SubscriptionPlan.BASIC}>Basic</option>
            <option value={SubscriptionPlan.PREMIUM}>Premium</option>
            <option value={SubscriptionPlan.ENTERPRISE}>Enterprise</option>
          </select>

          <div className="text-sm text-gray-600 flex items-center">
            {filteredSubscriptions.length} of {subscriptions.length} subscriptions
          </div>
        </div>
      </div>

      {/* Subscriptions Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Institute
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Next Billing
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSubscriptions.map((subscription) => (
                <tr key={subscription.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <Building2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{subscription.instituteName}</div>
                        <div className="text-sm text-gray-500">ID: {subscription.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      {getPlanBadge(subscription.plan)}
                      <div className="text-xs text-gray-500 mt-1">
                        ${subscription.billingCycle === 'monthly' ? subscription.monthlyPrice : subscription.yearlyPrice}
                        /{subscription.billingCycle === 'monthly' ? 'mo' : 'yr'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(subscription.status)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {subscription.usersUsed.toLocaleString()} / {subscription.usersIncluded.toLocaleString()}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(subscription.usersUsed / subscription.usersIncluded) * 100}%` }}
                      ></div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      ${subscription.totalRevenue.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {new Date(subscription.nextBillingDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditSubscription(subscription)}
                        title="Edit Plan"
                      >
                        Edit Plan
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleUpdateStatus(subscription)}
                        title="Update Status"
                      >
                        Status
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredSubscriptions.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No subscriptions found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}

      {/* Subscription Form Modal */}
      <SubscriptionFormModal
        isOpen={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
        onSuccess={handleFormSuccess}
        subscription={editingSubscription}
        mode={modalMode}
      />
    </div>
  )
}
