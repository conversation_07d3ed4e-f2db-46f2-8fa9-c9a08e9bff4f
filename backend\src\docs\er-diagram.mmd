erDiagram

    BLOCKED_IPS {
        UUID id NOT NULL
        INET ip_address NOT NULL
        TEXT reason NOT NULL
        INTEGER event_count
        TIMESTAMP WITHOUT TIME ZONE expires_at NOT NULL
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    COURSE_ENROLLMENTS {
        UUID id NOT NULL
        UUID course_id NOT NULL
        UUID student_id NOT NULL
        TIMESTAMP WITHOUT TIME ZONE enrolled_at
        CHARACTER VARYING status
        CHARACTER VARYING grade
        NUMERIC grade_points
        NUMERIC completion_percentage
        ... "and 3 more columns"
    }

    CUSTOM_DOMAINS {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        CHARACTER VARYING domain_name NOT NULL
        CHARACTER VARYING verification_token NOT NULL
        CHARACTER VARYING verification_status
        CHARACTER VARYING ssl_status
        TIMESTAMP WITHOUT TIME ZONE ssl_expires_at
        BOOLEAN dns_configured
        ... "and 4 more columns"
    }

    DOMAIN_VERIFICATION_LOGS {
        UUID id NOT NULL
        UUID domain_id NOT NULL
        CHARACTER VARYING verification_type NOT NULL
        CHARACTER VARYING verification_status NOT NULL
        JSONB verification_details
        TEXT error_message
        TIMESTAMP WITHOUT TIME ZONE checked_at
    }

    EMAIL_VERIFICATIONS {
        UUID id NOT NULL
        UUID user_id NOT NULL
        UUID institute_id
        CHARACTER VARYING email NOT NULL
        CHARACTER VARYING verification_token NOT NULL
        CHARACTER VARYING token_type NOT NULL
        TIMESTAMP WITHOUT TIME ZONE expires_at NOT NULL
        BOOLEAN is_used
        ... "and 2 more columns"
    }

    FAILED_LOGIN_ATTEMPTS {
        UUID id NOT NULL
        CHARACTER VARYING email NOT NULL
        INET ip_address NOT NULL
        TEXT user_agent
        TIMESTAMP WITHOUT TIME ZONE attempt_time
        CHARACTER VARYING reason
        UUID institute_id
    }

    INSTITUTE_BRANDING {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        CHARACTER VARYING logo_url
        CHARACTER VARYING favicon_url
        CHARACTER VARYING primary_color
        CHARACTER VARYING secondary_color
        CHARACTER VARYING accent_color
        CHARACTER VARYING font_family
        ... "and 9 more columns"
    }

    INSTITUTE_DOMAINS {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        CHARACTER VARYING domain NOT NULL
        CHARACTER VARYING subdomain
        CHARACTER VARYING domain_type
        BOOLEAN is_verified
        CHARACTER VARYING verification_token
        CHARACTER VARYING verification_method
        ... "and 14 more columns"
    }

    INSTITUTE_OVERVIEW {
        UUID id
        CHARACTER VARYING name
        CHARACTER VARYING slug
        CHARACTER VARYING email
        CHARACTER VARYING phone
        TEXT address
        CHARACTER VARYING website
        CHARACTER VARYING logo_url
        ... "and 15 more columns"
    }

    INSTITUTE_SETTINGS {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        CHARACTER VARYING setting_key NOT NULL
        JSONB setting_value
        CHARACTER VARYING setting_type
        BOOLEAN is_public
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    INSTITUTE_STATUS_HISTORY {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        UUID changed_by NOT NULL
        BOOLEAN old_status
        BOOLEAN new_status NOT NULL
        TEXT reason NOT NULL
        TIMESTAMP WITHOUT TIME ZONE created_at
    }

    INSTITUTE_USAGE_STATS {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        DATE stat_date NOT NULL
        INTEGER active_users
        INTEGER new_users
        INTEGER total_logins
        INTEGER storage_used_mb
        INTEGER api_requests
        ... "and 2 more columns"
    }

    INSTITUTES {
        UUID id NOT NULL
        CHARACTER VARYING name NOT NULL
        CHARACTER VARYING slug NOT NULL
        CHARACTER VARYING email NOT NULL
        CHARACTER VARYING phone
        TEXT address
        CHARACTER VARYING website
        CHARACTER VARYING logo_url
        ... "and 9 more columns"
    }

    MAINTENANCE_WINDOWS {
        UUID id NOT NULL
        CHARACTER VARYING title NOT NULL
        TEXT description
        CHARACTER VARYING maintenance_type NOT NULL
        TIMESTAMP WITHOUT TIME ZONE start_time NOT NULL
        TIMESTAMP WITHOUT TIME ZONE end_time NOT NULL
        JSONB affected_services
        CHARACTER VARYING status
        ... "and 3 more columns"
    }

    PASSWORD_HISTORY {
        UUID id NOT NULL
        UUID user_id NOT NULL
        CHARACTER VARYING password_hash NOT NULL
        TIMESTAMP WITHOUT TIME ZONE created_at
    }

    PLATFORM_ANALYTICS_CACHE {
        UUID id NOT NULL
        CHARACTER VARYING metric_type NOT NULL
        DATE metric_date NOT NULL
        JSONB metric_data NOT NULL
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    PLATFORM_FEATURE_FLAGS {
        UUID id NOT NULL
        CHARACTER VARYING feature_name NOT NULL
        TEXT description
        BOOLEAN is_enabled
        INTEGER rollout_percentage
        JSONB target_subscription_plans
        JSONB target_institutes
        UUID created_by NOT NULL
        ... "and 2 more columns"
    }

    PLATFORM_NOTIFICATIONS {
        UUID id NOT NULL
        CHARACTER VARYING notification_type NOT NULL
        CHARACTER VARYING title NOT NULL
        TEXT message NOT NULL
        CHARACTER VARYING severity
        CHARACTER VARYING target_audience
        UUID target_institute_id
        BOOLEAN is_read
        ... "and 4 more columns"
    }

    RATE_LIMIT_VIOLATIONS {
        UUID id NOT NULL
        INET ip_address NOT NULL
        CHARACTER VARYING endpoint NOT NULL
        INTEGER violation_count
        TIMESTAMP WITHOUT TIME ZONE window_start NOT NULL
        TIMESTAMP WITHOUT TIME ZONE window_end NOT NULL
        TIMESTAMP WITHOUT TIME ZONE created_at
    }

    SECURITY_AUDIT_LOG {
        UUID id NOT NULL
        CHARACTER VARYING event_type NOT NULL
        CHARACTER VARYING severity NOT NULL
        INET ip_address NOT NULL
        TEXT user_agent
        UUID user_id
        CHARACTER VARYING endpoint
        JSONB details
        ... "and 1 more columns"
    }

    SECURITY_CONFIG {
        UUID id NOT NULL
        CHARACTER VARYING config_key NOT NULL
        JSONB config_value NOT NULL
        TEXT description
        BOOLEAN is_active
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    SESSION_SECURITY {
        UUID id NOT NULL
        CHARACTER VARYING session_id NOT NULL
        UUID user_id NOT NULL
        INET ip_address NOT NULL
        TEXT user_agent
        CHARACTER VARYING location_country
        CHARACTER VARYING location_city
        BOOLEAN is_suspicious
        ... "and 3 more columns"
    }

    SSL_CERTIFICATES {
        UUID id NOT NULL
        UUID domain_id NOT NULL
        CHARACTER VARYING certificate_authority
        TEXT certificate_data
        TEXT private_key_data
        TEXT certificate_chain
        TIMESTAMP WITHOUT TIME ZONE issued_at
        TIMESTAMP WITHOUT TIME ZONE expires_at
        ... "and 6 more columns"
    }

    STUDENT_PROFILES {
        UUID id NOT NULL
        UUID user_id NOT NULL
        CHARACTER VARYING student_id
        DATE enrollment_date
        INTEGER graduation_year
        CHARACTER VARYING major
        NUMERIC gpa
        CHARACTER VARYING academic_status
        ... "and 7 more columns"
    }

    SUBSCRIPTION_HISTORY {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        UUID changed_by NOT NULL
        CHARACTER VARYING old_plan
        CHARACTER VARYING new_plan NOT NULL
        CHARACTER VARYING old_status
        CHARACTER VARYING new_status NOT NULL
        TEXT notes
        ... "and 1 more columns"
    }

    SUPER_ADMIN_ACTIVITY_LOG {
        UUID id NOT NULL
        UUID admin_id NOT NULL
        CHARACTER VARYING action_type NOT NULL
        CHARACTER VARYING target_type NOT NULL
        UUID target_id NOT NULL
        JSONB action_details
        INET ip_address
        TEXT user_agent
        ... "and 1 more columns"
    }

    TEACHER_INVITATIONS {
        UUID id NOT NULL
        UUID institute_id NOT NULL
        CHARACTER VARYING email NOT NULL
        CHARACTER VARYING token NOT NULL
        UUID invited_by NOT NULL
        CHARACTER VARYING department
        TEXT message
        TIMESTAMP WITHOUT TIME ZONE expires_at
        ... "and 4 more columns"
    }

    TEACHER_PROFILES {
        UUID id NOT NULL
        UUID user_id NOT NULL
        CHARACTER VARYING employee_id
        CHARACTER VARYING department
        CHARACTER VARYING specialization
        DATE hire_date
        CHARACTER VARYING office_location
        CHARACTER VARYING office_hours
        ... "and 6 more columns"
    }

    TWO_FACTOR_AUTH {
        UUID id NOT NULL
        UUID user_id NOT NULL
        CHARACTER VARYING secret_key NOT NULL
        JSONB backup_codes
        BOOLEAN is_enabled
        TIMESTAMP WITHOUT TIME ZONE last_used_at
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    USER_ROLES {
        UUID id NOT NULL
        UUID user_id NOT NULL
        UUID institute_id
        CHARACTER VARYING role_name NOT NULL
        JSONB permissions
        BOOLEAN is_active
        TIMESTAMP WITHOUT TIME ZONE created_at
        TIMESTAMP WITHOUT TIME ZONE updated_at
    }

    USER_SESSIONS {
        UUID id NOT NULL
        UUID user_id NOT NULL
        UUID institute_id
        CHARACTER VARYING session_token NOT NULL
        CHARACTER VARYING refresh_token
        INET ip_address
        TEXT user_agent
        TIMESTAMP WITHOUT TIME ZONE expires_at NOT NULL
        ... "and 3 more columns"
    }

    USERS {
        UUID id NOT NULL
        UUID institute_id
        CHARACTER VARYING email NOT NULL
        CHARACTER VARYING password_hash NOT NULL
        CHARACTER VARYING first_name NOT NULL
        CHARACTER VARYING last_name NOT NULL
        CHARACTER VARYING phone
        CHARACTER VARYING avatar_url
        ... "and 6 more columns"
    }

    USERS ||--o{ COURSE_ENROLLMENTS : "has"
    INSTITUTES ||--o{ CUSTOM_DOMAINS : "has"
    INSTITUTE_DOMAINS ||--o{ DOMAIN_VERIFICATION_LOGS : "has"
    INSTITUTES ||--o{ EMAIL_VERIFICATIONS : "has"
    USERS ||--o{ EMAIL_VERIFICATIONS : "has"
    INSTITUTES ||--o{ FAILED_LOGIN_ATTEMPTS : "has"
    INSTITUTES ||--o{ INSTITUTE_BRANDING : "has"
    INSTITUTES ||--o{ INSTITUTE_DOMAINS : "has"
    INSTITUTES ||--o{ INSTITUTE_SETTINGS : "has"
    USERS ||--|| INSTITUTE_STATUS_HISTORY : "has"
    INSTITUTES ||--o{ INSTITUTE_STATUS_HISTORY : "has"
    INSTITUTES ||--o{ INSTITUTE_USAGE_STATS : "has"
    USERS ||--|| MAINTENANCE_WINDOWS : "has"
    USERS ||--o{ PASSWORD_HISTORY : "has"
    USERS ||--|| PLATFORM_FEATURE_FLAGS : "has"
    USERS ||--|| PLATFORM_NOTIFICATIONS : "has"
    INSTITUTES ||--|| PLATFORM_NOTIFICATIONS : "has"
    USERS ||--|| SECURITY_AUDIT_LOG : "has"
    USERS ||--o{ SESSION_SECURITY : "has"
    INSTITUTE_DOMAINS ||--o{ SSL_CERTIFICATES : "has"
    USERS ||--o{ STUDENT_PROFILES : "has"
    USERS ||--|| SUBSCRIPTION_HISTORY : "has"
    INSTITUTES ||--o{ SUBSCRIPTION_HISTORY : "has"
    USERS ||--|| SUPER_ADMIN_ACTIVITY_LOG : "has"
    INSTITUTES ||--o{ TEACHER_INVITATIONS : "has"
    USERS ||--|| TEACHER_INVITATIONS : "has"
    USERS ||--|| TEACHER_INVITATIONS : "has"
    USERS ||--o{ TEACHER_PROFILES : "has"
    USERS ||--o{ TWO_FACTOR_AUTH : "has"
    INSTITUTES ||--o{ USER_ROLES : "has"
    USERS ||--o{ USER_ROLES : "has"
    INSTITUTES ||--o{ USER_SESSIONS : "has"
    USERS ||--o{ USER_SESSIONS : "has"
    INSTITUTES ||--o{ USERS : "has"
