'use client'

import React from 'react'
import { SuperAdminRoute } from '@/components/auth/ProtectedRoute'
import SuperAdminSidebar from '@/components/super-admin/SuperAdminSidebar'
import SuperAdminHeader from '@/components/super-admin/SuperAdminHeader'

interface SuperAdminLayoutProps {
  children: React.ReactNode
}

export default function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  return (
    <SuperAdminRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          {/* Sidebar */}
          <SuperAdminSidebar />
          
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <SuperAdminHeader />
            
            {/* Page Content */}
            <main className="flex-1 p-6">
              {children}
            </main>
          </div>
        </div>
      </div>
    </SuperAdminRoute>
  )
}
