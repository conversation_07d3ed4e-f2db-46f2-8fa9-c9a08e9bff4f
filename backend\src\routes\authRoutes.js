const express = require('express');
const authService = require('../services/authService');
const { responseUtils } = require('../utils/auth');
const { 
  registrationSchemas, 
  loginSchema, 
  emailVerificationSchema,
  passwordResetRequestSchema,
  passwordResetConfirmSchema,
  changePasswordSchema,
  refreshTokenSchema,
  createValidationMiddleware,
  sanitizeInput
} = require('../validation/authValidation');
const { 
  authenticateToken, 
  createAuthRateLimit, 
  logAuthEvent,
  requireEmailVerification
} = require('../middleware/authMiddleware');
const { requireTenant, requireSuperAdmin } = require('../middleware/tenantMiddleware');
const { query } = require('../database/connection');
const { trackFailedLogin, trackSuccessfulLogin, trackSessionSecurity } = require('../middleware/securityMonitoring');
const { createRateLimiters } = require('../middleware/securityMiddleware');

const router = express.Router();

// Apply sanitization to all routes
router.use(sanitizeInput);

// Enhanced rate limiting for auth endpoints
const rateLimiters = createRateLimiters();
const loginRateLimit = rateLimiters.auth;
const registrationRateLimit = rateLimiters.registration;
const passwordResetRateLimit = rateLimiters.passwordReset;

/**
 * @route POST /api/auth/register/student
 * @desc Register a new student
 * @access Public (with tenant context)
 */
router.post('/register/student',
  requireTenant,
  registrationRateLimit,
  logAuthEvent('student_registration'),
  createValidationMiddleware(registrationSchemas.student),
  async (req, res) => {
    try {
      const { email, password, firstName, lastName, phone, studentId } = req.body;

      // Validate email domain against institute
      const isValidDomain = await validateInstituteEmailDomain(email, req.tenant.instituteId);
      if (!isValidDomain) {
        return res.status(400).json(
          responseUtils.createErrorResponse(
            'Email domain not allowed for this institute. Please use your institute email address.',
            'INVALID_EMAIL_DOMAIN',
            {
              providedEmail: email,
              instituteName: req.tenant.instituteName,
              allowedDomains: await getInstituteAllowedDomains(req.tenant.instituteId)
            }
          )
        );
      }

      const result = await authService.registerUser({
        email,
        password,
        firstName,
        lastName,
        phone,
        studentId,
        instituteId: req.tenant.instituteId
      }, 'student');

      res.status(201).json(
        responseUtils.createRegistrationResponse(result.user, result.requiresVerification)
      );

    } catch (error) {
      console.error('Student registration error:', error.message);

      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'USER_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Registration failed', 'REGISTRATION_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/auth/register/institute-admin
 * @desc Register a new institute admin and create institute
 * @access Public
 */
router.post('/register/institute-admin',
  registrationRateLimit,
  logAuthEvent('institute_admin_registration'),
  createValidationMiddleware(registrationSchemas.instituteAdmin),
  async (req, res) => {
    try {
      const result = await authService.registerInstituteAdmin(req.body);

      res.status(201).json({
        success: true,
        message: 'Institute and admin account created successfully. Please check your email to verify your account.',
        institute: {
          id: result.institute.id,
          name: result.institute.name,
          slug: result.institute.slug,
          email: result.institute.email
        },
        user: {
          id: result.user.id,
          email: result.user.email,
          firstName: result.user.first_name,
          lastName: result.user.last_name,
          role: result.user.role
        },
        requiresVerification: true
      });

    } catch (error) {
      console.error('Institute admin registration error:', error.message);
      
      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'INSTITUTE_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Institute registration failed', 'INSTITUTE_REGISTRATION_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/auth/register/super-admin
 * @desc Register a new super admin (requires master key)
 * @access Restricted
 */
router.post('/register/super-admin',
  requireSuperAdmin,
  registrationRateLimit,
  logAuthEvent('super_admin_registration'),
  createValidationMiddleware(registrationSchemas.superAdmin),
  async (req, res) => {
    try {
      const { masterKey, ...userData } = req.body;
      
      // Verify master key
      if (masterKey !== process.env.SUPER_ADMIN_MASTER_KEY) {
        return res.status(403).json(
          responseUtils.createErrorResponse('Invalid master key', 'INVALID_MASTER_KEY')
        );
      }

      const result = await authService.registerUser({
        ...userData,
        instituteId: null // Super admin has no institute
      }, 'super_admin');

      res.status(201).json(
        responseUtils.createRegistrationResponse(result.user, result.requiresVerification)
      );

    } catch (error) {
      console.error('Super admin registration error:', error.message);
      
      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'USER_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Super admin registration failed', 'SUPER_ADMIN_REGISTRATION_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/auth/login
 * @desc Login user
 * @access Public
 */
router.post('/login',
  loginRateLimit,
  logAuthEvent('login'),
  createValidationMiddleware(loginSchema),
  async (req, res) => {
    try {
      const { email, password, rememberMe } = req.body;
      
      const result = await authService.loginUser(email, password, req);

      res.json(
        responseUtils.createLoginResponse(result.user, result.accessToken, result.refreshToken)
      );

    } catch (error) {
      console.error('Login error:', error.message);
      
      // Generic error message for security
      res.status(401).json(
        responseUtils.createErrorResponse('Invalid email or password', 'INVALID_CREDENTIALS')
      );
    }
  }
);

/**
 * @route POST /api/auth/verify-email
 * @desc Verify email address
 * @access Public
 */
router.post('/verify-email',
  createValidationMiddleware(emailVerificationSchema),
  logAuthEvent('email_verification'),
  async (req, res) => {
    try {
      const { token, email } = req.body;
      
      const user = await authService.verifyEmail(token, email);

      res.json({
        success: true,
        message: 'Email verified successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          isEmailVerified: user.is_email_verified
        }
      });

    } catch (error) {
      console.error('Email verification error:', error.message);
      
      res.status(400).json(
        responseUtils.createErrorResponse(error.message, 'EMAIL_VERIFICATION_FAILED')
      );
    }
  }
);

/**
 * @route POST /api/auth/forgot-password
 * @desc Request password reset
 * @access Public
 */
router.post('/forgot-password',
  passwordResetRateLimit,
  logAuthEvent('password_reset_request'),
  createValidationMiddleware(passwordResetRequestSchema),
  async (req, res) => {
    try {
      const { email } = req.body;
      
      const result = await authService.requestPasswordReset(email, req.tenant?.instituteId);

      // Always return success for security (don't reveal if email exists)
      res.json({
        success: true,
        message: 'If the email address exists, a password reset link has been sent.'
      });

    } catch (error) {
      console.error('Password reset request error:', error.message);
      
      // Still return success for security
      res.json({
        success: true,
        message: 'If the email address exists, a password reset link has been sent.'
      });
    }
  }
);

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password with token
 * @access Public
 */
router.post('/reset-password',
  passwordResetRateLimit,
  logAuthEvent('password_reset'),
  createValidationMiddleware(passwordResetConfirmSchema),
  async (req, res) => {
    try {
      const { token, newPassword } = req.body;
      
      const result = await authService.resetPassword(token, newPassword);

      res.json(result);

    } catch (error) {
      console.error('Password reset error:', error.message);
      
      res.status(400).json(
        responseUtils.createErrorResponse(error.message, 'PASSWORD_RESET_FAILED')
      );
    }
  }
);

/**
 * @route POST /api/auth/change-password
 * @desc Change password for authenticated user
 * @access Private
 */
router.post('/change-password',
  authenticateToken,
  requireEmailVerification,
  createValidationMiddleware(changePasswordSchema),
  logAuthEvent('password_change'),
  async (req, res) => {
    try {
      const { currentPassword, newPassword } = req.body;
      
      const result = await authService.changePassword(req.user.id, currentPassword, newPassword);

      res.json(result);

    } catch (error) {
      console.error('Change password error:', error.message);
      
      if (error.message.includes('incorrect')) {
        return res.status(400).json(
          responseUtils.createErrorResponse(error.message, 'INCORRECT_PASSWORD')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Password change failed', 'PASSWORD_CHANGE_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token
 * @access Public
 */
router.post('/refresh',
  createValidationMiddleware(refreshTokenSchema),
  async (req, res) => {
    try {
      const { refreshToken } = req.body;
      
      const result = await authService.refreshAccessToken(refreshToken);

      res.json({
        success: true,
        accessToken: result.accessToken,
        user: result.user
      });

    } catch (error) {
      console.error('Refresh token error:', error.message);
      
      res.status(401).json(
        responseUtils.createErrorResponse('Invalid refresh token', 'INVALID_REFRESH_TOKEN')
      );
    }
  }
);

/**
 * @route POST /api/auth/logout
 * @desc Logout user
 * @access Private
 */
router.post('/logout',
  authenticateToken,
  logAuthEvent('logout'),
  async (req, res) => {
    try {
      const sessionToken = req.get('Authorization')?.substring(7); // Remove 'Bearer '
      
      await authService.logoutUser(req.user.id, sessionToken);

      res.json({
        success: true,
        message: 'Logged out successfully'
      });

    } catch (error) {
      console.error('Logout error:', error.message);
      
      res.status(500).json(
        responseUtils.createErrorResponse('Logout failed', 'LOGOUT_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me',
  authenticateToken,
  async (req, res) => {
    try {
      res.json({
        success: true,
        user: {
          id: req.user.id,
          email: req.user.email,
          firstName: req.user.first_name,
          lastName: req.user.last_name,
          phone: req.user.phone,
          role: req.user.role,
          instituteId: req.user.institute_id,
          isEmailVerified: req.user.is_email_verified,
          avatarUrl: req.user.avatar_url,
          lastLoginAt: req.user.last_login_at
        }
      });

    } catch (error) {
      console.error('Get profile error:', error.message);
      
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get profile', 'PROFILE_ERROR')
      );
    }
  }
);

/**
 * Helper function to validate email domain against institute
 */
async function validateInstituteEmailDomain(email, instituteId) {
  try {
    const emailDomain = email.split('@')[1].toLowerCase();

    // Get institute information
    const instituteResult = await query(
      'SELECT email, allowed_domains FROM institutes WHERE id = $1',
      [instituteId]
    );

    if (instituteResult.rows.length === 0) {
      return false;
    }

    const institute = instituteResult.rows[0];
    const instituteEmailDomain = institute.email.split('@')[1].toLowerCase();

    // Check if email domain matches institute domain
    if (emailDomain === instituteEmailDomain) {
      return true;
    }

    // Check against allowed domains (if configured)
    if (institute.allowed_domains) {
      try {
        const allowedDomains = JSON.parse(institute.allowed_domains);
        return allowedDomains.some(domain => domain.toLowerCase() === emailDomain);
      } catch (error) {
        console.warn('Failed to parse allowed domains:', error.message);
      }
    }

    // Check custom domains
    const customDomainsResult = await query(
      'SELECT domain FROM institute_domains WHERE institute_id = $1 AND is_active = TRUE',
      [instituteId]
    );

    const customDomains = customDomainsResult.rows.map(row => row.domain.toLowerCase());
    return customDomains.includes(emailDomain);

  } catch (error) {
    console.error('Email domain validation error:', error.message);
    return false;
  }
}

/**
 * Helper function to get allowed domains for an institute
 */
async function getInstituteAllowedDomains(instituteId) {
  try {
    const instituteResult = await query(
      'SELECT email, allowed_domains FROM institutes WHERE id = $1',
      [instituteId]
    );

    if (instituteResult.rows.length === 0) {
      return [];
    }

    const institute = instituteResult.rows[0];
    const domains = [institute.email.split('@')[1].toLowerCase()];

    // Add configured allowed domains
    if (institute.allowed_domains) {
      try {
        const allowedDomains = JSON.parse(institute.allowed_domains);
        domains.push(...allowedDomains.map(d => d.toLowerCase()));
      } catch (error) {
        console.warn('Failed to parse allowed domains:', error.message);
      }
    }

    // Add custom domains
    const customDomainsResult = await query(
      'SELECT domain FROM institute_domains WHERE institute_id = $1 AND is_active = TRUE',
      [instituteId]
    );

    domains.push(...customDomainsResult.rows.map(row => row.domain.toLowerCase()));

    return [...new Set(domains)]; // Remove duplicates

  } catch (error) {
    console.error('Get allowed domains error:', error.message);
    return [];
  }
}

module.exports = router;
