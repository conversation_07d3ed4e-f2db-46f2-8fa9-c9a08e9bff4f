'use client'

import React, { useState } from 'react'
import { 
  Building2,
  Search,
  Filter,
  Plus,
  MoreVertical,
  Users,
  Globe,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  Edit,
  Trash2,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'

enum InstituteStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
}

enum SubscriptionPlan {
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

interface Institute {
  id: string
  name: string
  email: string
  website?: string
  status: InstituteStatus
  subscriptionPlan: SubscriptionPlan
  customDomain?: string
  totalUsers: number
  totalCourses: number
  monthlyRevenue: number
  createdAt: string
  lastActivity: string
}

export default function SuperAdminInstitutesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<InstituteStatus | 'all'>('all')
  const [planFilter, setPlanFilter] = useState<SubscriptionPlan | 'all'>('all')
  const [selectedInstitutes, setSelectedInstitutes] = useState<string[]>([])

  // Mock data
  const institutes: Institute[] = [
    {
      id: 'inst-1',
      name: 'Harvard University',
      email: '<EMAIL>',
      website: 'https://harvard.edu',
      status: InstituteStatus.ACTIVE,
      subscriptionPlan: SubscriptionPlan.ENTERPRISE,
      customDomain: 'harvard.edu',
      totalUsers: 15000,
      totalCourses: 500,
      monthlyRevenue: 12500,
      createdAt: '2023-01-15',
      lastActivity: '2023-12-01T10:30:00Z'
    },
    {
      id: 'inst-2',
      name: 'MIT',
      email: '<EMAIL>',
      website: 'https://mit.edu',
      status: InstituteStatus.ACTIVE,
      subscriptionPlan: SubscriptionPlan.PREMIUM,
      customDomain: 'mit.edu',
      totalUsers: 12000,
      totalCourses: 450,
      monthlyRevenue: 8500,
      createdAt: '2023-02-20',
      lastActivity: '2023-12-01T09:15:00Z'
    },
    {
      id: 'inst-3',
      name: 'Stanford University',
      email: '<EMAIL>',
      website: 'https://stanford.edu',
      status: InstituteStatus.PENDING,
      subscriptionPlan: SubscriptionPlan.ENTERPRISE,
      totalUsers: 0,
      totalCourses: 0,
      monthlyRevenue: 0,
      createdAt: '2023-12-01',
      lastActivity: '2023-12-01T08:00:00Z'
    },
    {
      id: 'inst-4',
      name: 'Berkeley College',
      email: '<EMAIL>',
      website: 'https://berkeley.edu',
      status: InstituteStatus.SUSPENDED,
      subscriptionPlan: SubscriptionPlan.BASIC,
      totalUsers: 5000,
      totalCourses: 150,
      monthlyRevenue: 0,
      createdAt: '2023-06-10',
      lastActivity: '2023-11-15T14:20:00Z'
    }
  ]

  const getStatusBadge = (status: InstituteStatus) => {
    const statusConfig = {
      [InstituteStatus.ACTIVE]: {
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
        text: 'Active',
      },
      [InstituteStatus.PENDING]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        text: 'Pending',
      },
      [InstituteStatus.SUSPENDED]: {
        color: 'bg-red-100 text-red-800',
        icon: XCircle,
        text: 'Suspended',
      },
      [InstituteStatus.CANCELLED]: {
        color: 'bg-gray-100 text-gray-800',
        icon: XCircle,
        text: 'Cancelled',
      },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const getPlanBadge = (plan: SubscriptionPlan) => {
    const planConfig = {
      [SubscriptionPlan.BASIC]: {
        color: 'bg-blue-100 text-blue-800',
        text: 'Basic',
      },
      [SubscriptionPlan.PREMIUM]: {
        color: 'bg-purple-100 text-purple-800',
        text: 'Premium',
      },
      [SubscriptionPlan.ENTERPRISE]: {
        color: 'bg-orange-100 text-orange-800',
        text: 'Enterprise',
      },
    }

    const config = planConfig[plan]

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    )
  }

  const filteredInstitutes = institutes.filter(institute => {
    const matchesSearch = 
      institute.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institute.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institute.customDomain?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || institute.status === statusFilter
    const matchesPlan = planFilter === 'all' || institute.subscriptionPlan === planFilter

    return matchesSearch && matchesStatus && matchesPlan
  })

  const handleInstituteAction = (instituteId: string, action: string) => {
    console.log(`Performing ${action} on institute ${instituteId}`)
    // TODO: Implement actual actions
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Institute Management</h1>
          <p className="text-gray-600 mt-1">
            Manage educational institutions and their subscriptions
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Institute
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Institutes</p>
              <p className="text-2xl font-bold text-gray-900">{institutes.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">
                {institutes.filter(i => i.status === InstituteStatus.ACTIVE).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">
                {institutes.filter(i => i.status === InstituteStatus.PENDING).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                ${institutes.reduce((sum, i) => sum + i.monthlyRevenue, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search institutes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as InstituteStatus | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value={InstituteStatus.ACTIVE}>Active</option>
            <option value={InstituteStatus.PENDING}>Pending</option>
            <option value={InstituteStatus.SUSPENDED}>Suspended</option>
            <option value={InstituteStatus.CANCELLED}>Cancelled</option>
          </select>
          
          <select
            value={planFilter}
            onChange={(e) => setPlanFilter(e.target.value as SubscriptionPlan | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Plans</option>
            <option value={SubscriptionPlan.BASIC}>Basic</option>
            <option value={SubscriptionPlan.PREMIUM}>Premium</option>
            <option value={SubscriptionPlan.ENTERPRISE}>Enterprise</option>
          </select>

          <div className="text-sm text-gray-600 flex items-center">
            {filteredInstitutes.length} of {institutes.length} institutes
          </div>
        </div>
      </div>

      {/* Institutes Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedInstitutes.length === filteredInstitutes.length && filteredInstitutes.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedInstitutes(filteredInstitutes.map(institute => institute.id))
                      } else {
                        setSelectedInstitutes([])
                      }
                    }}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Institute
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Users
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInstitutes.map((institute) => (
                <tr key={institute.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedInstitutes.includes(institute.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedInstitutes([...selectedInstitutes, institute.id])
                        } else {
                          setSelectedInstitutes(selectedInstitutes.filter(id => id !== institute.id))
                        }
                      }}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <Building2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{institute.name}</div>
                        <div className="text-sm text-gray-500">{institute.email}</div>
                        {institute.customDomain && (
                          <div className="text-xs text-blue-600 flex items-center mt-1">
                            <Globe className="w-3 h-3 mr-1" />
                            {institute.customDomain}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(institute.status)}
                  </td>
                  <td className="px-6 py-4">
                    {getPlanBadge(institute.subscriptionPlan)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{institute.totalUsers.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">{institute.totalCourses} courses</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      ${institute.monthlyRevenue.toLocaleString()}/mo
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="ghost">
                        <MoreVertical className="h-3 w-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredInstitutes.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No institutes found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  )
}
