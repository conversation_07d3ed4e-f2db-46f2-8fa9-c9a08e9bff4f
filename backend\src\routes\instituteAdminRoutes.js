const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { authenticateToken, requireEmailVerification } = require('../middleware/authMiddleware');
const { requireTenant } = require('../middleware/tenantMiddleware');
const { requireAnyPermission } = require('../middleware/rbacMiddleware');
const { PERMISSIONS } = require('../services/rbacService');
const { query, transaction } = require('../database/connection');
const { responseUtils } = require('../utils/auth');
const securityService = require('../services/securityService');
const yup = require('yup');

const router = express.Router();

// Apply authentication and tenant requirement to all routes
router.use(authenticateToken);
router.use(requireEmailVerification);
router.use(requireTenant);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/branding', req.tenant.instituteId);
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, `${timestamp}_${sanitizedName}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, SVG, and WebP images are allowed.'));
    }
  }
});

/**
 * @route GET /api/institute-admin/branding
 * @desc Get current institute branding settings
 * @access Institute Admin
 */
router.get('/branding',
  requireAnyPermission([PERMISSIONS.INSTITUTE_UPDATE]),
  async (req, res) => {
    try {
      const brandingResult = await query(`
        SELECT * FROM institute_branding 
        WHERE institute_id = $1 AND is_active = TRUE
        ORDER BY created_at DESC
        LIMIT 1
      `, [req.tenant.instituteId]);

      const branding = brandingResult.rows[0] || {
        institute_id: req.tenant.instituteId,
        primary_color: '#3B82F6',
        secondary_color: '#1E40AF',
        accent_color: '#F59E0B',
        logo_url: null,
        favicon_url: null,
        custom_css: null,
        theme_settings: {}
      };

      res.json({
        success: true,
        branding
      });

    } catch (error) {
      console.error('Get branding error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get branding settings', 'BRANDING_GET_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/institute-admin/branding
 * @desc Update institute branding settings
 * @access Institute Admin
 */
router.put('/branding',
  requireAnyPermission([PERMISSIONS.INSTITUTE_UPDATE]),
  async (req, res) => {
    try {
      // Validate input
      const schema = yup.object({
        primaryColor: yup
          .string()
          .matches(/^#[0-9A-F]{6}$/i, 'Primary color must be a valid hex color')
          .required(),
        secondaryColor: yup
          .string()
          .matches(/^#[0-9A-F]{6}$/i, 'Secondary color must be a valid hex color')
          .required(),
        accentColor: yup
          .string()
          .matches(/^#[0-9A-F]{6}$/i, 'Accent color must be a valid hex color')
          .required(),
        customCss: yup
          .string()
          .max(10000, 'Custom CSS must be less than 10,000 characters')
          .nullable(),
        themeSettings: yup
          .object()
          .nullable()
      });

      const validatedData = await schema.validate(req.body);

      const result = await query(`
        INSERT INTO institute_branding (
          institute_id, primary_color, secondary_color, accent_color, 
          custom_css, theme_settings, is_active
        )
        VALUES ($1, $2, $3, $4, $5, $6, TRUE)
        ON CONFLICT (institute_id, is_active)
        WHERE is_active = TRUE
        DO UPDATE SET
          primary_color = EXCLUDED.primary_color,
          secondary_color = EXCLUDED.secondary_color,
          accent_color = EXCLUDED.accent_color,
          custom_css = EXCLUDED.custom_css,
          theme_settings = EXCLUDED.theme_settings,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [
        req.tenant.instituteId,
        validatedData.primaryColor,
        validatedData.secondaryColor,
        validatedData.accentColor,
        validatedData.customCss,
        JSON.stringify(validatedData.themeSettings || {})
      ]);

      res.json({
        success: true,
        message: 'Branding updated successfully',
        branding: result.rows[0]
      });

    } catch (error) {
      console.error('Update branding error:', error.message);
      
      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update branding', 'BRANDING_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/institute-admin/branding/upload-logo
 * @desc Upload institute logo
 * @access Institute Admin
 */
router.post('/branding/upload-logo',
  requireAnyPermission([PERMISSIONS.INSTITUTE_UPDATE]),
  upload.single('logo'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json(
          responseUtils.createErrorResponse('No file uploaded', 'NO_FILE')
        );
      }

      // Validate file
      const validation = securityService.validateFileUpload(
        req.file,
        ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'],
        5 * 1024 * 1024
      );

      if (!validation.isValid) {
        // Delete uploaded file
        await fs.unlink(req.file.path).catch(() => {});
        return res.status(400).json(
          responseUtils.createErrorResponse('File validation failed', 'FILE_VALIDATION_ERROR', validation.errors)
        );
      }

      // Generate public URL for the file
      const logoUrl = `/uploads/branding/${req.tenant.instituteId}/${req.file.filename}`;

      // Update branding with logo URL
      await query(`
        INSERT INTO institute_branding (
          institute_id, logo_url, is_active
        )
        VALUES ($1, $2, TRUE)
        ON CONFLICT (institute_id, is_active)
        WHERE is_active = TRUE
        DO UPDATE SET
          logo_url = EXCLUDED.logo_url,
          updated_at = CURRENT_TIMESTAMP
      `, [req.tenant.instituteId, logoUrl]);

      res.json({
        success: true,
        message: 'Logo uploaded successfully',
        logoUrl
      });

    } catch (error) {
      console.error('Upload logo error:', error.message);
      
      // Clean up uploaded file on error
      if (req.file) {
        await fs.unlink(req.file.path).catch(() => {});
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to upload logo', 'LOGO_UPLOAD_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/institute-admin/branding/upload-favicon
 * @desc Upload institute favicon
 * @access Institute Admin
 */
router.post('/branding/upload-favicon',
  requireAnyPermission([PERMISSIONS.INSTITUTE_UPDATE]),
  upload.single('favicon'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json(
          responseUtils.createErrorResponse('No file uploaded', 'NO_FILE')
        );
      }

      // Validate file
      const validation = securityService.validateFileUpload(
        req.file,
        ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/gif'],
        1 * 1024 * 1024 // 1MB limit for favicon
      );

      if (!validation.isValid) {
        // Delete uploaded file
        await fs.unlink(req.file.path).catch(() => {});
        return res.status(400).json(
          responseUtils.createErrorResponse('File validation failed', 'FILE_VALIDATION_ERROR', validation.errors)
        );
      }

      // Generate public URL for the file
      const faviconUrl = `/uploads/branding/${req.tenant.instituteId}/${req.file.filename}`;

      // Update branding with favicon URL
      await query(`
        INSERT INTO institute_branding (
          institute_id, favicon_url, is_active
        )
        VALUES ($1, $2, TRUE)
        ON CONFLICT (institute_id, is_active)
        WHERE is_active = TRUE
        DO UPDATE SET
          favicon_url = EXCLUDED.favicon_url,
          updated_at = CURRENT_TIMESTAMP
      `, [req.tenant.instituteId, faviconUrl]);

      res.json({
        success: true,
        message: 'Favicon uploaded successfully',
        faviconUrl
      });

    } catch (error) {
      console.error('Upload favicon error:', error.message);
      
      // Clean up uploaded file on error
      if (req.file) {
        await fs.unlink(req.file.path).catch(() => {});
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to upload favicon', 'FAVICON_UPLOAD_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/institute-admin/settings
 * @desc Get institute settings
 * @access Institute Admin
 */
router.get('/settings',
  requireAnyPermission([PERMISSIONS.INSTITUTE_READ]),
  async (req, res) => {
    try {
      const instituteResult = await query(`
        SELECT
          name, email, website, address, phone,
          subscription_plan, subscription_status,
          allowed_domains, timezone, language,
          registration_settings, notification_settings
        FROM institutes
        WHERE id = $1
      `, [req.tenant.instituteId]);

      if (instituteResult.rows.length === 0) {
        return res.status(404).json(
          responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
        );
      }

      const institute = instituteResult.rows[0];

      res.json({
        success: true,
        settings: {
          ...institute,
          allowed_domains: institute.allowed_domains ? JSON.parse(institute.allowed_domains) : [],
          registration_settings: institute.registration_settings ? JSON.parse(institute.registration_settings) : {},
          notification_settings: institute.notification_settings ? JSON.parse(institute.notification_settings) : {}
        }
      });

    } catch (error) {
      console.error('Get settings error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get settings', 'SETTINGS_GET_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/institute-admin/settings
 * @desc Update institute settings
 * @access Institute Admin
 */
router.put('/settings',
  requireAnyPermission([PERMISSIONS.INSTITUTE_UPDATE]),
  async (req, res) => {
    try {
      // Validate input
      const schema = yup.object({
        name: yup
          .string()
          .min(2, 'Institute name must be at least 2 characters')
          .max(255, 'Institute name must be less than 255 characters')
          .required(),
        website: yup
          .string()
          .url('Please provide a valid website URL')
          .nullable(),
        address: yup
          .string()
          .max(500, 'Address must be less than 500 characters')
          .nullable(),
        phone: yup
          .string()
          .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
          .nullable(),
        timezone: yup
          .string()
          .max(50, 'Timezone must be less than 50 characters')
          .nullable(),
        language: yup
          .string()
          .max(10, 'Language must be less than 10 characters')
          .nullable(),
        allowedDomains: yup
          .array()
          .of(yup.string().matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Invalid domain format'))
          .nullable(),
        registrationSettings: yup
          .object({
            requireApproval: yup.boolean(),
            allowSelfRegistration: yup.boolean(),
            requireEmailVerification: yup.boolean(),
            defaultRole: yup.string().oneOf(['student', 'teacher'])
          })
          .nullable(),
        notificationSettings: yup
          .object({
            emailNotifications: yup.boolean(),
            smsNotifications: yup.boolean(),
            pushNotifications: yup.boolean(),
            weeklyDigest: yup.boolean()
          })
          .nullable()
      });

      const validatedData = await schema.validate(req.body);

      const result = await query(`
        UPDATE institutes
        SET
          name = $1,
          website = $2,
          address = $3,
          phone = $4,
          timezone = $5,
          language = $6,
          allowed_domains = $7,
          registration_settings = $8,
          notification_settings = $9,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $10
        RETURNING *
      `, [
        validatedData.name,
        validatedData.website,
        validatedData.address,
        validatedData.phone,
        validatedData.timezone,
        validatedData.language,
        JSON.stringify(validatedData.allowedDomains || []),
        JSON.stringify(validatedData.registrationSettings || {}),
        JSON.stringify(validatedData.notificationSettings || {}),
        req.tenant.instituteId
      ]);

      res.json({
        success: true,
        message: 'Settings updated successfully',
        settings: result.rows[0]
      });

    } catch (error) {
      console.error('Update settings error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update settings', 'SETTINGS_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/institute-admin/students
 * @desc Get students for the institute
 * @access Institute Admin
 */
router.get('/students',
  requireAnyPermission([PERMISSIONS.USER_READ]),
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        status = ''
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      // Build WHERE clause
      let whereConditions = ['u.institute_id = $1', 'u.role = $2'];
      let queryParams = [req.tenant.instituteId, 'student'];
      let paramIndex = 3;

      if (search) {
        whereConditions.push(`(u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`);
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      if (status) {
        whereConditions.push(`u.is_active = $${paramIndex}`);
        queryParams.push(status === 'active');
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get students with profile information
      const students = await query(`
        SELECT
          u.id, u.email, u.first_name, u.last_name, u.phone,
          u.is_active, u.is_email_verified, u.created_at, u.last_login_at,
          sp.student_id, sp.major, sp.graduation_year, sp.academic_status, sp.gpa
        FROM users u
        LEFT JOIN student_profiles sp ON u.id = sp.user_id
        WHERE ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, parseInt(limit), offset]);

      // Get total count
      const totalResult = await query(`
        SELECT COUNT(*) as total
        FROM users u
        WHERE ${whereClause}
      `, queryParams);

      const total = parseInt(totalResult.rows[0].total);
      const totalPages = Math.ceil(total / parseInt(limit));

      res.json({
        success: true,
        students: students.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      });

    } catch (error) {
      console.error('Get students error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get students', 'STUDENTS_GET_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/institute-admin/students
 * @desc Register a new student (admin registration)
 * @access Institute Admin
 */
router.post('/students',
  requireAnyPermission([PERMISSIONS.USER_CREATE]),
  async (req, res) => {
    try {
      // Validate input
      const schema = yup.object({
        email: yup
          .string()
          .email('Please provide a valid email address')
          .required(),
        firstName: yup
          .string()
          .min(2, 'First name must be at least 2 characters')
          .required(),
        lastName: yup
          .string()
          .min(2, 'Last name must be at least 2 characters')
          .required(),
        phone: yup
          .string()
          .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
          .nullable(),
        studentId: yup
          .string()
          .max(50, 'Student ID must be less than 50 characters')
          .nullable(),
        major: yup
          .string()
          .max(100, 'Major must be less than 100 characters')
          .nullable(),
        graduationYear: yup
          .number()
          .integer('Graduation year must be an integer')
          .min(2020, 'Graduation year must be 2020 or later')
          .max(2050, 'Graduation year must be 2050 or earlier')
          .nullable(),
        sendWelcomeEmail: yup
          .boolean()
          .default(true)
      });

      const validatedData = await schema.validate(req.body);

      // Generate temporary password
      const tempPassword = securityService.generateSecureToken(12);

      const result = await transaction(async (client) => {
        // Create user
        const hashedPassword = await require('../utils/auth').passwordUtils.hashPassword(tempPassword);

        const userResult = await client.query(`
          INSERT INTO users (
            email, password_hash, first_name, last_name, phone,
            role, institute_id, is_active, is_email_verified
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, TRUE, FALSE)
          RETURNING *
        `, [
          validatedData.email,
          hashedPassword,
          validatedData.firstName,
          validatedData.lastName,
          validatedData.phone,
          'student',
          req.tenant.instituteId
        ]);

        const user = userResult.rows[0];

        // Create student profile if additional data provided
        if (validatedData.studentId || validatedData.major || validatedData.graduationYear) {
          await client.query(`
            INSERT INTO student_profiles (
              user_id, student_id, major, graduation_year, academic_status
            )
            VALUES ($1, $2, $3, $4, 'active')
          `, [
            user.id,
            validatedData.studentId,
            validatedData.major,
            validatedData.graduationYear
          ]);
        }

        return { user, tempPassword };
      });

      res.status(201).json({
        success: true,
        message: 'Student registered successfully',
        student: {
          id: result.user.id,
          email: result.user.email,
          firstName: result.user.first_name,
          lastName: result.user.last_name,
          tempPassword: result.tempPassword
        }
      });

    } catch (error) {
      console.error('Register student error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse('Email already exists', 'EMAIL_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to register student', 'STUDENT_REGISTRATION_ERROR')
      );
    }
  }
);

module.exports = router;
