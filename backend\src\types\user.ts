import { UserRole } from './auth'

export enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  DEACTIVATED = 'deactivated',
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  EXPIRED = 'expired',
  FAILED = 'failed',
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId: string
  password: string
  status: UserStatus
  emailVerified: boolean
  emailVerificationToken?: string | undefined
  emailVerificationExpires?: Date | undefined
  passwordResetToken?: string | undefined
  passwordResetExpires?: Date | undefined
  lastLoginAt?: Date | undefined
  profilePicture?: string | undefined
  phone?: string | undefined
  dateOfBirth?: Date | undefined
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  } | undefined
  preferences?: {
    language: string
    timezone: string
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
    theme: 'light' | 'dark' | 'auto'
  } | undefined
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
    bio?: string
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    }
  } | undefined
  createdAt: Date
  updatedAt: Date
  createdBy?: string | undefined
  updatedBy?: string | undefined
}

export interface CreateUserRequest {
  email: string
  firstName: string
  lastName: string
  password: string
  role: UserRole
  instituteId: string
  phone?: string
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
  }
}

export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  preferences?: {
    language: string
    timezone: string
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
    theme: 'light' | 'dark' | 'auto'
  }
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
    bio?: string
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    }
  }
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId: string
  instituteName: string
  status: UserStatus
  emailVerified: boolean
  lastLoginAt?: Date
  profilePicture?: string
  phone?: string
  dateOfBirth?: Date
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  preferences?: {
    language: string
    timezone: string
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
    theme: 'light' | 'dark' | 'auto'
  }
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
    bio?: string
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    }
  }
  createdAt: Date
  updatedAt: Date
}

export interface UserRegistrationRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  role: 'student' | 'teacher'
  instituteCode: string
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
  }
}

export interface EmailVerificationRequest {
  token: string
}

export interface ResendVerificationRequest {
  email: string
}

export interface UserSearchFilters {
  role?: UserRole
  status?: UserStatus
  emailVerified?: boolean
  instituteId?: string
  search?: string
  department?: string
  yearOfStudy?: number
}

export interface UserListResponse {
  users: UserProfile[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  filters: UserSearchFilters
}

// Validation constants
export const PASSWORD_MIN_LENGTH = 8
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
export const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/

export const SUPPORTED_LANGUAGES = [
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi'
]

export const SUPPORTED_TIMEZONES = [
  'UTC',
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Asia/Kolkata',
  'Australia/Sydney',
]

export const USER_ROLES_HIERARCHY = {
  [UserRole.SUPER_ADMIN]: 4,
  [UserRole.INSTITUTE_ADMIN]: 3,
  [UserRole.TEACHER]: 2,
  [UserRole.STUDENT]: 1,
}

export const DEFAULT_USER_PREFERENCES = {
  language: 'en',
  timezone: 'UTC',
  notifications: {
    email: true,
    push: true,
    sms: false,
  },
  theme: 'light' as const,
}

export const PROFILE_PICTURE_MAX_SIZE = 5 * 1024 * 1024 // 5MB
export const PROFILE_PICTURE_ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp']

export const EMAIL_VERIFICATION_TOKEN_EXPIRY = 24 * 60 * 60 * 1000 // 24 hours
export const PASSWORD_RESET_TOKEN_EXPIRY = 60 * 60 * 1000 // 1 hour
