const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

/**
 * Multi-Tenant Database Query Optimizer
 * Analyzes query patterns, optimizes indexes, and improves performance for multi-tenant LMS
 */

class MultiTenantQueryOptimizer {
  constructor() {
    this.pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'lte_lms',
      password: process.env.DB_PASSWORD || '1234',
      port: process.env.DB_PORT || 5432,
    });

    this.multiTenantTables = [
      'users', 'user_sessions', 'student_profiles', 'teacher_profiles',
      'course_enrollments', 'security_audit_log', 'failed_login_attempts',
      'institute_domains', 'institute_branding', 'teacher_invitations'
    ];

    this.optimizations = {
      indexesCreated: [],
      queriesOptimized: [],
      partitionsCreated: [],
      recommendations: []
    };
  }

  /**
   * Analyze query patterns for multi-tenant access
   */
  async analyzeQueryPatterns() {
    console.log('🔍 Analyzing multi-tenant query patterns...');

    try {
      // Check if pg_stat_statements is available
      const statsEnabledQuery = `
        SELECT EXISTS (
          SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
        ) as enabled;
      `;

      const statsResult = await this.pool.query(statsEnabledQuery);
      const statsEnabled = statsResult.rows[0].enabled;

      let queryAnalysis = {};

      if (statsEnabled) {
        // Analyze actual query patterns from pg_stat_statements
        const queryPatternsQuery = `
          SELECT 
            query,
            calls,
            total_time,
            mean_time,
            rows,
            100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
          FROM pg_stat_statements 
          WHERE query ILIKE '%institute_id%'
          OR query ILIKE '%WHERE%'
          ORDER BY total_time DESC 
          LIMIT 20;
        `;

        const result = await this.pool.query(queryPatternsQuery);
        queryAnalysis.actualQueries = result.rows;
      }

      // Analyze table access patterns
      const tableStatsQuery = `
        SELECT 
          schemaname,
          relname as tablename,
          seq_scan,
          seq_tup_read,
          idx_scan,
          idx_tup_fetch,
          n_tup_ins,
          n_tup_upd,
          n_tup_del,
          n_live_tup,
          CASE 
            WHEN seq_scan + idx_scan > 0 
            THEN round(100.0 * idx_scan / (seq_scan + idx_scan), 2)
            ELSE 0 
          END as index_usage_percent
        FROM pg_stat_user_tables 
        WHERE relname = ANY($1)
        ORDER BY n_live_tup DESC;
      `;

      const tableStatsResult = await this.pool.query(tableStatsQuery, [this.multiTenantTables]);

      // Analyze current index usage
      const indexStatsQuery = `
        SELECT 
          schemaname,
          relname as tablename,
          indexrelname as indexname,
          idx_scan,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE relname = ANY($1)
        ORDER BY idx_scan DESC;
      `;

      const indexStatsResult = await this.pool.query(indexStatsQuery, [this.multiTenantTables]);

      queryAnalysis.tableStats = tableStatsResult.rows;
      queryAnalysis.indexStats = indexStatsResult.rows;
      queryAnalysis.statsEnabled = statsEnabled;

      console.log(`📊 Analyzed ${tableStatsResult.rows.length} multi-tenant tables`);
      console.log(`📈 Found ${indexStatsResult.rows.length} indexes on multi-tenant tables`);

      return queryAnalysis;

    } catch (error) {
      console.error('Error analyzing query patterns:', error.message);
      throw error;
    }
  }

  /**
   * Create optimized indexes for multi-tenant queries
   */
  async createOptimizedIndexes() {
    console.log('🔧 Creating optimized indexes for multi-tenant queries...');

    const indexDefinitions = [
      // Multi-tenant user queries
      {
        name: 'idx_users_institute_email',
        table: 'users',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_institute_email ON users (institute_id, email)',
        purpose: 'Optimize user lookup by institute and email'
      },
      {
        name: 'idx_users_institute_role_active',
        table: 'users',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_institute_role_active ON users (institute_id, role, is_active) WHERE is_active = true',
        purpose: 'Optimize active user queries by institute and role'
      },

      // Session management
      {
        name: 'idx_user_sessions_institute_active',
        table: 'user_sessions',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_institute_active ON user_sessions (institute_id, is_active, expires_at) WHERE is_active = true',
        purpose: 'Optimize active session queries by institute'
      },
      {
        name: 'idx_user_sessions_user_active',
        table: 'user_sessions',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_active ON user_sessions (user_id, is_active) WHERE is_active = true',
        purpose: 'Optimize user session lookup'
      },

      // Student profiles
      {
        name: 'idx_student_profiles_user_status',
        table: 'student_profiles',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_student_profiles_user_status ON student_profiles (user_id, academic_status)',
        purpose: 'Optimize student profile queries by status'
      },

      // Security audit log
      {
        name: 'idx_security_audit_institute_time',
        table: 'security_audit_log',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_audit_institute_time ON security_audit_log (institute_id, created_at DESC) WHERE institute_id IS NOT NULL',
        purpose: 'Optimize security audit queries by institute and time'
      },
      {
        name: 'idx_security_audit_event_severity',
        table: 'security_audit_log',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_audit_event_severity ON security_audit_log (event_type, severity, created_at DESC)',
        purpose: 'Optimize security event filtering by type and severity'
      },

      // Failed login attempts
      {
        name: 'idx_failed_logins_institute_time',
        table: 'failed_login_attempts',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_failed_logins_institute_time ON failed_login_attempts (institute_id, attempt_time DESC)',
        purpose: 'Optimize failed login analysis by institute'
      },
      {
        name: 'idx_failed_logins_ip_time',
        table: 'failed_login_attempts',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_failed_logins_ip_time ON failed_login_attempts (ip_address, attempt_time DESC)',
        purpose: 'Optimize IP-based security analysis'
      },

      // Institute domains
      {
        name: 'idx_institute_domains_domain_active',
        table: 'institute_domains',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_institute_domains_domain_active ON institute_domains (domain, is_active) WHERE is_active = true',
        purpose: 'Optimize domain verification and routing'
      },

      // Course enrollments (for future expansion)
      {
        name: 'idx_course_enrollments_student_status',
        table: 'course_enrollments',
        definition: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_course_enrollments_student_status ON course_enrollments (student_id, enrollment_status)',
        purpose: 'Optimize student enrollment queries'
      }
    ];

    const results = [];

    for (const indexDef of indexDefinitions) {
      try {
        console.log(`📝 Creating index: ${indexDef.name}`);
        
        const startTime = Date.now();
        await this.pool.query(indexDef.definition);
        const duration = Date.now() - startTime;

        results.push({
          name: indexDef.name,
          table: indexDef.table,
          purpose: indexDef.purpose,
          created: true,
          duration,
          error: null
        });

        this.optimizations.indexesCreated.push(indexDef.name);
        console.log(`✅ Created ${indexDef.name} in ${duration}ms`);

      } catch (error) {
        results.push({
          name: indexDef.name,
          table: indexDef.table,
          purpose: indexDef.purpose,
          created: false,
          duration: 0,
          error: error.message
        });

        console.warn(`⚠️ Failed to create ${indexDef.name}: ${error.message}`);
      }
    }

    console.log(`🎯 Index creation completed: ${results.filter(r => r.created).length}/${results.length} successful`);
    return results;
  }

  /**
   * Analyze and optimize specific multi-tenant queries
   */
  async optimizeMultiTenantQueries() {
    console.log('⚡ Analyzing multi-tenant query performance...');

    const testQueries = [
      {
        name: 'Institute User Lookup',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT u.id, u.email, u.first_name, u.last_name, u.role, ur.permissions 
          FROM users u 
          LEFT JOIN user_roles ur ON u.id = ur.user_id 
          WHERE u.institute_id = $1 AND u.is_active = true
          ORDER BY u.created_at DESC
          LIMIT 50
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Active Sessions by Institute',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT us.id, us.user_id, us.created_at, us.last_activity, u.email 
          FROM user_sessions us 
          JOIN users u ON us.user_id = u.id 
          WHERE us.institute_id = $1 AND us.is_active = true 
          AND us.expires_at > CURRENT_TIMESTAMP
          ORDER BY us.last_activity DESC
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Student Profiles by Institute',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT u.id, u.first_name, u.last_name, sp.student_id, sp.major, sp.gpa 
          FROM users u 
          JOIN student_profiles sp ON u.id = sp.user_id 
          WHERE u.institute_id = $1 AND u.role = 'student' AND u.is_active = true
          ORDER BY sp.gpa DESC NULLS LAST
          LIMIT 100
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Security Events by Institute',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT sal.event_type, sal.severity, sal.created_at, sal.ip_address, u.email 
          FROM security_audit_log sal 
          LEFT JOIN users u ON sal.user_id = u.id 
          WHERE sal.institute_id = $1 
          AND sal.created_at >= CURRENT_DATE - INTERVAL '7 days'
          ORDER BY sal.created_at DESC
          LIMIT 200
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      },
      {
        name: 'Failed Login Analysis',
        query: `
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT ip_address, COUNT(*) as attempt_count, MAX(attempt_time) as latest_attempt
          FROM failed_login_attempts 
          WHERE institute_id = $1 
          AND attempt_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
          GROUP BY ip_address
          HAVING COUNT(*) > 3
          ORDER BY attempt_count DESC
        `,
        params: ['7a731a99-1277-4240-ad1c-d34a674f01c3']
      }
    ];

    const results = [];

    for (const testQuery of testQueries) {
      try {
        const result = await this.pool.query(testQuery.query, testQuery.params);
        const plan = result.rows[0]['QUERY PLAN'][0];
        
        results.push({
          name: testQuery.name,
          executionTime: plan['Execution Time'],
          planningTime: plan['Planning Time'],
          totalCost: plan.Plan['Total Cost'],
          actualRows: plan.Plan['Actual Rows'],
          indexUsed: this.extractIndexUsage(plan.Plan),
          bufferHits: this.extractBufferStats(plan.Plan)
        });

        console.log(`📊 ${testQuery.name}: ${plan['Execution Time'].toFixed(2)}ms execution`);

      } catch (error) {
        results.push({
          name: testQuery.name,
          error: error.message
        });
        console.error(`❌ Query analysis failed for ${testQuery.name}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Extract index usage from query plan
   */
  extractIndexUsage(plan) {
    const indexes = [];
    
    const extractFromNode = (node) => {
      if (node['Node Type'] && node['Node Type'].includes('Index')) {
        if (node['Index Name']) {
          indexes.push(node['Index Name']);
        }
      }
      
      if (node.Plans) {
        node.Plans.forEach(extractFromNode);
      }
    };

    extractFromNode(plan);
    return indexes;
  }

  /**
   * Extract buffer statistics from query plan
   */
  extractBufferStats(plan) {
    let totalHits = 0;
    let totalReads = 0;

    const extractFromNode = (node) => {
      if (node['Shared Hit Blocks']) {
        totalHits += node['Shared Hit Blocks'];
      }
      if (node['Shared Read Blocks']) {
        totalReads += node['Shared Read Blocks'];
      }
      
      if (node.Plans) {
        node.Plans.forEach(extractFromNode);
      }
    };

    extractFromNode(plan);
    
    const hitRatio = totalHits + totalReads > 0 ? 
      (totalHits / (totalHits + totalReads) * 100).toFixed(2) : 0;

    return {
      hits: totalHits,
      reads: totalReads,
      hitRatio: parseFloat(hitRatio)
    };
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(queryAnalysis, queryResults) {
    const recommendations = [];

    // Analyze table scan ratios
    queryAnalysis.tableStats.forEach(table => {
      if (table.index_usage_percent < 80 && table.n_live_tup > 1000) {
        recommendations.push({
          type: 'Index Usage',
          priority: 'high',
          table: table.tablename,
          issue: `Low index usage: ${table.index_usage_percent}%`,
          recommendation: `Consider adding indexes for frequently queried columns on ${table.tablename}`
        });
      }
    });

    // Analyze query performance
    queryResults.forEach(query => {
      if (query.executionTime > 50) {
        recommendations.push({
          type: 'Query Performance',
          priority: 'medium',
          query: query.name,
          issue: `Slow execution time: ${query.executionTime.toFixed(2)}ms`,
          recommendation: `Optimize query or add missing indexes for ${query.name}`
        });
      }

      if (query.bufferHits && query.bufferHits.hitRatio < 95) {
        recommendations.push({
          type: 'Buffer Cache',
          priority: 'medium',
          query: query.name,
          issue: `Low buffer hit ratio: ${query.bufferHits.hitRatio}%`,
          recommendation: `Consider increasing shared_buffers or optimizing data access patterns`
        });
      }
    });

    // Multi-tenant specific recommendations
    recommendations.push({
      type: 'Multi-Tenancy',
      priority: 'high',
      issue: 'Ensure all tenant-scoped queries use institute_id in WHERE clauses',
      recommendation: 'Review application queries to ensure proper tenant isolation and index usage'
    });

    return recommendations;
  }

  /**
   * Run complete optimization analysis
   */
  async runOptimization() {
    console.log('🚀 Starting multi-tenant database optimization...\n');

    try {
      // Step 1: Analyze current query patterns
      const queryAnalysis = await this.analyzeQueryPatterns();

      // Step 2: Create optimized indexes
      const indexResults = await this.createOptimizedIndexes();

      // Step 3: Test multi-tenant query performance
      const queryResults = await this.optimizeMultiTenantQueries();

      // Step 4: Generate recommendations
      const recommendations = this.generateRecommendations(queryAnalysis, queryResults);

      const report = {
        timestamp: new Date().toISOString(),
        queryAnalysis,
        indexResults,
        queryResults,
        recommendations,
        summary: {
          indexesCreated: indexResults.filter(r => r.created).length,
          totalIndexes: indexResults.length,
          avgQueryTime: queryResults.reduce((sum, q) => sum + (q.executionTime || 0), 0) / queryResults.length,
          recommendationCount: recommendations.length
        }
      };

      // Save optimization report
      const reportPath = path.join(__dirname, '../reports/optimization-report.json');
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

      console.log('\n📊 Multi-Tenant Optimization Summary:');
      console.log(`🔧 Indexes Created: ${report.summary.indexesCreated}/${report.summary.totalIndexes}`);
      console.log(`⚡ Average Query Time: ${report.summary.avgQueryTime.toFixed(2)}ms`);
      console.log(`💡 Recommendations: ${report.summary.recommendationCount}`);
      console.log(`📄 Report saved to: ${reportPath}`);

      if (recommendations.length > 0) {
        console.log('\n🔧 Top Recommendations:');
        recommendations.slice(0, 3).forEach((rec, index) => {
          console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.recommendation}`);
        });
      }

      return report;

    } catch (error) {
      console.error('❌ Optimization failed:', error.message);
      throw error;
    } finally {
      await this.pool.end();
    }
  }
}

// Run optimization if script is executed directly
if (require.main === module) {
  const optimizer = new MultiTenantQueryOptimizer();
  optimizer.runOptimization()
    .then(() => {
      console.log('\n✅ Multi-tenant database optimization completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Optimization failed:', error.message);
      process.exit(1);
    });
}

module.exports = { MultiTenantQueryOptimizer };
