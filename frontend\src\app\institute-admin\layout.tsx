import React from 'react'
import InstituteAdminRoute from '@/components/auth/InstituteAdminRoute'
import InstituteAdminSidebar from '@/components/institute-admin/InstituteAdminSidebar'
import InstituteAdminHeader from '@/components/institute-admin/InstituteAdminHeader'

interface InstituteAdminLayoutProps {
  children: React.ReactNode
}

export default function InstituteAdminLayout({ children }: InstituteAdminLayoutProps) {
  return (
    <InstituteAdminRoute>
      <div className="min-h-screen bg-gray-50">
        <InstituteAdminSidebar />
        <div className="lg:pl-64">
          <InstituteAdminHeader />
          <main className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </InstituteAdminRoute>
  )
}
