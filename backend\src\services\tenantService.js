const { query, transaction } = require('../database/connection');

/**
 * Tenant-Aware Database Service
 * Provides scoped database operations that automatically enforce tenant isolation
 */

class TenantService {
  constructor(instituteId = null) {
    this.instituteId = instituteId;
  }

  /**
   * Validate that institute ID is provided for tenant-scoped operations
   */
  validateTenantScope() {
    if (!this.instituteId) {
      throw new Error('Institute ID is required for tenant-scoped operations');
    }
  }

  /**
   * Execute a tenant-scoped query
   * Automatically adds institute_id filter to WHERE clause
   */
  async scopedQuery(sql, params = [], options = {}) {
    const { allowSuperAdmin = false, tableName = null } = options;

    // Super admin queries (no institute scoping)
    if (!this.instituteId && allowSuperAdmin) {
      return await query(sql, params);
    }

    // Require institute ID for tenant-scoped queries
    this.validateTenantScope();

    // Add institute_id to parameters
    const scopedParams = [...params, this.instituteId];
    
    // Modify SQL to include institute_id filter
    let scopedSql = sql;
    
    if (tableName) {
      // If table name is provided, add WHERE clause or AND condition
      if (sql.toLowerCase().includes('where')) {
        scopedSql = sql.replace(/where/i, `WHERE ${tableName}.institute_id = $${scopedParams.length} AND`);
      } else {
        // Add WHERE clause before ORDER BY, GROUP BY, LIMIT, etc.
        const keywords = ['ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT', 'OFFSET'];
        let insertPoint = sql.length;
        
        for (const keyword of keywords) {
          const index = sql.toUpperCase().indexOf(keyword);
          if (index !== -1 && index < insertPoint) {
            insertPoint = index;
          }
        }
        
        scopedSql = sql.slice(0, insertPoint) + ` WHERE ${tableName}.institute_id = $${scopedParams.length} ` + sql.slice(insertPoint);
      }
    }

    return await query(scopedSql, scopedParams);
  }

  /**
   * Get users for current institute
   */
  async getUsers(filters = {}) {
    this.validateTenantScope(); // Add validation

    const { role, isActive = true, limit = 100, offset = 0 } = filters;

    let sql = `
      SELECT id, email, first_name, last_name, phone, avatar_url, role,
             is_active, is_email_verified, last_login_at, created_at, updated_at
      FROM users
      WHERE institute_id = $1 AND is_active = $2
    `;

    const params = [this.instituteId, isActive];

    if (role) {
      sql += ` AND role = $${params.length + 1}`;
      params.push(role);
    }

    sql += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);

    return await query(sql, params);
  }

  /**
   * Get user by ID (tenant-scoped)
   */
  async getUserById(userId) {
    const result = await query(
      'SELECT * FROM users WHERE id = $1 AND institute_id = $2',
      [userId, this.instituteId]
    );
    return result.rows[0] || null;
  }

  /**
   * Get user by email (tenant-scoped)
   */
  async getUserByEmail(email) {
    const result = await query(
      'SELECT * FROM users WHERE email = $1 AND institute_id = $2',
      [email, this.instituteId]
    );
    return result.rows[0] || null;
  }

  /**
   * Create user (tenant-scoped)
   */
  async createUser(userData) {
    this.validateTenantScope();
    
    const {
      email, password_hash, first_name, last_name, phone, avatar_url, role
    } = userData;

    const result = await query(`
      INSERT INTO users (institute_id, email, password_hash, first_name, last_name, phone, avatar_url, role)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [this.instituteId, email, password_hash, first_name, last_name, phone, avatar_url, role]);

    return result.rows[0];
  }

  /**
   * Update user (tenant-scoped)
   */
  async updateUser(userId, updates) {
    this.validateTenantScope();
    
    const allowedFields = ['first_name', 'last_name', 'phone', 'avatar_url', 'is_active', 'is_email_verified'];
    const updateFields = [];
    const params = [userId, this.instituteId];
    
    Object.keys(updates).forEach(field => {
      if (allowedFields.includes(field)) {
        updateFields.push(`${field} = $${params.length + 1}`);
        params.push(updates[field]);
      }
    });
    
    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }
    
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    
    const sql = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $1 AND institute_id = $2
      RETURNING *
    `;
    
    const result = await query(sql, params);
    return result.rows[0] || null;
  }

  /**
   * Get institute settings
   */
  async getInstituteSettings(isPublic = null) {
    this.validateTenantScope();
    
    let sql = 'SELECT setting_key, setting_value, setting_type FROM institute_settings WHERE institute_id = $1';
    const params = [this.instituteId];
    
    if (isPublic !== null) {
      sql += ` AND is_public = $${params.length + 1}`;
      params.push(isPublic);
    }
    
    const result = await query(sql, params);
    
    // Convert to key-value object
    const settings = {};
    result.rows.forEach(row => {
      try {
        settings[row.setting_key] = JSON.parse(row.setting_value);
      } catch {
        settings[row.setting_key] = row.setting_value;
      }
    });
    
    return settings;
  }

  /**
   * Set institute setting
   */
  async setInstituteSetting(key, value, type = 'string', isPublic = false) {
    this.validateTenantScope();
    
    const result = await query(`
      INSERT INTO institute_settings (institute_id, setting_key, setting_value, setting_type, is_public)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (institute_id, setting_key) 
      DO UPDATE SET 
        setting_value = EXCLUDED.setting_value,
        setting_type = EXCLUDED.setting_type,
        is_public = EXCLUDED.is_public,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `, [this.instituteId, key, JSON.stringify(value), type, isPublic]);
    
    return result.rows[0];
  }

  /**
   * Get user roles for current institute
   */
  async getUserRoles(userId = null) {
    this.validateTenantScope();
    
    let sql = `
      SELECT ur.*, u.email, u.first_name, u.last_name
      FROM user_roles ur
      JOIN users u ON ur.user_id = u.id
      WHERE ur.institute_id = $1 AND ur.is_active = true
    `;
    
    const params = [this.instituteId];
    
    if (userId) {
      sql += ` AND ur.user_id = $${params.length + 1}`;
      params.push(userId);
    }
    
    return await query(sql, params);
  }

  /**
   * Create user role (tenant-scoped)
   */
  async createUserRole(userId, roleName, permissions = []) {
    this.validateTenantScope();
    
    const result = await query(`
      INSERT INTO user_roles (user_id, institute_id, role_name, permissions)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [userId, this.instituteId, roleName, JSON.stringify(permissions)]);
    
    return result.rows[0];
  }

  /**
   * Execute transaction with tenant scoping
   */
  async executeTransaction(callback) {
    this.validateTenantScope();
    
    return await transaction(async (client) => {
      // Create a scoped client that automatically adds institute_id
      const scopedClient = {
        query: async (sql, params = []) => {
          // Add institute_id to all queries in transaction
          const scopedParams = [...params, this.instituteId];
          return await client.query(sql, scopedParams);
        }
      };
      
      return await callback(scopedClient);
    });
  }

  /**
   * Validate user belongs to current institute
   */
  async validateUserAccess(userId) {
    this.validateTenantScope();
    
    const result = await query(
      'SELECT id FROM users WHERE id = $1 AND institute_id = $2',
      [userId, this.instituteId]
    );
    
    return result.rows.length > 0;
  }

  /**
   * Get institute information
   */
  async getInstituteInfo() {
    this.validateTenantScope();
    
    const result = await query(
      'SELECT * FROM institutes WHERE id = $1 AND is_active = true',
      [this.instituteId]
    );
    
    return result.rows[0] || null;
  }
}

/**
 * Factory function to create tenant service
 */
const createTenantService = (req) => {
  const instituteId = req.tenant?.instituteId || null;
  return new TenantService(instituteId);
};

/**
 * Super admin service (no tenant scoping)
 */
class SuperAdminService {
  /**
   * Get all institutes
   */
  async getAllInstitutes(filters = {}) {
    const { isActive = true, limit = 100, offset = 0 } = filters;
    
    let sql = 'SELECT * FROM institutes WHERE is_active = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3';
    return await query(sql, [isActive, limit, offset]);
  }

  /**
   * Get institute by ID
   */
  async getInstituteById(instituteId) {
    const result = await query('SELECT * FROM institutes WHERE id = $1', [instituteId]);
    return result.rows[0] || null;
  }

  /**
   * Create institute
   */
  async createInstitute(instituteData) {
    const result = await query(`
      INSERT INTO institutes (name, slug, email, phone, address, website, subscription_plan)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      instituteData.name,
      instituteData.slug,
      instituteData.email,
      instituteData.phone,
      instituteData.address,
      instituteData.website,
      instituteData.subscription_plan || 'basic'
    ]);
    
    return result.rows[0];
  }

  /**
   * Get all users across all institutes
   */
  async getAllUsers(filters = {}) {
    const { instituteId, role, limit = 100, offset = 0 } = filters;
    
    let sql = `
      SELECT u.*, i.name as institute_name 
      FROM users u 
      LEFT JOIN institutes i ON u.institute_id = i.id 
      WHERE 1=1
    `;
    const params = [];
    
    if (instituteId) {
      sql += ` AND u.institute_id = $${params.length + 1}`;
      params.push(instituteId);
    }
    
    if (role) {
      sql += ` AND u.role = $${params.length + 1}`;
      params.push(role);
    }
    
    sql += ` ORDER BY u.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);
    
    return await query(sql, params);
  }
}

module.exports = {
  TenantService,
  SuperAdminService,
  createTenantService
};
