const superAdminService = require('../services/superAdminService');
const authService = require('../services/authService');
const { query } = require('../database/connection');
const axios = require('axios');

/**
 * Super Admin Platform Management Test Suite
 * Tests super admin dashboard, analytics, institute management, and access controls
 */

class SuperAdminTester {
  constructor() {
    this.testResults = [];
    this.baseURL = 'http://localhost:5010';
    this.testData = {
      superAdmin: null,
      testInstitute: null,
      regularUser: null,
      authToken: null
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test super admin service functionality
   */
  async testSuperAdminService() {
    console.log('\n🔧 Testing Super Admin Service...');

    try {
      // Test platform health metrics
      const health = await superAdminService.getPlatformHealth();
      
      this.logResult(
        'Platform Health Metrics',
        health && typeof health.healthScore === 'number' && health.status,
        `Health Score: ${health.healthScore}, Status: ${health.status}`
      );

      // Test daily analytics generation
      const analytics = await superAdminService.generateDailyAnalytics();
      
      this.logResult(
        'Daily Analytics Generation',
        analytics && typeof analytics.new_institutes !== 'undefined',
        `Generated analytics with ${analytics.new_institutes} new institutes`
      );

      // Test feature flag checking
      const featureEnabled = await superAdminService.checkFeatureFlag('advanced_analytics', null, 'premium');
      
      this.logResult(
        'Feature Flag Check',
        typeof featureEnabled === 'boolean',
        `Advanced analytics for premium: ${featureEnabled}`
      );

      // Test notification creation
      if (this.testData.superAdmin) {
        const notification = await superAdminService.createNotification({
          notificationType: 'test_notification',
          title: 'Test Notification',
          message: 'This is a test notification',
          severity: 'info',
          createdBy: this.testData.superAdmin.id
        });

        this.logResult(
          'Notification Creation',
          notification && notification.id,
          `Created notification: ${notification?.title}`
        );
      }

    } catch (error) {
      this.logResult('Super Admin Service', false, error.message);
    }
  }

  /**
   * Test super admin API endpoints
   */
  async testSuperAdminAPI() {
    console.log('\n🌐 Testing Super Admin API Endpoints...');

    if (!this.testData.authToken) {
      this.logResult('Super Admin API', false, 'No auth token available');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${this.testData.authToken}`,
      'Content-Type': 'application/json'
    };

    try {
      // Test dashboard stats endpoint
      const statsResponse = await axios.get(`${this.baseURL}/api/super-admin/dashboard/stats`, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Dashboard Stats API',
        statsResponse.status === 200 && statsResponse.data.success,
        `Retrieved stats: ${statsResponse.data.stats?.total_institutes} institutes`
      );

      // Test institutes listing endpoint
      const institutesResponse = await axios.get(`${this.baseURL}/api/super-admin/institutes`, {
        headers,
        params: { page: 1, limit: 10 },
        timeout: 5000
      });

      this.logResult(
        'Institutes Listing API',
        institutesResponse.status === 200 && institutesResponse.data.success,
        `Retrieved ${institutesResponse.data.institutes?.length} institutes`
      );

      // Test growth analytics endpoint
      const growthResponse = await axios.get(`${this.baseURL}/api/super-admin/analytics/growth`, {
        headers,
        params: { period: '30d' },
        timeout: 5000
      });

      this.logResult(
        'Growth Analytics API',
        growthResponse.status === 200 && growthResponse.data.success,
        `Retrieved growth data for ${growthResponse.data.period}`
      );

      // Test security analytics endpoint
      const securityResponse = await axios.get(`${this.baseURL}/api/super-admin/analytics/security`, {
        headers,
        params: { period: '7d' },
        timeout: 5000
      });

      this.logResult(
        'Security Analytics API',
        securityResponse.status === 200 && securityResponse.data.success,
        `Retrieved security data: ${securityResponse.data.securityEvents?.length} event types`
      );

      // Test users listing endpoint
      const usersResponse = await axios.get(`${this.baseURL}/api/super-admin/users`, {
        headers,
        params: { page: 1, limit: 10 },
        timeout: 5000
      });

      this.logResult(
        'Users Listing API',
        usersResponse.status === 200 && usersResponse.data.success,
        `Retrieved ${usersResponse.data.users?.length} users`
      );

    } catch (error) {
      this.logResult('Super Admin API', false, error.response?.data?.error || error.message);
    }
  }

  /**
   * Test access control for super admin endpoints
   */
  async testAccessControl() {
    console.log('\n🛡️ Testing Super Admin Access Control...');

    try {
      // Test access without authentication
      try {
        await axios.get(`${this.baseURL}/api/super-admin/dashboard/stats`, {
          timeout: 5000
        });
        this.logResult('Unauthenticated Access', false, 'Should have been denied');
      } catch (error) {
        this.logResult(
          'Unauthenticated Access',
          error.response?.status === 401,
          'Access correctly denied'
        );
      }

      // Test access with regular user token (if available)
      if (this.testData.regularUser) {
        try {
          // Create a regular user token
          const mockReq = {
            ip: '127.0.0.1',
            get: (header) => header === 'User-Agent' ? 'Test-Agent' : null,
            tenant: { instituteId: this.testData.testInstitute?.id }
          };

          const loginResult = await authService.loginUser(
            this.testData.regularUser.email,
            'TestPassword123!',
            mockReq
          );

          const regularUserToken = loginResult.accessToken;

          await axios.get(`${this.baseURL}/api/super-admin/dashboard/stats`, {
            headers: { 'Authorization': `Bearer ${regularUserToken}` },
            timeout: 5000
          });

          this.logResult('Regular User Access', false, 'Should have been denied');

        } catch (error) {
          this.logResult(
            'Regular User Access',
            error.response?.status === 403,
            'Access correctly denied for regular user'
          );
        }
      }

    } catch (error) {
      this.logResult('Access Control', false, error.message);
    }
  }

  /**
   * Test institute management operations
   */
  async testInstituteManagement() {
    console.log('\n🏢 Testing Institute Management...');

    if (!this.testData.authToken || !this.testData.testInstitute) {
      this.logResult('Institute Management', false, 'Missing auth token or test institute');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${this.testData.authToken}`,
      'Content-Type': 'application/json'
    };

    try {
      // Test getting institute details
      const detailsResponse = await axios.get(
        `${this.baseURL}/api/super-admin/institutes/${this.testData.testInstitute.id}`,
        { headers, timeout: 5000 }
      );

      this.logResult(
        'Get Institute Details',
        detailsResponse.status === 200 && detailsResponse.data.success,
        `Retrieved details for ${detailsResponse.data.institute?.name}`
      );

      // Test updating subscription
      const subscriptionResponse = await axios.put(
        `${this.baseURL}/api/super-admin/institutes/${this.testData.testInstitute.id}/subscription`,
        {
          subscription_plan: 'premium',
          subscription_status: 'active',
          notes: 'Test subscription update'
        },
        { headers, timeout: 5000 }
      );

      this.logResult(
        'Update Subscription',
        subscriptionResponse.status === 200 && subscriptionResponse.data.success,
        `Updated to ${subscriptionResponse.data.institute?.subscription_plan}`
      );

      // Test updating institute status
      const statusResponse = await axios.put(
        `${this.baseURL}/api/super-admin/institutes/${this.testData.testInstitute.id}/status`,
        {
          is_active: true,
          reason: 'Test status update'
        },
        { headers, timeout: 5000 }
      );

      this.logResult(
        'Update Institute Status',
        statusResponse.status === 200 && statusResponse.data.success,
        `Status updated: ${statusResponse.data.institute?.is_active ? 'active' : 'inactive'}`
      );

    } catch (error) {
      this.logResult('Institute Management', false, error.response?.data?.error || error.message);
    }
  }

  /**
   * Test analytics and reporting
   */
  async testAnalyticsReporting() {
    console.log('\n📊 Testing Analytics and Reporting...');

    try {
      // Test revenue analytics
      const revenue = await superAdminService.getRevenueAnalytics('30d');
      
      this.logResult(
        'Revenue Analytics',
        revenue && typeof revenue.totalMonthlyRevenue === 'number',
        `Total monthly revenue: $${revenue.totalMonthlyRevenue}`
      );

      // Test institute usage stats
      if (this.testData.testInstitute) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        const endDate = new Date();

        // First update some usage stats
        await superAdminService.updateInstituteUsageStats(
          this.testData.testInstitute.id,
          new Date().toISOString().split('T')[0],
          {
            activeUsers: 10,
            newUsers: 2,
            totalLogins: 25,
            storageUsedMb: 150,
            apiRequests: 500,
            featureUsage: { 'course_creation': 5, 'user_management': 15 }
          }
        );

        const usageStats = await superAdminService.getInstituteUsageStats(
          this.testData.testInstitute.id,
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0]
        );

        this.logResult(
          'Institute Usage Stats',
          Array.isArray(usageStats),
          `Retrieved ${usageStats.length} usage records`
        );
      }

    } catch (error) {
      this.logResult('Analytics Reporting', false, error.message);
    }
  }

  /**
   * Create test data
   */
  async createTestData() {
    try {
      // Create super admin user
      const superAdminData = {
        email: `superadmin${Date.now()}@lms-saas.com`,
        password: 'SuperAdmin123!',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin'
      };

      // Create super admin directly in database
      const hashedPassword = await require('../utils/auth').passwordUtils.hashPassword(superAdminData.password);
      
      const superAdminResult = await query(`
        INSERT INTO users (email, password_hash, first_name, last_name, role, is_email_verified, is_active)
        VALUES ($1, $2, $3, $4, $5, TRUE, TRUE)
        RETURNING *
      `, [
        superAdminData.email,
        hashedPassword,
        superAdminData.firstName,
        superAdminData.lastName,
        superAdminData.role
      ]);

      this.testData.superAdmin = superAdminResult.rows[0];

      // Create test institute
      const instituteData = {
        email: `admin${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Admin',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `contact${Date.now()}@testinstitute.edu`,
        instituteAddress: '123 Test Street'
      };

      const instituteResult = await authService.registerInstituteAdmin(instituteData);
      this.testData.testInstitute = instituteResult.institute;
      this.testData.regularUser = instituteResult.user;

      // Login as super admin to get token
      const mockReq = {
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test-Agent' : null
      };

      const loginResult = await authService.loginUser(
        this.testData.superAdmin.email,
        superAdminData.password,
        mockReq
      );

      this.testData.authToken = loginResult.accessToken;

      console.log('✅ Test data created successfully');

    } catch (error) {
      console.error('❌ Test data creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test notifications
      await query(`DELETE FROM platform_notifications WHERE notification_type = 'test_notification'`);

      // Delete test users and institute
      if (this.testData.superAdmin?.id) {
        await query('DELETE FROM users WHERE id = $1', [this.testData.superAdmin.id]);
      }

      if (this.testData.regularUser?.id) {
        await query('DELETE FROM users WHERE id = $1', [this.testData.regularUser.id]);
      }

      if (this.testData.testInstitute?.id) {
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.testInstitute.id]);
      }

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all super admin tests
   */
  async runAllTests() {
    console.log('🚀 Starting Super Admin Platform Management Tests...\n');

    await this.createTestData();
    await this.testSuperAdminService();
    await this.testSuperAdminAPI();
    await this.testAccessControl();
    await this.testInstituteManagement();
    await this.testAnalyticsReporting();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Super Admin Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new SuperAdminTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Super admin testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { SuperAdminTester };
