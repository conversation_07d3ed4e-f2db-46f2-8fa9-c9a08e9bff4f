const domainService = require('../services/domainService');
const brandingService = require('../services/brandingService');
const authService = require('../services/authService');
const { query } = require('../database/connection');

/**
 * Domain Management Test Suite
 * Tests custom domain registration, verification, SSL, and landing page generation
 */

class DomainTester {
  constructor() {
    this.testResults = [];
    this.testData = {
      testInstitute: null,
      testUser: null,
      testDomains: []
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test domain validation
   */
  async testDomainValidation() {
    console.log('\n🌐 Testing Domain Validation...');

    try {
      // Test valid domains
      const validDomains = [
        'example.com',
        'test-institute.edu',
        'my.university.ac.uk',
        'school123.org'
      ];

      validDomains.forEach(domain => {
        const isValid = domainService.isValidDomain(domain);
        this.logResult(
          `Valid Domain - ${domain}`,
          isValid === true,
          isValid ? 'Accepted' : 'Rejected'
        );
      });

      // Test invalid domains
      const invalidDomains = [
        'invalid..domain.com',
        '-invalid.com',
        'invalid-.com',
        'toolongdomainnamethatexceedsmaximumlength.com',
        'invalid domain.com',
        '.invalid.com'
      ];

      invalidDomains.forEach(domain => {
        const isValid = domainService.isValidDomain(domain);
        this.logResult(
          `Invalid Domain - ${domain}`,
          isValid === false,
          isValid ? 'Incorrectly accepted' : 'Correctly rejected'
        );
      });

    } catch (error) {
      this.logResult('Domain Validation', false, error.message);
    }
  }

  /**
   * Test custom domain addition
   */
  async testCustomDomainAddition() {
    console.log('\n➕ Testing Custom Domain Addition...');

    try {
      // Create test institute and user
      await this.createTestData();

      if (!this.testData.testInstitute) {
        this.logResult('Custom Domain Addition', false, 'No test institute available');
        return;
      }

      const testDomain = `test${Date.now()}.example.com`;
      
      // Add custom domain
      const result = await domainService.addCustomDomain(
        this.testData.testInstitute.id,
        {
          domain: testDomain,
          domainType: 'custom',
          isPrimary: true
        }
      );

      this.logResult(
        'Add Custom Domain',
        result.domain && result.verificationInstructions,
        `Domain: ${result.domain.domain}, Token: ${result.domain.verification_token.substring(0, 10)}...`
      );

      this.testData.testDomains.push(result.domain);

      // Test verification instructions
      const instructions = result.verificationInstructions;
      this.logResult(
        'Verification Instructions',
        instructions.method === 'DNS TXT Record' && instructions.recordName === '_lms-verification',
        `Method: ${instructions.method}, Record: ${instructions.recordName}`
      );

      // Test duplicate domain addition
      try {
        await domainService.addCustomDomain(
          this.testData.testInstitute.id,
          { domain: testDomain }
        );
        this.logResult('Duplicate Domain Prevention', false, 'Should have failed but succeeded');
      } catch (error) {
        this.logResult(
          'Duplicate Domain Prevention',
          error.message.includes('already registered'),
          'Duplicate domain correctly rejected'
        );
      }

    } catch (error) {
      this.logResult('Custom Domain Addition', false, error.message);
    }
  }

  /**
   * Test DNS verification simulation
   */
  async testDNSVerification() {
    console.log('\n🔍 Testing DNS Verification...');

    if (this.testData.testDomains.length === 0) {
      this.logResult('DNS Verification', false, 'No test domains available');
      return;
    }

    try {
      const testDomain = this.testData.testDomains[0];

      // Test DNS verification (will fail since we don't actually set DNS records)
      const verificationResult = await domainService.verifyDomainOwnership(testDomain.id);

      this.logResult(
        'DNS Verification Attempt',
        verificationResult.verified === false && verificationResult.troubleshooting,
        `Verified: ${verificationResult.verified}, Message: ${verificationResult.message}`
      );

      // Test verification record format
      const expectedRecord = testDomain.verification_record;
      this.logResult(
        'Verification Record Format',
        expectedRecord.startsWith('lms-verify=') && expectedRecord.length > 20,
        `Record: ${expectedRecord.substring(0, 20)}...`
      );

    } catch (error) {
      this.logResult('DNS Verification', false, error.message);
    }
  }

  /**
   * Test SSL certificate simulation
   */
  async testSSLCertificate() {
    console.log('\n🔒 Testing SSL Certificate Management...');

    if (this.testData.testDomains.length === 0) {
      this.logResult('SSL Certificate', false, 'No test domains available');
      return;
    }

    try {
      const testDomain = this.testData.testDomains[0];

      // Manually mark domain as verified for SSL testing
      await query(
        'UPDATE institute_domains SET is_verified = TRUE, verified_at = CURRENT_TIMESTAMP WHERE id = $1',
        [testDomain.id]
      );

      // Request SSL certificate
      const sslResult = await domainService.requestSSLCertificate(testDomain.id);

      this.logResult(
        'SSL Certificate Request',
        sslResult.success && sslResult.certificateId,
        `Certificate ID: ${sslResult.certificateId}, Expires: ${sslResult.expiresAt}`
      );

      // Test SSL renewal
      const renewalResult = await domainService.renewSSLCertificate(testDomain.id);

      this.logResult(
        'SSL Certificate Renewal',
        renewalResult.success && renewalResult.certificateId,
        `Renewed Certificate ID: ${renewalResult.certificateId}`
      );

    } catch (error) {
      this.logResult('SSL Certificate', false, error.message);
    }
  }

  /**
   * Test institute branding
   */
  async testInstituteBranding() {
    console.log('\n🎨 Testing Institute Branding...');

    if (!this.testData.testInstitute) {
      this.logResult('Institute Branding', false, 'No test institute available');
      return;
    }

    try {
      // Get default branding
      const defaultBranding = await brandingService.getInstituteBranding(
        this.testData.testInstitute.id
      );

      this.logResult(
        'Default Branding Creation',
        defaultBranding && defaultBranding.primary_color === '#007bff',
        `Primary Color: ${defaultBranding.primary_color}, Font: ${defaultBranding.font_family}`
      );

      // Update branding
      const updatedBranding = await brandingService.updateInstituteBranding(
        this.testData.testInstitute.id,
        {
          primaryColor: '#ff6b35',
          secondaryColor: '#004e89',
          logoUrl: 'https://example.com/logo.png',
          landingPageContent: {
            subtitle: 'Excellence in Online Education',
            featuresTitle: 'Why Choose Our Platform?'
          },
          socialLinks: {
            facebook: 'https://facebook.com/testinstitute',
            twitter: 'https://twitter.com/testinstitute'
          },
          contactInfo: {
            email: '<EMAIL>',
            phone: '******-0123'
          }
        }
      );

      this.logResult(
        'Branding Update',
        updatedBranding.primary_color === '#ff6b35' && updatedBranding.logo_url,
        `Updated Primary Color: ${updatedBranding.primary_color}`
      );

    } catch (error) {
      this.logResult('Institute Branding', false, error.message);
    }
  }

  /**
   * Test landing page generation
   */
  async testLandingPageGeneration() {
    console.log('\n📄 Testing Landing Page Generation...');

    if (!this.testData.testInstitute || this.testData.testDomains.length === 0) {
      this.logResult('Landing Page Generation', false, 'No test data available');
      return;
    }

    try {
      const testDomain = this.testData.testDomains[0];

      // Generate landing page
      const landingPage = await brandingService.generateLandingPage(
        this.testData.testInstitute.id,
        testDomain.domain
      );

      this.logResult(
        'Landing Page Generation',
        landingPage.html && landingPage.metadata,
        `HTML Length: ${landingPage.html.length} chars, Title: ${landingPage.metadata.title}`
      );

      // Test HTML content
      const htmlContent = landingPage.html;
      const hasInstituteName = htmlContent.includes(this.testData.testInstitute.name);
      const hasCSS = htmlContent.includes(':root {') && htmlContent.includes('--primary-color');
      const hasResponsive = htmlContent.includes('viewport') && htmlContent.includes('@media');

      this.logResult(
        'Landing Page Content',
        hasInstituteName && hasCSS && hasResponsive,
        `Institute Name: ${hasInstituteName}, CSS Variables: ${hasCSS}, Responsive: ${hasResponsive}`
      );

      // Test available templates
      const templates = brandingService.getAvailableTemplates();
      this.logResult(
        'Available Templates',
        templates.length >= 4 && templates[0].id === 'default',
        `${templates.length} templates available`
      );

    } catch (error) {
      this.logResult('Landing Page Generation', false, error.message);
    }
  }

  /**
   * Test domain management operations
   */
  async testDomainManagement() {
    console.log('\n⚙️ Testing Domain Management...');

    if (!this.testData.testInstitute) {
      this.logResult('Domain Management', false, 'No test institute available');
      return;
    }

    try {
      // Get institute domains
      const domains = await domainService.getInstituteDomains(this.testData.testInstitute.id);

      this.logResult(
        'Get Institute Domains',
        Array.isArray(domains) && domains.length > 0,
        `Found ${domains.length} domains`
      );

      // Test domain lookup by name
      if (this.testData.testDomains.length > 0) {
        const testDomain = this.testData.testDomains[0];
        const foundDomain = await domainService.getDomainByName(testDomain.domain);

        this.logResult(
          'Domain Lookup by Name',
          foundDomain && foundDomain.institute_name === this.testData.testInstitute.name,
          `Found domain: ${foundDomain?.domain}, Institute: ${foundDomain?.institute_name}`
        );
      }

    } catch (error) {
      this.logResult('Domain Management', false, error.message);
    }
  }

  /**
   * Create test data
   */
  async createTestData() {
    try {
      // Create test institute
      const instituteData = {
        email: `admin${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Admin',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `contact${Date.now()}@testinstitute.edu`,
        instituteAddress: '123 Test Street'
      };

      const result = await authService.registerInstituteAdmin(instituteData);
      this.testData.testInstitute = result.institute;
      this.testData.testUser = result.user;

      console.log('✅ Test data created successfully');

    } catch (error) {
      console.error('❌ Test data creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test domains and related data
      for (const domain of this.testData.testDomains) {
        await query('DELETE FROM ssl_certificates WHERE domain_id = $1', [domain.id]);
        await query('DELETE FROM domain_verification_logs WHERE domain_id = $1', [domain.id]);
        await query('DELETE FROM institute_domains WHERE id = $1', [domain.id]);
      }

      // Delete branding
      if (this.testData.testInstitute) {
        await query('DELETE FROM institute_branding WHERE institute_id = $1', [this.testData.testInstitute.id]);
      }

      // Delete user and institute
      if (this.testData.testUser) {
        await query('DELETE FROM email_verifications WHERE user_id = $1', [this.testData.testUser.id]);
        await query('DELETE FROM user_sessions WHERE user_id = $1', [this.testData.testUser.id]);
        await query('DELETE FROM users WHERE id = $1', [this.testData.testUser.id]);
      }

      if (this.testData.testInstitute) {
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.testInstitute.id]);
      }

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all domain management tests
   */
  async runAllTests() {
    console.log('🚀 Starting Domain Management Tests...\n');

    await this.testDomainValidation();
    await this.testCustomDomainAddition();
    await this.testDNSVerification();
    await this.testSSLCertificate();
    await this.testInstituteBranding();
    await this.testLandingPageGeneration();
    await this.testDomainManagement();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new DomainTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Domain management testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { DomainTester };
