# PostgreSQL Configuration - Optimized for LMS SAAS
# Generated on: 2025-07-14T05:01:07.335Z
# System: win32, 16GB RAM, 8 CPU cores
# Optimized for multi-tenant LMS workload

# -----------------------------
# CONNECTIONS AND AUTHENTICATION
# -----------------------------

max_connections = 200
listen_addresses = '*'
port = 5432

# SSL Configuration (recommended for production)
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'

# -----------------------------
# RESOURCE USAGE (except WAL)
# -----------------------------

# Memory
shared_buffers = 4GB
work_mem = 64MB
maintenance_work_mem = 1024MB
effective_cache_size = 12GB

# Kernel Resource Usage
max_worker_processes = 16
max_parallel_workers = 8
max_parallel_workers_per_gather = 4
max_parallel_maintenance_workers = 4

# -----------------------------
# WRITE AHEAD LOG
# -----------------------------

wal_buffers = 64MB
min_wal_size = 4GB
max_wal_size = 16GB
checkpoint_completion_target = 0.9

# WAL archiving (for backup and replication)
archive_mode = on
archive_command = 'copy "%p" "C:\\postgres_archive\\%f"'  # Windows
# archive_command = 'cp %p /var/lib/postgresql/archive/%f'    # Linux

# -----------------------------
# QUERY TUNING
# -----------------------------

# Planner Cost Constants
random_page_cost = 4
effective_io_concurrency = 1

# Planner Method Configuration
enable_partitionwise_join = on
enable_partitionwise_aggregate = on

# Other Planner Options
default_statistics_target = 100

# -----------------------------
# REPORTING AND LOGGING
# -----------------------------

# What to log
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_statement = 'ddl'

# Where to log
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB

# What to log (detailed)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_timezone = 'UTC'

# -----------------------------
# RUNTIME STATISTICS
# -----------------------------

shared_preload_libraries = 'pg_stat_statements'
track_activity_query_size = 2048
track_counts = on
track_functions = all
track_io_timing = on

# pg_stat_statements configuration
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# -----------------------------
# AUTOVACUUM PARAMETERS
# -----------------------------

autovacuum = on
autovacuum_max_workers = 4
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05

# -----------------------------
# CLIENT CONNECTION DEFAULTS
# -----------------------------

timezone = 'UTC'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'
default_text_search_config = 'pg_catalog.english'

# -----------------------------
# LOCK MANAGEMENT
# -----------------------------

deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# -----------------------------
# VERSION/PLATFORM COMPATIBILITY
# -----------------------------

# (none currently required)

# -----------------------------
# CUSTOMIZED OPTIONS
# -----------------------------

# Multi-tenant specific settings
row_security = on  # Enable row-level security
shared_buffers_ring_size = 256kB  # Optimize for many small queries

# Performance monitoring
log_parser_stats = off
log_planner_stats = off
log_executor_stats = off
log_statement_stats = off

# Security settings
password_encryption = scram-sha-256
ssl_prefer_server_ciphers = on
ssl_min_protocol_version = 'TLSv1.2'
