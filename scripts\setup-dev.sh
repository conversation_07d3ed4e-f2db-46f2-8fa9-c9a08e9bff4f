#!/bin/bash

# LMS SAAS Platform - Development Setup Script
# This script sets up the development environment for the LMS SAAS platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check PostgreSQL
    if ! command_exists psql; then
        print_warning "PostgreSQL client (psql) is not installed. You may need to install PostgreSQL."
    fi
    
    # Check Git
    if ! command_exists git; then
        print_error "Git is not installed. Please install Git."
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..
    
    print_success "Dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        print_status "Creating frontend environment file..."
        cp frontend/.env.example frontend/.env.local
        print_success "Frontend .env.local created from template"
    else
        print_warning "Frontend .env.local already exists, skipping..."
    fi
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend environment file..."
        cp backend/.env.example backend/.env
        print_success "Backend .env created from template"
    else
        print_warning "Backend .env already exists, skipping..."
    fi
    
    print_success "Environment files setup completed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if PostgreSQL is running
    if command_exists pg_isready; then
        if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            print_success "PostgreSQL is running"
            
            # Try to create database
            print_status "Creating database if it doesn't exist..."
            createdb -h localhost -p 5432 -U postgres lte_lms 2>/dev/null || print_warning "Database might already exist"
            
            # Run migrations
            print_status "Running database migrations..."
            cd backend && npm run db:migrate && cd ..
            
            # Seed database
            print_status "Seeding database with initial data..."
            cd backend && npm run db:seed && cd ..
            
            print_success "Database setup completed"
        else
            print_warning "PostgreSQL is not running. Please start PostgreSQL and run this script again."
            print_warning "Or use Docker: docker-compose -f docker-compose.dev.yml up postgres-dev"
        fi
    else
        print_warning "PostgreSQL client not found. Skipping database setup."
        print_warning "You can use Docker: docker-compose -f docker-compose.dev.yml up postgres-dev"
    fi
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    
    if [ -d ".git" ]; then
        npx husky install
        print_success "Git hooks setup completed"
    else
        print_warning "Not a Git repository. Skipping Git hooks setup."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Backend directories
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p backend/temp
    
    # Frontend directories
    mkdir -p frontend/public/uploads
    
    print_success "Directories created"
}

# Run initial checks
run_initial_checks() {
    print_status "Running initial checks..."
    
    # Type checking
    print_status "Running TypeScript checks..."
    npm run type-check
    
    # Linting
    print_status "Running linting checks..."
    npm run lint
    
    print_success "Initial checks completed"
}

# Main setup function
main() {
    echo "=================================================="
    echo "🚀 LMS SAAS Platform - Development Setup"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    echo ""
    
    install_dependencies
    echo ""
    
    setup_environment
    echo ""
    
    create_directories
    echo ""
    
    setup_git_hooks
    echo ""
    
    setup_database
    echo ""
    
    run_initial_checks
    echo ""
    
    echo "=================================================="
    echo "✅ Development setup completed successfully!"
    echo "=================================================="
    echo ""
    echo "Next steps:"
    echo "1. Review and update environment files:"
    echo "   - frontend/.env.local"
    echo "   - backend/.env"
    echo ""
    echo "2. Start the development servers:"
    echo "   npm run dev"
    echo ""
    echo "3. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:5010"
    echo ""
    echo "4. For database management (optional):"
    echo "   docker-compose -f docker-compose.dev.yml up pgadmin"
    echo "   Access pgAdmin at: http://localhost:5050"
    echo ""
    echo "Happy coding! 🎉"
}

# Run main function
main "$@"
