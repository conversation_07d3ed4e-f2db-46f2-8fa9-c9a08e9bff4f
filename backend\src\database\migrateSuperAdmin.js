const { Pool } = require('pg');

/**
 * Super Admin Platform Management Tables Migration
 * Creates tables for subscription history, institute status tracking, and platform analytics
 */

async function migrateSuperAdminTables() {
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'lte_lms',
    password: process.env.DB_PASSWORD || '1234',
    port: process.env.DB_PORT || 5432,
  });

  try {
    console.log('🚀 Starting super admin tables migration...');

    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connected successfully');
    console.log(`📊 Connected to database: ${client.database}`);
    console.log(`🏠 Host: ${client.host}:${client.port}`);
    client.release();

    // Super admin tables SQL
    const superAdminTablesSQL = `
-- =============================================
-- SUPER ADMIN PLATFORM MANAGEMENT TABLES
-- =============================================

-- Subscription history table
CREATE TABLE IF NOT EXISTS subscription_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    changed_by UUID NOT NULL REFERENCES users(id),
    old_plan VARCHAR(50),
    new_plan VARCHAR(50) NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Institute status history table
CREATE TABLE IF NOT EXISTS institute_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    changed_by UUID NOT NULL REFERENCES users(id),
    old_status BOOLEAN,
    new_status BOOLEAN NOT NULL,
    reason TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Platform analytics cache table
CREATE TABLE IF NOT EXISTS platform_analytics_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(100) NOT NULL, -- 'daily_stats', 'growth_metrics', 'security_summary'
    metric_date DATE NOT NULL,
    metric_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(metric_type, metric_date)
);

-- Super admin activity log
CREATE TABLE IF NOT EXISTS super_admin_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID NOT NULL REFERENCES users(id),
    action_type VARCHAR(100) NOT NULL, -- 'institute_created', 'subscription_changed', 'user_suspended', etc.
    target_type VARCHAR(50) NOT NULL, -- 'institute', 'user', 'subscription'
    target_id UUID NOT NULL,
    action_details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Platform notifications table
CREATE TABLE IF NOT EXISTS platform_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_type VARCHAR(100) NOT NULL, -- 'security_alert', 'subscription_expiry', 'system_maintenance'
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'info', -- 'info', 'warning', 'error', 'critical'
    target_audience VARCHAR(50) DEFAULT 'super_admin', -- 'super_admin', 'institute_admin', 'all'
    target_institute_id UUID REFERENCES institutes(id),
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System maintenance windows table
CREATE TABLE IF NOT EXISTS maintenance_windows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    maintenance_type VARCHAR(50) NOT NULL, -- 'scheduled', 'emergency', 'security'
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    affected_services JSONB, -- Array of affected service names
    status VARCHAR(50) DEFAULT 'scheduled', -- 'scheduled', 'in_progress', 'completed', 'cancelled'
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Institute usage statistics table
CREATE TABLE IF NOT EXISTS institute_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    stat_date DATE NOT NULL,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    total_logins INTEGER DEFAULT 0,
    storage_used_mb INTEGER DEFAULT 0,
    api_requests INTEGER DEFAULT 0,
    feature_usage JSONB, -- JSON object with feature usage counts
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(institute_id, stat_date)
);

-- Platform feature flags table
CREATE TABLE IF NOT EXISTS platform_feature_flags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    feature_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_enabled BOOLEAN DEFAULT FALSE,
    rollout_percentage INTEGER DEFAULT 0, -- 0-100 for gradual rollout
    target_subscription_plans JSONB, -- Array of subscription plans that have access
    target_institutes JSONB, -- Array of specific institute IDs
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Super admin indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscription_history_institute_id ON subscription_history(institute_id);
CREATE INDEX IF NOT EXISTS idx_subscription_history_changed_by ON subscription_history(changed_by);
CREATE INDEX IF NOT EXISTS idx_subscription_history_created_at ON subscription_history(created_at);

CREATE INDEX IF NOT EXISTS idx_institute_status_history_institute_id ON institute_status_history(institute_id);
CREATE INDEX IF NOT EXISTS idx_institute_status_history_changed_by ON institute_status_history(changed_by);
CREATE INDEX IF NOT EXISTS idx_institute_status_history_created_at ON institute_status_history(created_at);

CREATE INDEX IF NOT EXISTS idx_platform_analytics_cache_metric_type ON platform_analytics_cache(metric_type);
CREATE INDEX IF NOT EXISTS idx_platform_analytics_cache_metric_date ON platform_analytics_cache(metric_date);

CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_admin_id ON super_admin_activity_log(admin_id);
CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_action_type ON super_admin_activity_log(action_type);
CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_target_type ON super_admin_activity_log(target_type);
CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_created_at ON super_admin_activity_log(created_at);

CREATE INDEX IF NOT EXISTS idx_platform_notifications_target_audience ON platform_notifications(target_audience);
CREATE INDEX IF NOT EXISTS idx_platform_notifications_target_institute_id ON platform_notifications(target_institute_id);
CREATE INDEX IF NOT EXISTS idx_platform_notifications_is_read ON platform_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_platform_notifications_created_at ON platform_notifications(created_at);

CREATE INDEX IF NOT EXISTS idx_maintenance_windows_start_time ON maintenance_windows(start_time);
CREATE INDEX IF NOT EXISTS idx_maintenance_windows_end_time ON maintenance_windows(end_time);
CREATE INDEX IF NOT EXISTS idx_maintenance_windows_status ON maintenance_windows(status);

CREATE INDEX IF NOT EXISTS idx_institute_usage_stats_institute_id ON institute_usage_stats(institute_id);
CREATE INDEX IF NOT EXISTS idx_institute_usage_stats_stat_date ON institute_usage_stats(stat_date);

CREATE INDEX IF NOT EXISTS idx_platform_feature_flags_feature_name ON platform_feature_flags(feature_name);
CREATE INDEX IF NOT EXISTS idx_platform_feature_flags_is_enabled ON platform_feature_flags(is_enabled);

-- Add deleted_at column to institutes table if it doesn't exist
ALTER TABLE institutes ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP;

-- Insert default platform feature flags
INSERT INTO platform_feature_flags (feature_name, description, is_enabled, rollout_percentage, target_subscription_plans, created_by) 
SELECT 
    'advanced_analytics', 
    'Advanced analytics and reporting features', 
    TRUE, 
    100, 
    '["premium", "enterprise"]'::jsonb,
    (SELECT id FROM users WHERE role = 'super_admin' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM platform_feature_flags WHERE feature_name = 'advanced_analytics');

INSERT INTO platform_feature_flags (feature_name, description, is_enabled, rollout_percentage, target_subscription_plans, created_by) 
SELECT 
    'custom_branding', 
    'Custom branding and white-label features', 
    TRUE, 
    100, 
    '["enterprise"]'::jsonb,
    (SELECT id FROM users WHERE role = 'super_admin' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM platform_feature_flags WHERE feature_name = 'custom_branding');

INSERT INTO platform_feature_flags (feature_name, description, is_enabled, rollout_percentage, target_subscription_plans, created_by) 
SELECT 
    'api_access', 
    'API access for third-party integrations', 
    TRUE, 
    100, 
    '["basic", "premium", "enterprise"]'::jsonb,
    (SELECT id FROM users WHERE role = 'super_admin' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM platform_feature_flags WHERE feature_name = 'api_access');

-- Create a view for institute overview
CREATE OR REPLACE VIEW institute_overview AS
SELECT 
    i.*,
    (SELECT COUNT(*) FROM users WHERE institute_id = i.id) as total_users,
    (SELECT COUNT(*) FROM users WHERE institute_id = i.id AND role = 'student') as student_count,
    (SELECT COUNT(*) FROM users WHERE institute_id = i.id AND role = 'teacher') as teacher_count,
    (SELECT COUNT(*) FROM users WHERE institute_id = i.id AND role = 'institute_admin') as admin_count,
    (SELECT COUNT(*) FROM institute_domains WHERE institute_id = i.id AND is_active = TRUE) as domain_count,
    (SELECT COUNT(*) FROM user_sessions us JOIN users u ON us.user_id = u.id WHERE u.institute_id = i.id AND us.is_active = TRUE) as active_sessions
FROM institutes i;
`;

    console.log('📄 Executing super admin tables SQL...');

    // Execute the SQL
    await pool.query(superAdminTablesSQL);

    console.log('✅ Super admin tables migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - subscription_history');
    console.log('   - institute_status_history');
    console.log('   - platform_analytics_cache');
    console.log('   - super_admin_activity_log');
    console.log('   - platform_notifications');
    console.log('   - maintenance_windows');
    console.log('   - institute_usage_stats');
    console.log('   - platform_feature_flags');
    console.log('🔍 Created indexes for performance optimization');
    console.log('📋 Created institute_overview view');
    console.log('⚙️ Inserted default feature flags');

  } catch (error) {
    console.error('❌ Migration error:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('⚠️  Tables already exist, skipping creation');
    } else {
      throw error;
    }
  } finally {
    console.log('🔒 Database pool closed');
    await pool.end();
  }
}

// Run migration if script is executed directly
if (require.main === module) {
  migrateSuperAdminTables()
    .then(() => {
      console.log('🏁 Super admin tables migration completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { migrateSuperAdminTables };
