const { query, transaction } = require('../database/connection');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs').promises;

/**
 * Institute Branding and Landing Page Service
 * Manages institute branding, customization, and landing page generation
 */

class BrandingService {
  /**
   * Get or create institute branding
   */
  async getInstituteBranding(instituteId) {
    try {
      let result = await query(
        'SELECT * FROM institute_branding WHERE institute_id = $1 AND is_active = TRUE',
        [instituteId]
      );

      if (result.rows.length === 0) {
        // Create default branding
        result = await query(`
          INSERT INTO institute_branding (
            institute_id, primary_color, secondary_color, accent_color,
            font_family, landing_page_template
          )
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `, [
          instituteId,
          '#007bff', // Primary blue
          '#6c757d', // Secondary gray
          '#28a745', // Success green
          'Inter',
          'default'
        ]);
      }

      return result.rows[0];
    } catch (error) {
      console.error('Get institute branding error:', error.message);
      throw error;
    }
  }

  /**
   * Update institute branding
   */
  async updateInstituteBranding(instituteId, brandingData) {
    try {
      const {
        logoUrl,
        faviconUrl,
        primaryColor,
        secondaryColor,
        accentColor,
        fontFamily,
        customCss,
        landingPageTemplate,
        landingPageContent,
        socialLinks,
        contactInfo,
        seoSettings
      } = brandingData;

      const result = await query(`
        UPDATE institute_branding 
        SET 
          logo_url = COALESCE($2, logo_url),
          favicon_url = COALESCE($3, favicon_url),
          primary_color = COALESCE($4, primary_color),
          secondary_color = COALESCE($5, secondary_color),
          accent_color = COALESCE($6, accent_color),
          font_family = COALESCE($7, font_family),
          custom_css = COALESCE($8, custom_css),
          landing_page_template = COALESCE($9, landing_page_template),
          landing_page_content = COALESCE($10, landing_page_content),
          social_links = COALESCE($11, social_links),
          contact_info = COALESCE($12, contact_info),
          seo_settings = COALESCE($13, seo_settings),
          updated_at = CURRENT_TIMESTAMP
        WHERE institute_id = $1 AND is_active = TRUE
        RETURNING *
      `, [
        instituteId,
        logoUrl,
        faviconUrl,
        primaryColor,
        secondaryColor,
        accentColor,
        fontFamily,
        customCss,
        landingPageTemplate,
        landingPageContent ? JSON.stringify(landingPageContent) : null,
        socialLinks ? JSON.stringify(socialLinks) : null,
        contactInfo ? JSON.stringify(contactInfo) : null,
        seoSettings ? JSON.stringify(seoSettings) : null
      ]);

      if (result.rows.length === 0) {
        throw new Error('Institute branding not found');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update institute branding error:', error.message);
      throw error;
    }
  }

  /**
   * Generate landing page for institute domain
   */
  async generateLandingPage(instituteId, domainName) {
    try {
      // Get institute information
      const instituteResult = await query(
        'SELECT * FROM institutes WHERE id = $1',
        [instituteId]
      );

      if (instituteResult.rows.length === 0) {
        throw new Error('Institute not found');
      }

      const institute = instituteResult.rows[0];

      // Get branding information
      const branding = await this.getInstituteBranding(instituteId);

      // Get domain information
      const domainResult = await query(
        'SELECT * FROM institute_domains WHERE institute_id = $1 AND domain = $2',
        [instituteId, domainName]
      );

      const domain = domainResult.rows[0];

      // Generate landing page content
      const landingPageData = {
        institute,
        branding,
        domain,
        generatedAt: new Date().toISOString()
      };

      const htmlContent = await this.renderLandingPageTemplate(landingPageData);

      return {
        html: htmlContent,
        metadata: {
          title: `${institute.name} - Learning Management System`,
          description: `Welcome to ${institute.name}'s online learning platform`,
          domain: domainName,
          generatedAt: landingPageData.generatedAt
        }
      };

    } catch (error) {
      console.error('Generate landing page error:', error.message);
      throw error;
    }
  }

  /**
   * Render landing page template
   */
  async renderLandingPageTemplate(data) {
    const { institute, branding, domain } = data;

    // Parse JSON fields safely
    const landingPageContent = this.parseJSON(branding.landing_page_content, {});
    const socialLinks = this.parseJSON(branding.social_links, {});
    const contactInfo = this.parseJSON(branding.contact_info, {});
    const seoSettings = this.parseJSON(branding.seo_settings, {});

    // Generate CSS variables from branding
    const cssVariables = `
      :root {
        --primary-color: ${branding.primary_color};
        --secondary-color: ${branding.secondary_color};
        --accent-color: ${branding.accent_color};
        --font-family: ${branding.font_family}, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
    `;

    // Default landing page template
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${seoSettings.title || `${institute.name} - Learning Management System`}</title>
    <meta name="description" content="${seoSettings.description || `Welcome to ${institute.name}'s online learning platform`}">
    <meta name="keywords" content="${seoSettings.keywords || 'education, learning, online courses, LMS'}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://${domain?.domain || 'example.com'}">
    <meta property="og:title" content="${institute.name}">
    <meta property="og:description" content="${seoSettings.description || `Welcome to ${institute.name}'s online learning platform`}">
    <meta property="og:image" content="${branding.logo_url || '/default-logo.png'}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://${domain?.domain || 'example.com'}">
    <meta property="twitter:title" content="${institute.name}">
    <meta property="twitter:description" content="${seoSettings.description || `Welcome to ${institute.name}'s online learning platform`}">
    <meta property="twitter:image" content="${branding.logo_url || '/default-logo.png'}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="${branding.favicon_url || '/favicon.ico'}">
    
    <style>
        ${cssVariables}
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-family);
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .logo {
            max-height: 80px;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .cta-button {
            display: inline-block;
            background: white;
            color: var(--primary-color);
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .features {
            padding: 4rem 0;
            background: white;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .contact-section {
            background: var(--secondary-color);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .social-links {
            margin-top: 2rem;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            text-decoration: none;
        }
        
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
        
        ${branding.custom_css || ''}
    </style>
</head>
<body>
    <header>
        <div class="container">
            ${branding.logo_url ? `<img src="${branding.logo_url}" alt="${institute.name} Logo" class="logo">` : ''}
            <h1>${institute.name}</h1>
            <p class="subtitle">${landingPageContent.subtitle || 'Excellence in Education'}</p>
            <a href="/login" class="cta-button">Access Learning Portal</a>
        </div>
    </header>

    <section class="features">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 1rem; color: var(--primary-color);">
                ${landingPageContent.featuresTitle || 'Why Choose Us?'}
            </h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📚</div>
                    <h3>Quality Education</h3>
                    <p>Access to high-quality courses and learning materials designed by expert educators.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💻</div>
                    <h3>Online Learning</h3>
                    <p>Learn at your own pace with our flexible online learning platform available 24/7.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎓</div>
                    <h3>Expert Faculty</h3>
                    <p>Learn from experienced professionals and industry experts in their respective fields.</p>
                </div>
            </div>
        </div>
    </section>

    ${contactInfo.email || contactInfo.phone || contactInfo.address ? `
    <section class="contact-section">
        <div class="container">
            <h2>Get in Touch</h2>
            ${contactInfo.email ? `<p>📧 ${contactInfo.email}</p>` : ''}
            ${contactInfo.phone ? `<p>📞 ${contactInfo.phone}</p>` : ''}
            ${contactInfo.address ? `<p>📍 ${contactInfo.address}</p>` : ''}
            
            ${Object.keys(socialLinks).length > 0 ? `
            <div class="social-links">
                ${socialLinks.facebook ? `<a href="${socialLinks.facebook}" target="_blank">📘</a>` : ''}
                ${socialLinks.twitter ? `<a href="${socialLinks.twitter}" target="_blank">🐦</a>` : ''}
                ${socialLinks.linkedin ? `<a href="${socialLinks.linkedin}" target="_blank">💼</a>` : ''}
                ${socialLinks.instagram ? `<a href="${socialLinks.instagram}" target="_blank">📷</a>` : ''}
            </div>
            ` : ''}
        </div>
    </section>
    ` : ''}

    <footer>
        <div class="container">
            <p>&copy; ${new Date().getFullYear()} ${institute.name}. All rights reserved.</p>
            <p>Powered by LMS SAAS Platform</p>
        </div>
    </footer>

    <script>
        // Simple analytics tracking
        console.log('Landing page loaded for ${institute.name}');
        
        // Track CTA button clicks
        document.querySelector('.cta-button').addEventListener('click', function() {
            console.log('CTA button clicked');
        });
    </script>
</body>
</html>`;

    return htmlTemplate;
  }

  /**
   * Get landing page templates
   */
  getAvailableTemplates() {
    return [
      {
        id: 'default',
        name: 'Default Template',
        description: 'Clean and professional design suitable for most institutions',
        preview: '/templates/default-preview.jpg'
      },
      {
        id: 'modern',
        name: 'Modern Template',
        description: 'Contemporary design with bold typography and animations',
        preview: '/templates/modern-preview.jpg'
      },
      {
        id: 'academic',
        name: 'Academic Template',
        description: 'Traditional academic styling with formal layout',
        preview: '/templates/academic-preview.jpg'
      },
      {
        id: 'minimal',
        name: 'Minimal Template',
        description: 'Simple and clean design focusing on content',
        preview: '/templates/minimal-preview.jpg'
      }
    ];
  }

  /**
   * Safely parse JSON with fallback
   */
  parseJSON(jsonString, fallback = {}) {
    try {
      return jsonString ? JSON.parse(jsonString) : fallback;
    } catch (error) {
      console.warn('Failed to parse JSON:', error.message);
      return fallback;
    }
  }

  /**
   * Validate color format (hex)
   */
  isValidHexColor(color) {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  }

  /**
   * Generate CSS from branding settings
   */
  generateBrandingCSS(branding) {
    return `
      :root {
        --primary-color: ${branding.primary_color};
        --secondary-color: ${branding.secondary_color};
        --accent-color: ${branding.accent_color};
        --font-family: ${branding.font_family};
      }
      
      .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
      
      .btn-secondary {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
      }
      
      .text-primary {
        color: var(--primary-color) !important;
      }
      
      .bg-primary {
        background-color: var(--primary-color) !important;
      }
      
      body {
        font-family: var(--font-family), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      ${branding.custom_css || ''}
    `;
  }
}

module.exports = new BrandingService();
