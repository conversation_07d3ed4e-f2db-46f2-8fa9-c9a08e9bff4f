'use client'

import React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { But<PERSON> } from '@/components/ui/button'
import { FormField, SelectField, CheckboxField } from '@/components/forms/FormField'
import { useAuthActions, useAuth } from '@/store/authStore'
import { ArrowLeft, UserPlus } from 'lucide-react'

const registerSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .required('First name is required'),
  lastName: Yup.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .required('Last name is required'),
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  role: Yup.string()
    .oneOf(['institute_admin', 'teacher', 'student'], 'Please select a valid role')
    .required('Role is required'),
  instituteCode: Yup.string().when('role', {
    is: (role: string) => role === 'teacher' || role === 'student',
    then: (schema) => schema.required('Institute code is required for teachers and students'),
    otherwise: (schema) => schema.notRequired(),
  }),
  agreeToTerms: Yup.boolean()
    .oneOf([true], 'You must agree to the terms and conditions')
    .required('You must agree to the terms and conditions'),
})

interface RegisterFormValues {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  role: string
  instituteCode: string
  agreeToTerms: boolean
}

const roleOptions = [
  { value: 'institute_admin', label: 'Institute Administrator' },
  { value: 'teacher', label: 'Teacher' },
  { value: 'student', label: 'Student' },
]

export default function RegisterPage() {
  const router = useRouter()
  const { register } = useAuthActions()
  const { isLoading } = useAuth()

  const handleSubmit = async (values: RegisterFormValues, { setSubmitting }: any) => {
    try {
      await register({
        email: values.email,
        password: values.password,
        firstName: values.firstName,
        lastName: values.lastName,
        role: values.role as any,
        instituteId: values.instituteCode,
      })

      // Redirect to dashboard after successful registration
      router.push('/dashboard')

    } catch (error) {
      console.error('Registration error:', error)
      // Error toast is handled in the store
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
          <Link 
            href="/" 
            className="flex items-center space-x-1 hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to home</span>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900">Create your account</h1>
        <p className="text-gray-600">
          Join thousands of educators and students using our platform
        </p>
      </div>

      {/* Registration Form */}
      <Formik
        initialValues={{
          firstName: '',
          lastName: '',
          email: '',
          password: '',
          confirmPassword: '',
          role: '',
          instituteCode: '',
          agreeToTerms: false,
        }}
        validationSchema={registerSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, isValid, values }) => (
          <Form className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                name="firstName"
                label="First name"
                placeholder="Enter your first name"
                autoComplete="given-name"
                required
              />

              <FormField
                name="lastName"
                label="Last name"
                placeholder="Enter your last name"
                autoComplete="family-name"
                required
              />
            </div>

            <FormField
              name="email"
              label="Email address"
              type="email"
              placeholder="Enter your email"
              autoComplete="email"
              required
            />

            <SelectField
              name="role"
              label="I am a"
              placeholder="Select your role"
              options={roleOptions}
              required
            />

            {(values.role === 'teacher' || values.role === 'student') && (
              <FormField
                name="instituteCode"
                label="Institute code"
                placeholder="Enter your institute code"
                description="Contact your institute administrator for the institute code"
                required
              />
            )}

            <FormField
              name="password"
              label="Password"
              type="password"
              placeholder="Create a strong password"
              autoComplete="new-password"
              required
            />

            <FormField
              name="confirmPassword"
              label="Confirm password"
              type="password"
              placeholder="Confirm your password"
              autoComplete="new-password"
              required
            />

            <CheckboxField
              name="agreeToTerms"
              label={
                <>
                  I agree to the{' '}
                  <Link href="/terms" className="text-blue-600 hover:underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-blue-600 hover:underline">
                    Privacy Policy
                  </Link>
                </>
              }
              required
            />

            <Button
              type="submit"
              className="w-full"
              loading={isSubmitting || isLoading}
              disabled={!isValid}
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Create account
            </Button>
          </Form>
        )}
      </Formik>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      {/* Social Registration */}
      <div className="grid grid-cols-2 gap-4">
        <Button variant="outline" disabled>
          <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Google
        </Button>
        
        <Button variant="outline" disabled>
          <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
          </svg>
          Twitter
        </Button>
      </div>

      {/* Sign in link */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Already have an account?{' '}
          <Link 
            href="/auth/login"
            className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
          >
            Sign in
          </Link>
        </p>
      </div>

      {/* Role descriptions */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Role Descriptions</h3>
        <div className="space-y-2 text-xs text-gray-600">
          <p><strong>Institute Administrator:</strong> Manage your institution, teachers, and students</p>
          <p><strong>Teacher:</strong> Create courses, manage students, and track progress</p>
          <p><strong>Student:</strong> Access courses, submit assignments, and track your learning</p>
        </div>
      </div>
    </div>
  )
}
