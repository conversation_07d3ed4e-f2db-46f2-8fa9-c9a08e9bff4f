'use client'

import React, { useState, useRef } from 'react'
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  FileImage, 
  AlertCircle,
  Check,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface LogoUploadProps {
  value: string
  onChange: (url: string) => void
  type: 'logo' | 'favicon'
}

export default function LogoUpload({ value, onChange, type }: LogoUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const maxSize = type === 'logo' ? 2 * 1024 * 1024 : 512 * 1024 // 2MB for logo, 512KB for favicon
  const acceptedTypes = type === 'logo' 
    ? ['image/png', 'image/jpeg', 'image/svg+xml', 'image/webp']
    : ['image/png', 'image/x-icon', 'image/vnd.microsoft.icon']

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `Invalid file type. Please upload ${type === 'logo' ? 'PNG, JPEG, SVG, or WebP' : 'PNG or ICO'} files only.`
    }

    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024)
      return `File size too large. Maximum size is ${maxSizeMB}MB.`
    }

    return null
  }

  const handleFileUpload = async (file: File) => {
    setUploadError(null)
    
    const validationError = validateFile(file)
    if (validationError) {
      setUploadError(validationError)
      return
    }

    setIsUploading(true)

    try {
      // Create a preview URL for immediate display
      const previewUrl = URL.createObjectURL(file)
      
      // TODO: Upload to actual file storage service
      // For now, we'll simulate an upload and use the preview URL
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // In a real implementation, you would upload to your file storage service
      // and get back the permanent URL
      const uploadedUrl = previewUrl // This would be the actual uploaded URL
      
      onChange(uploadedUrl)
    } catch (error) {
      console.error('Upload error:', error)
      setUploadError('Failed to upload file. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemove = () => {
    onChange('')
    setUploadError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const getRecommendedSize = () => {
    return type === 'logo' ? '300x100px' : '32x32px or 16x16px'
  }

  const getAcceptedFormats = () => {
    return type === 'logo' ? 'PNG, JPEG, SVG, WebP' : 'PNG, ICO'
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`
          relative border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer
          ${isDragging 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${value ? 'bg-gray-50' : 'bg-white'}
        `}
        onClick={!value ? openFileDialog : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        {isUploading ? (
          <div className="text-center">
            <Loader2 className="mx-auto h-12 w-12 text-blue-500 animate-spin" />
            <p className="mt-2 text-sm text-gray-600">Uploading {type}...</p>
          </div>
        ) : value ? (
          <div className="text-center">
            <div className="relative inline-block">
              <img
                src={value}
                alt={`${type} preview`}
                className={`
                  mx-auto object-contain border border-gray-200 rounded
                  ${type === 'logo' ? 'max-h-20 max-w-40' : 'w-8 h-8'}
                `}
              />
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRemove()
                }}
                className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-600">
              {type === 'logo' ? 'Logo' : 'Favicon'} uploaded successfully
            </p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                openFileDialog()
              }}
              className="mt-2"
            >
              <Upload className="w-4 h-4 mr-2" />
              Replace
            </Button>
          </div>
        ) : (
          <div className="text-center">
            <div className="mx-auto h-12 w-12 text-gray-400">
              {type === 'logo' ? <ImageIcon className="w-full h-full" /> : <FileImage className="w-full h-full" />}
            </div>
            <p className="mt-2 text-sm font-medium text-gray-900">
              Upload {type === 'logo' ? 'Logo' : 'Favicon'}
            </p>
            <p className="text-xs text-gray-500">
              Drag and drop or click to browse
            </p>
          </div>
        )}
      </div>

      {/* Upload Error */}
      {uploadError && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700">{uploadError}</p>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="text-xs text-gray-500 space-y-1">
        <p><strong>Recommended size:</strong> {getRecommendedSize()}</p>
        <p><strong>Accepted formats:</strong> {getAcceptedFormats()}</p>
        <p><strong>Maximum file size:</strong> {maxSize / (1024 * 1024)}MB</p>
        {type === 'logo' && (
          <p><strong>Best practices:</strong> Use transparent background, high contrast, simple design</p>
        )}
      </div>

      {/* Upload Status */}
      {value && !isUploading && (
        <div className="flex items-center space-x-2 p-2 bg-green-50 border border-green-200 rounded-md">
          <Check className="w-4 h-4 text-green-500" />
          <p className="text-sm text-green-700">
            {type === 'logo' ? 'Logo' : 'Favicon'} ready to use
          </p>
        </div>
      )}
    </div>
  )
}
