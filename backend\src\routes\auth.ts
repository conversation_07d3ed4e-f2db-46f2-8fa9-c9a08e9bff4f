import { Router, Request, Response } from 'express'
import bcrypt from 'bcryptjs'
import { body, validationResult } from 'express-validator'
import { 
  UserRole, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  RefreshTokenRequest,
  PasswordResetRequest,
  PasswordResetConfirmRequest,
  User 
} from '../types/auth'
import { 
  generateAccessToken, 
  generateRefreshToken, 
  verifyRefreshToken,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  generateEmailVerificationToken,
  verifyEmailVerificationToken
} from '../utils/jwt'
import { authenticate } from '../middleware/auth'

const router = Router()

// Validation rules
const loginValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
]

const registerValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'),
  body('role').optional().isIn(Object.values(UserRole)).withMessage('Invalid role'),
]

const passwordResetValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
]

const passwordResetConfirmValidation = [
  body('token').notEmpty().withMessage('Reset token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
]

/**
 * POST /api/auth/login
 * Authenticate user and return JWT tokens
 */
router.post('/login', loginValidation, async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array(),
      })
      return
    }

    const { email, password }: LoginRequest = req.body

    // TODO: Replace with actual database query
    // Mock user for demonstration
    const mockUser: User = {
      id: 'user-123',
      email: email,
      firstName: 'John',
      lastName: 'Doe',
      role: UserRole.STUDENT,
      instituteId: 'inst-123',
      isActive: true,
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // TODO: Verify password with bcrypt
    const isValidPassword = await bcrypt.compare(password, '$2a$12$hashedpassword') // Mock
    
    if (!isValidPassword) {
      res.status(401).json({
        success: false,
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS',
      })
      return
    }

    if (!mockUser.isActive) {
      res.status(401).json({
        success: false,
        error: 'Account is deactivated',
        code: 'ACCOUNT_DEACTIVATED',
      })
      return
    }

    if (!mockUser.emailVerified) {
      res.status(401).json({
        success: false,
        error: 'Email not verified',
        code: 'EMAIL_NOT_VERIFIED',
      })
      return
    }

    // Generate tokens
    const accessToken = generateAccessToken(mockUser)
    const refreshToken = generateRefreshToken(mockUser)

    // TODO: Store refresh token in database

    const response: AuthResponse = {
      success: true,
      user: {
        id: mockUser.id,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        role: mockUser.role,
        instituteId: mockUser.instituteId,
        isActive: mockUser.isActive,
        emailVerified: mockUser.emailVerified,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      },
      token: accessToken,
      refreshToken: refreshToken,
    }

    res.json(response)
  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      success: false,
      error: 'Login failed',
      code: 'LOGIN_ERROR',
    })
  }
})

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', registerValidation, async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array(),
      })
      return
    }

    const { email, password, firstName, lastName, role = UserRole.STUDENT, instituteId }: RegisterRequest = req.body

    // TODO: Check if user already exists in database

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12')
    const _hashedPassword = await bcrypt.hash(password, saltRounds)
    // TODO: Store hashedPassword in database

    // TODO: Create user in database
    const newUser: User = {
      id: `user-${Date.now()}`, // TODO: Use proper UUID
      email,
      firstName,
      lastName,
      role,
      instituteId,
      isActive: true,
      emailVerified: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Generate email verification token
    const _verificationToken = generateEmailVerificationToken(newUser.id, newUser.email)
    // TODO: Send verification email with verificationToken

    // TODO: Send verification email

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please check your email for verification.',
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        role: newUser.role,
        instituteId: newUser.instituteId,
        isActive: newUser.isActive,
        emailVerified: newUser.emailVerified,
        createdAt: newUser.createdAt,
        updatedAt: newUser.updatedAt,
      },
    })
  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({
      success: false,
      error: 'Registration failed',
      code: 'REGISTRATION_ERROR',
    })
  }
})

/**
 * POST /api/auth/refresh
 * Refresh access token using refresh token
 */
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken }: RefreshTokenRequest = req.body

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        error: 'Refresh token is required',
        code: 'MISSING_REFRESH_TOKEN',
      })
      return
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken)

    // TODO: Check if refresh token exists in database and is not revoked

    // TODO: Fetch user from database
    const user: User = {
      id: decoded.userId,
      email: decoded.email,
      firstName: 'John', // TODO: Fetch from DB
      lastName: 'Doe', // TODO: Fetch from DB
      role: decoded.role,
      instituteId: decoded.instituteId,
      isActive: true,
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(user)

    res.json({
      success: true,
      token: newAccessToken,
    })
  } catch (error) {
    console.error('Token refresh error:', error)
    res.status(401).json({
      success: false,
      error: 'Invalid refresh token',
      code: 'INVALID_REFRESH_TOKEN',
    })
  }
})

/**
 * POST /api/auth/logout
 * Logout user and invalidate tokens
 */
router.post('/logout', authenticate, async (_req: Request, res: Response): Promise<void> => {
  try {
    // TODO: Add refresh token to blacklist in database
    // TODO: Optionally invalidate all user sessions

    res.json({
      success: true,
      message: 'Logged out successfully',
    })
  } catch (error) {
    console.error('Logout error:', error)
    res.status(500).json({
      success: false,
      error: 'Logout failed',
      code: 'LOGOUT_ERROR',
    })
  }
})

/**
 * POST /api/auth/forgot-password
 * Request password reset
 */
router.post('/forgot-password', passwordResetValidation, async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array(),
      })
      return
    }

    const { email }: PasswordResetRequest = req.body

    // TODO: Check if user exists in database
    const userId = 'user-123' // Mock

    // Generate password reset token
    const _resetToken = generatePasswordResetToken(userId, email)
    // TODO: Send password reset email with resetToken

    // TODO: Send password reset email

    res.json({
      success: true,
      message: 'Password reset email sent',
    })
  } catch (error) {
    console.error('Password reset request error:', error)
    res.status(500).json({
      success: false,
      error: 'Password reset request failed',
      code: 'PASSWORD_RESET_ERROR',
    })
  }
})

/**
 * POST /api/auth/reset-password
 * Reset password with token
 */
router.post('/reset-password', passwordResetConfirmValidation, async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        error: 'Validation failed',
        errors: errors.array(),
      })
      return
    }

    const { token, password }: PasswordResetConfirmRequest = req.body

    // Verify reset token
    const { userId: _userId } = verifyPasswordResetToken(token)

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12')
    const _hashedPassword = await bcrypt.hash(password, saltRounds)
    // TODO: Update password in database with hashedPassword

    // TODO: Update password in database
    // TODO: Invalidate all user sessions

    res.json({
      success: true,
      message: 'Password reset successfully',
    })
  } catch (error) {
    console.error('Password reset error:', error)
    res.status(400).json({
      success: false,
      error: 'Invalid or expired reset token',
      code: 'INVALID_RESET_TOKEN',
    })
  }
})

/**
 * GET /api/auth/verify-email/:token
 * Verify email address
 */
router.get('/verify-email/:token', async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.params

    // Verify email verification token
    const { userId: _userId } = verifyEmailVerificationToken(token)

    // TODO: Update user email verification status in database

    res.json({
      success: true,
      message: 'Email verified successfully',
    })
  } catch (error) {
    console.error('Email verification error:', error)
    res.status(400).json({
      success: false,
      error: 'Invalid or expired verification token',
      code: 'INVALID_VERIFICATION_TOKEN',
    })
  }
})

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', authenticate, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated',
        code: 'UNAUTHORIZED',
      })
      return
    }

    res.json({
      success: true,
      user: req.user,
    })
  } catch (error) {
    console.error('Get profile error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile',
      code: 'PROFILE_ERROR',
    })
  }
})

export default router
