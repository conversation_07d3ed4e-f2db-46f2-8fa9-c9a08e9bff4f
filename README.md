# LMS SAAS Platform

A comprehensive, multi-tenant Learning Management System built with modern web technologies.

## 🚀 Features

- **Multi-Tenant Architecture**: Secure, isolated environments for each educational institution
- **Role-Based Access Control**: Support for super admins, institute admins, teachers, and students
- **Modern UI/UX**: Responsive design with accessibility compliance (WCAG 2.1 AA)
- **Enterprise Security**: Advanced authentication, audit logging, and threat monitoring
- **Scalable Performance**: Optimized database queries with sub-millisecond response times
- **Custom Branding**: Institute-specific theming and domain support

## 🏗️ Architecture

This is a monorepo containing:

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS, and Shadcn/ui
- **Backend**: Node.js with Express, PostgreSQL, and comprehensive API
- **Database**: PostgreSQL with optimized multi-tenant schema

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- PostgreSQL 12 or higher
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/lms-saas-platform.git
   cd lms-saas-platform
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**
   ```bash
   # Copy environment templates
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env.local
   
   # Edit the files with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create database and run migrations
   npm run db:migrate
   
   # Seed with initial data
   npm run db:seed
   ```

## 🚀 Development

### Start the development servers

```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:frontend  # Frontend on http://localhost:3000
npm run dev:backend   # Backend on http://localhost:5010
```

### Available Scripts

```bash
# Development
npm run dev              # Start both frontend and backend
npm run dev:frontend     # Start frontend only
npm run dev:backend      # Start backend only

# Building
npm run build            # Build both applications
npm run build:frontend   # Build frontend only
npm run build:backend    # Build backend only

# Production
npm run start            # Start both applications in production
npm run start:frontend   # Start frontend in production
npm run start:backend    # Start backend in production

# Code Quality
npm run lint             # Lint all code
npm run lint:fix         # Fix linting issues
npm run format           # Format code with Prettier
npm run type-check       # Run TypeScript checks

# Testing
npm run test             # Run all tests
npm run test:frontend    # Run frontend tests
npm run test:backend     # Run backend tests

# Database
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with initial data
npm run db:reset         # Reset database

# Utilities
npm run clean            # Clean all build artifacts
npm run install:all      # Install all dependencies
```

## 📁 Project Structure

```
lms-saas-platform/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── app/             # Next.js App Router pages
│   │   ├── components/      # Reusable UI components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── lib/             # Utility functions
│   │   ├── store/           # State management (Zustand)
│   │   ├── styles/          # Global styles
│   │   └── types/           # TypeScript type definitions
│   ├── public/              # Static assets
│   └── package.json
├── backend/                  # Express.js backend application
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Database models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   ├── utils/           # Utility functions
│   │   └── database/        # Database configuration
│   ├── migrations/          # Database migrations
│   ├── seeds/               # Database seeds
│   └── package.json
├── docs/                     # Documentation
├── .husky/                   # Git hooks
├── package.json              # Root package.json (monorepo)
└── README.md
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lte_lms
DB_USER=postgres
DB_PASSWORD=1234

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Server
PORT=5010
NODE_ENV=development

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

#### Frontend (.env.local)
```env
# API
NEXT_PUBLIC_API_URL=http://localhost:5010

# App
NEXT_PUBLIC_APP_NAME=LMS SAAS
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🧪 Testing

The project includes comprehensive testing setup:

- **Frontend**: Jest + React Testing Library
- **Backend**: Jest + Supertest
- **E2E**: Playwright (planned)

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 📚 API Documentation

The backend API includes:

- **Authentication**: Login, register, password reset
- **User Management**: CRUD operations for all user types
- **Institute Management**: Multi-tenant institute operations
- **Course Management**: Course creation and enrollment
- **Security**: Audit logging and monitoring

API documentation is available at `http://localhost:5010/api-docs` when running in development.

## 🔒 Security

- **Authentication**: JWT-based with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Data Protection**: Input validation and sanitization
- **Audit Logging**: Comprehensive security event tracking
- **Rate Limiting**: API rate limiting and DDoS protection
- **HTTPS**: SSL/TLS encryption in production

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Start production servers
npm run start
```

### Docker Support (Coming Soon)

```bash
# Build and run with Docker
docker-compose up --build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Use TypeScript for all new code
- Follow the existing code style (enforced by ESLint and Prettier)
- Write tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions in GitHub Discussions

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [Shadcn/ui](https://ui.shadcn.com/)
- Database powered by [PostgreSQL](https://postgresql.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)

---

**Made with ❤️ by the LMS SAAS Team**
