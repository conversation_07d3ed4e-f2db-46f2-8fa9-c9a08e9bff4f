import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Menu, X, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from './button'
import { useResponsiveNavigation } from '@/hooks/useResponsive'
import { useFocusTrap } from '@/hooks/useAccessibility'

export interface NavItem {
  label: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  children?: NavItem[]
  external?: boolean
}

interface NavigationProps {
  items: NavItem[]
  logo?: React.ReactNode
  actions?: React.ReactNode
  className?: string
}

export function Navigation({ items, logo, actions, className }: NavigationProps) {
  const pathname = usePathname()
  const { isMobile, isMenuOpen, toggleMenu, closeMenu } = useResponsiveNavigation()
  const focusTrapRef = useFocusTrap(isMenuOpen && isMobile) as React.RefObject<HTMLDivElement>

  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <nav className={cn('relative', className)} role="navigation" aria-label="Main navigation">
      {/* Desktop Navigation */}
      <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        {/* Logo */}
        <div className="flex-shrink-0">
          {logo}
        </div>

        {/* Desktop Menu */}
        <div className="hidden md:block">
          <div className="ml-10 flex items-baseline space-x-4">
            {items.map((item) => (
              <NavItemDesktop
                key={item.href}
                item={item}
                isActive={isActiveLink(item.href)}
              />
            ))}
          </div>
        </div>

        {/* Actions and Mobile Menu Button */}
        <div className="flex items-center space-x-4">
          {actions && <div className="hidden md:block">{actions}</div>}
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobile && (
        <div
          ref={focusTrapRef}
          id="mobile-menu"
          className={cn(
            'md:hidden absolute top-full left-0 right-0 z-50',
            'bg-background border-b border-border shadow-lg',
            isMenuOpen ? 'block' : 'hidden'
          )}
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            {items.map((item) => (
              <NavItemMobile
                key={item.href}
                item={item}
                isActive={isActiveLink(item.href)}
                onClose={closeMenu}
              />
            ))}
            
            {/* Mobile actions */}
            {actions && (
              <div className="pt-4 border-t border-border">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}

function NavItemDesktop({ item, isActive }: { item: NavItem; isActive: boolean }) {
  const [isOpen, setIsOpen] = React.useState(false)

  if (item.children && item.children.length > 0) {
    return (
      <div className="relative group">
        <button
          className={cn(
            'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
            'hover:bg-accent hover:text-accent-foreground',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            isActive
              ? 'bg-accent text-accent-foreground'
              : 'text-foreground'
          )}
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
          aria-expanded={isOpen}
          aria-haspopup="true"
        >
          {item.icon && <item.icon className="mr-2 h-4 w-4" />}
          {item.label}
          <ChevronDown className="ml-1 h-4 w-4" />
        </button>

        {/* Dropdown */}
        {isOpen && (
          <div
            className="absolute left-0 mt-1 w-48 bg-popover border border-border rounded-md shadow-lg z-50"
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
          >
            <div className="py-1">
              {item.children.map((child) => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    'block px-4 py-2 text-sm transition-colors',
                    'hover:bg-accent hover:text-accent-foreground',
                    'focus:outline-none focus:bg-accent focus:text-accent-foreground'
                  )}
                  target={child.external ? '_blank' : undefined}
                  rel={child.external ? 'noopener noreferrer' : undefined}
                >
                  {child.icon && <child.icon className="mr-2 h-4 w-4 inline" />}
                  {child.label}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <Link
      href={item.href}
      className={cn(
        'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
        'hover:bg-accent hover:text-accent-foreground',
        'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        isActive
          ? 'bg-accent text-accent-foreground'
          : 'text-foreground'
      )}
      target={item.external ? '_blank' : undefined}
      rel={item.external ? 'noopener noreferrer' : undefined}
    >
      {item.icon && <item.icon className="mr-2 h-4 w-4" />}
      {item.label}
    </Link>
  )
}

function NavItemMobile({ 
  item, 
  isActive, 
  onClose 
}: { 
  item: NavItem
  isActive: boolean
  onClose: () => void
}) {
  const [isOpen, setIsOpen] = React.useState(false)

  if (item.children && item.children.length > 0) {
    return (
      <div>
        <button
          className={cn(
            'flex items-center justify-between w-full px-3 py-2 rounded-md text-base font-medium transition-colors',
            'hover:bg-accent hover:text-accent-foreground',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            isActive
              ? 'bg-accent text-accent-foreground'
              : 'text-foreground'
          )}
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
          aria-controls={`mobile-submenu-${item.label}`}
        >
          <div className="flex items-center">
            {item.icon && <item.icon className="mr-3 h-5 w-5" />}
            {item.label}
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform',
              isOpen && 'rotate-180'
            )}
          />
        </button>

        {/* Submenu */}
        {isOpen && (
          <div id={`mobile-submenu-${item.label}`} className="ml-4 mt-1 space-y-1">
            {item.children.map((child) => (
              <Link
                key={child.href}
                href={child.href}
                className={cn(
                  'flex items-center px-3 py-2 rounded-md text-sm transition-colors',
                  'hover:bg-accent hover:text-accent-foreground',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  'text-muted-foreground'
                )}
                onClick={onClose}
                target={child.external ? '_blank' : undefined}
                rel={child.external ? 'noopener noreferrer' : undefined}
              >
                {child.icon && <child.icon className="mr-3 h-4 w-4" />}
                {child.label}
              </Link>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <Link
      href={item.href}
      className={cn(
        'flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors',
        'hover:bg-accent hover:text-accent-foreground',
        'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        isActive
          ? 'bg-accent text-accent-foreground'
          : 'text-foreground'
      )}
      onClick={onClose}
      target={item.external ? '_blank' : undefined}
      rel={item.external ? 'noopener noreferrer' : undefined}
    >
      {item.icon && <item.icon className="mr-3 h-5 w-5" />}
      {item.label}
    </Link>
  )
}
