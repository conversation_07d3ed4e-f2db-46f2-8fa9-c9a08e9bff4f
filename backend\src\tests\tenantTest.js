const { query } = require('../database/connection');
const { TenantService, SuperAdminService } = require('../services/tenantService');
const { ConfigService } = require('../services/configService');
const { extractTenantIdentifier, resolveInstituteId } = require('../middleware/tenantMiddleware');

/**
 * Multi-Tenant Architecture Test Suite
 * Tests tenant identification, data isolation, and configuration loading
 */

class TenantTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test tenant identification from different sources
   */
  async testTenantIdentification() {
    console.log('\n🧪 Testing Tenant Identification...');

    // Test subdomain extraction
    const subdomainReq = {
      get: (header) => header === 'host' ? 'abc-engineering.exampllms.com' : null,
      query: {}
    };
    
    const subdomainResult = extractTenantIdentifier(subdomainReq);
    this.logResult(
      'Subdomain Extraction',
      subdomainResult?.type === 'subdomain' && subdomainResult?.identifier === 'abc-engineering',
      `Extracted: ${subdomainResult?.identifier} (${subdomainResult?.type})`
    );

    // Test custom domain extraction
    const customDomainReq = {
      get: (header) => header === 'host' ? 'abcengineering.edu' : null,
      query: {}
    };
    
    const customDomainResult = extractTenantIdentifier(customDomainReq);
    this.logResult(
      'Custom Domain Extraction',
      customDomainResult?.type === 'domain' && customDomainResult?.identifier === 'abcengineering.edu',
      `Extracted: ${customDomainResult?.identifier} (${customDomainResult?.type})`
    );

    // Test header extraction
    const headerReq = {
      get: (header) => header === 'X-Tenant-ID' ? 'abc-engineering' : header === 'host' ? 'localhost:5000' : null,
      query: {}
    };
    
    const headerResult = extractTenantIdentifier(headerReq);
    this.logResult(
      'Header Extraction',
      headerResult?.type === 'header' && headerResult?.identifier === 'abc-engineering',
      `Extracted: ${headerResult?.identifier} (${headerResult?.type})`
    );

    // Test institute ID resolution
    try {
      const instituteId = await resolveInstituteId({
        type: 'subdomain',
        identifier: 'abc-engineering'
      });
      
      this.logResult(
        'Institute ID Resolution',
        !!instituteId,
        `Resolved to institute ID: ${instituteId}`
      );
    } catch (error) {
      this.logResult('Institute ID Resolution', false, error.message);
    }
  }

  /**
   * Test data isolation between tenants
   */
  async testDataIsolation() {
    console.log('\n🔒 Testing Data Isolation...');

    try {
      // Get institute IDs for testing
      const institutes = await query('SELECT id, name FROM institutes LIMIT 2');
      
      if (institutes.rows.length < 1) {
        this.logResult('Data Isolation', false, 'Need at least 1 institute for testing');
        return;
      }

      const institute1Id = institutes.rows[0].id;
      const institute1Name = institutes.rows[0].name;
      
      // Create tenant service for institute 1
      const tenantService1 = new TenantService(institute1Id);
      
      // Test user retrieval (should only return users from institute 1)
      const users1 = await tenantService1.getUsers();
      const allUsersFromInstitute1 = users1.rows.every(user => user.institute_id === institute1Id);
      
      this.logResult(
        'User Data Isolation',
        allUsersFromInstitute1,
        `Retrieved ${users1.rows.length} users, all from ${institute1Name}`
      );

      // Test cross-tenant access prevention
      if (institutes.rows.length >= 2) {
        const institute2Id = institutes.rows[1].id;
        
        // Try to access institute 2's users with institute 1's service
        const user2Result = await query('SELECT id FROM users WHERE institute_id = $1 LIMIT 1', [institute2Id]);
        
        if (user2Result.rows.length > 0) {
          const user2Id = user2Result.rows[0].id;
          const crossTenantUser = await tenantService1.getUserById(user2Id);
          
          this.logResult(
            'Cross-Tenant Access Prevention',
            crossTenantUser === null,
            crossTenantUser ? 'SECURITY ISSUE: Cross-tenant access allowed!' : 'Cross-tenant access properly blocked'
          );
        }
      }

    } catch (error) {
      this.logResult('Data Isolation', false, error.message);
    }
  }

  /**
   * Test configuration loading and caching
   */
  async testConfigurationLoading() {
    console.log('\n⚙️ Testing Configuration Loading...');

    try {
      // Get an institute for testing
      const institutes = await query('SELECT id FROM institutes LIMIT 1');
      
      if (institutes.rows.length === 0) {
        this.logResult('Configuration Loading', false, 'No institutes found for testing');
        return;
      }

      const instituteId = institutes.rows[0].id;
      const configService = new ConfigService(instituteId);

      // Test loading all configurations
      const allConfigs = await configService.loadAllConfigs();
      this.logResult(
        'Load All Configurations',
        typeof allConfigs === 'object' && allConfigs.theme_primary_color,
        `Loaded ${Object.keys(allConfigs).length} configuration keys`
      );

      // Test getting specific configuration
      const primaryColor = await configService.get('theme_primary_color');
      this.logResult(
        'Get Specific Configuration',
        typeof primaryColor === 'string',
        `Primary color: ${primaryColor}`
      );

      // Test setting configuration
      const testKey = 'test_setting';
      const testValue = 'test_value_' + Date.now();
      
      await configService.set(testKey, testValue, 'string', false);
      const retrievedValue = await configService.get(testKey);
      
      this.logResult(
        'Set and Get Configuration',
        retrievedValue === testValue,
        `Set: ${testValue}, Retrieved: ${retrievedValue}`
      );

      // Test public configuration filtering
      const publicConfigs = await configService.getPublicConfigs();
      const hasPrivateConfig = publicConfigs.hasOwnProperty(testKey);
      
      this.logResult(
        'Public Configuration Filtering',
        !hasPrivateConfig,
        hasPrivateConfig ? 'SECURITY ISSUE: Private config exposed!' : 'Private configs properly filtered'
      );

    } catch (error) {
      this.logResult('Configuration Loading', false, error.message);
    }
  }

  /**
   * Test super admin functionality
   */
  async testSuperAdminFunctionality() {
    console.log('\n👑 Testing Super Admin Functionality...');

    try {
      const superAdminService = new SuperAdminService();

      // Test getting all institutes
      const institutes = await superAdminService.getAllInstitutes();
      this.logResult(
        'Super Admin Institute Access',
        institutes.rows.length > 0,
        `Retrieved ${institutes.rows.length} institutes`
      );

      // Test getting all users across institutes
      const allUsers = await superAdminService.getAllUsers({ limit: 10 });
      this.logResult(
        'Super Admin User Access',
        allUsers.rows.length > 0,
        `Retrieved ${allUsers.rows.length} users across all institutes`
      );

      // Test institute creation
      const testInstitute = {
        name: 'Test Institute ' + Date.now(),
        slug: 'test-institute-' + Date.now(),
        email: `test${Date.now()}@example.com`,
        phone: '+1234567890',
        address: 'Test Address',
        website: 'https://test.example.com'
      };

      const createdInstitute = await superAdminService.createInstitute(testInstitute);
      this.logResult(
        'Super Admin Institute Creation',
        createdInstitute && createdInstitute.id,
        `Created institute: ${createdInstitute?.name}`
      );

      // Clean up test institute
      if (createdInstitute?.id) {
        await query('DELETE FROM institutes WHERE id = $1', [createdInstitute.id]);
      }

    } catch (error) {
      this.logResult('Super Admin Functionality', false, error.message);
    }
  }

  /**
   * Test tenant service validation
   */
  async testTenantServiceValidation() {
    console.log('\n🛡️ Testing Tenant Service Validation...');

    try {
      // Test service without institute ID
      const noTenantService = new TenantService(null);
      
      let validationError = null;
      try {
        await noTenantService.getUsers();
      } catch (error) {
        validationError = error;
      }
      
      this.logResult(
        'Tenant Service Validation',
        validationError && validationError.message.includes('Institute ID is required'),
        validationError ? 'Properly validates tenant requirement' : 'SECURITY ISSUE: No tenant validation!'
      );

      // Test user access validation
      const institutes = await query('SELECT id FROM institutes LIMIT 1');
      if (institutes.rows.length > 0) {
        const instituteId = institutes.rows[0].id;
        const tenantService = new TenantService(instituteId);
        
        // Test with valid user
        const users = await tenantService.getUsers({ limit: 1 });
        if (users.rows.length > 0) {
          const validUserId = users.rows[0].id;
          const hasAccess = await tenantService.validateUserAccess(validUserId);
          
          this.logResult(
            'Valid User Access Validation',
            hasAccess === true,
            'Valid user access confirmed'
          );
        }

        // Test with invalid user ID
        const fakeUserId = '00000000-0000-0000-0000-000000000000';
        const hasInvalidAccess = await tenantService.validateUserAccess(fakeUserId);
        
        this.logResult(
          'Invalid User Access Validation',
          hasInvalidAccess === false,
          'Invalid user access properly denied'
        );
      }

    } catch (error) {
      this.logResult('Tenant Service Validation', false, error.message);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting Multi-Tenant Architecture Tests...\n');

    await this.testTenantIdentification();
    await this.testDataIsolation();
    await this.testConfigurationLoading();
    await this.testSuperAdminFunctionality();
    await this.testTenantServiceValidation();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new TenantTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { TenantTester };
