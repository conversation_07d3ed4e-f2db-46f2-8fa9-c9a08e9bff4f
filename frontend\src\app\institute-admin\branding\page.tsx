'use client'

import React, { useState } from 'react'
import { 
  Palette,
  Upload,
  Eye,
  Save,
  RefreshCw,
  Image,
  Type,
  Monitor,
  Smartphone,
  Tablet,
  Download,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { FormField, SelectField } from '@/components/forms/FormField'
import ColorPicker from '@/components/institute-admin/ColorPicker'
import LogoUpload from '@/components/institute-admin/LogoUpload'
import BrandPreview from '@/components/institute-admin/BrandPreview'

const brandingSchema = Yup.object().shape({
  instituteName: Yup.string().required('Institute name is required'),
  tagline: Yup.string().max(100, 'Tagline must be less than 100 characters'),
  primaryColor: Yup.string().required('Primary color is required'),
  secondaryColor: Yup.string().required('Secondary color is required'),
  accentColor: Yup.string().required('Accent color is required'),
  fontFamily: Yup.string().required('Font family is required'),
  logoUrl: Yup.string().url('Must be a valid URL'),
  faviconUrl: Yup.string().url('Must be a valid URL'),
})

interface BrandingSettings {
  instituteName: string
  tagline: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  fontFamily: string
  logoUrl: string
  faviconUrl: string
  customCss: string
  enableCustomTheme: boolean
}

export default function BrandingPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [previewMode, setPreviewMode] = useState('desktop')
  const [isSaving, setIsSaving] = useState(false)

  const initialBranding: BrandingSettings = {
    instituteName: 'Harvard University',
    tagline: 'Excellence in Education Since 1636',
    primaryColor: '#1e40af',
    secondaryColor: '#64748b',
    accentColor: '#f59e0b',
    fontFamily: 'Inter',
    logoUrl: '',
    faviconUrl: '',
    customCss: '',
    enableCustomTheme: true,
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'colors', name: 'Colors', icon: Palette },
    { id: 'typography', name: 'Typography', icon: Type },
    { id: 'assets', name: 'Assets', icon: Image },
    { id: 'preview', name: 'Preview', icon: Eye },
  ]

  const fontOptions = [
    { value: 'Inter', label: 'Inter (Recommended)' },
    { value: 'Roboto', label: 'Roboto' },
    { value: 'Open Sans', label: 'Open Sans' },
    { value: 'Lato', label: 'Lato' },
    { value: 'Montserrat', label: 'Montserrat' },
    { value: 'Poppins', label: 'Poppins' },
    { value: 'Source Sans Pro', label: 'Source Sans Pro' },
    { value: 'Nunito', label: 'Nunito' },
  ]

  const handleSubmit = async (values: BrandingSettings) => {
    setIsSaving(true)
    try {
      // TODO: Save branding settings to API
      console.log('Saving branding settings:', values)
      await new Promise(resolve => setTimeout(resolve, 1500))
      // Show success message
    } catch (error) {
      console.error('Save branding error:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const exportTheme = () => {
    // TODO: Export theme as JSON/CSS
    console.log('Exporting theme...')
  }

  const importTheme = () => {
    // TODO: Import theme from file
    console.log('Importing theme...')
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Branding & Theme</h1>
          <p className="text-gray-600 mt-1">
            Customize your institute's visual identity and branding
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={importTheme}>
            <Upload className="mr-2 h-4 w-4" />
            Import Theme
          </Button>
          <Button variant="outline" onClick={exportTheme}>
            <Download className="mr-2 h-4 w-4" />
            Export Theme
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Settings Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                        ${activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }
                      `}
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {tab.name}
                    </button>
                  )
                })}
              </nav>
            </div>

            {/* Content */}
            <div className="p-6">
              <Formik
                initialValues={initialBranding}
                validationSchema={brandingSchema}
                onSubmit={handleSubmit}
              >
                {({ values, setFieldValue }) => (
                  <Form className="space-y-6">
                    {activeTab === 'general' && (
                      <div className="space-y-6">
                        <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
                        
                        <div className="grid grid-cols-1 gap-6">
                          <FormField
                            name="instituteName"
                            label="Institute Name"
                            placeholder="Harvard University"
                            required
                          />
                          
                          <FormField
                            name="tagline"
                            label="Tagline"
                            placeholder="Excellence in Education Since 1636"
                            help="A short description that appears with your logo"
                          />
                        </div>

                        <div className="space-y-4">
                          <h4 className="text-md font-medium text-gray-900">Theme Options</h4>
                          
                          <div className="space-y-3">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={values.enableCustomTheme}
                                onChange={(e) => setFieldValue('enableCustomTheme', e.target.checked)}
                                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                              />
                              <span className="ml-2 text-sm text-gray-700">Enable custom theme</span>
                            </label>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 'colors' && (
                      <div className="space-y-6">
                        <h3 className="text-lg font-medium text-gray-900">Color Scheme</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Primary Color
                            </label>
                            <ColorPicker
                              value={values.primaryColor}
                              onChange={(color) => setFieldValue('primaryColor', color)}
                              label="Primary"
                            />
                            <p className="text-xs text-gray-500 mt-1">Main brand color for buttons and links</p>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Secondary Color
                            </label>
                            <ColorPicker
                              value={values.secondaryColor}
                              onChange={(color) => setFieldValue('secondaryColor', color)}
                              label="Secondary"
                            />
                            <p className="text-xs text-gray-500 mt-1">Supporting color for text and borders</p>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Accent Color
                            </label>
                            <ColorPicker
                              value={values.accentColor}
                              onChange={(color) => setFieldValue('accentColor', color)}
                              label="Accent"
                            />
                            <p className="text-xs text-gray-500 mt-1">Highlight color for notifications and alerts</p>
                          </div>
                        </div>

                        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Color Preview</h4>
                          <div className="flex space-x-4">
                            <div className="flex items-center space-x-2">
                              <div 
                                className="w-6 h-6 rounded border border-gray-300"
                                style={{ backgroundColor: values.primaryColor }}
                              ></div>
                              <span className="text-sm text-gray-600">Primary</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div 
                                className="w-6 h-6 rounded border border-gray-300"
                                style={{ backgroundColor: values.secondaryColor }}
                              ></div>
                              <span className="text-sm text-gray-600">Secondary</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div 
                                className="w-6 h-6 rounded border border-gray-300"
                                style={{ backgroundColor: values.accentColor }}
                              ></div>
                              <span className="text-sm text-gray-600">Accent</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 'typography' && (
                      <div className="space-y-6">
                        <h3 className="text-lg font-medium text-gray-900">Typography</h3>
                        
                        <div className="grid grid-cols-1 gap-6">
                          <SelectField
                            name="fontFamily"
                            label="Font Family"
                            options={fontOptions}
                            required
                          />
                        </div>

                        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Font Preview</h4>
                          <div className="space-y-2" style={{ fontFamily: values.fontFamily }}>
                            <h1 className="text-2xl font-bold text-gray-900">Heading 1 - {values.fontFamily}</h1>
                            <h2 className="text-xl font-semibold text-gray-800">Heading 2 - {values.fontFamily}</h2>
                            <p className="text-base text-gray-700">
                              This is a paragraph using {values.fontFamily}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                            </p>
                            <p className="text-sm text-gray-600">Small text using {values.fontFamily}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 'assets' && (
                      <div className="space-y-6">
                        <h3 className="text-lg font-medium text-gray-900">Brand Assets</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Logo
                            </label>
                            <LogoUpload
                              value={values.logoUrl}
                              onChange={(url) => setFieldValue('logoUrl', url)}
                              type="logo"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Recommended: PNG or SVG, max 2MB, 300x100px
                            </p>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Favicon
                            </label>
                            <LogoUpload
                              value={values.faviconUrl}
                              onChange={(url) => setFieldValue('faviconUrl', url)}
                              type="favicon"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Recommended: ICO or PNG, 32x32px or 16x16px
                            </p>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Custom CSS
                          </label>
                          <textarea
                            value={values.customCss}
                            onChange={(e) => setFieldValue('customCss', e.target.value)}
                            rows={8}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-mono"
                            placeholder="/* Add your custom CSS here */
.custom-header {
  background: linear-gradient(45deg, #1e40af, #3b82f6);
}

.custom-button {
  border-radius: 8px;
  transition: all 0.2s ease;
}"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Advanced: Add custom CSS to override default styles
                          </p>
                        </div>
                      </div>
                    )}

                    {activeTab === 'preview' && (
                      <div className="space-y-6">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-medium text-gray-900">Live Preview</h3>
                          <div className="flex items-center space-x-2">
                            <button
                              type="button"
                              onClick={() => setPreviewMode('desktop')}
                              className={`p-2 rounded ${previewMode === 'desktop' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                            >
                              <Monitor className="w-4 h-4" />
                            </button>
                            <button
                              type="button"
                              onClick={() => setPreviewMode('tablet')}
                              className={`p-2 rounded ${previewMode === 'tablet' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                            >
                              <Tablet className="w-4 h-4" />
                            </button>
                            <button
                              type="button"
                              onClick={() => setPreviewMode('mobile')}
                              className={`p-2 rounded ${previewMode === 'mobile' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                            >
                              <Smartphone className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        
                        <BrandPreview
                          branding={values}
                          mode={previewMode}
                        />
                      </div>
                    )}

                    {/* Save Button */}
                    <div className="flex justify-end pt-6 border-t border-gray-200">
                      <Button
                        type="submit"
                        loading={isSaving}
                        className="flex items-center"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        Save Branding
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>

        {/* Live Preview Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Preview</h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span className="text-white text-sm font-bold">H</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Harvard University</h4>
                    <p className="text-xs text-gray-500">Excellence in Education</p>
                  </div>
                </div>
                <button 
                  className="w-full py-2 px-4 rounded text-white text-sm font-medium"
                  style={{ backgroundColor: '#1e40af' }}
                >
                  Primary Button
                </button>
              </div>
              
              <div className="text-xs text-gray-500">
                This preview updates in real-time as you make changes
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
