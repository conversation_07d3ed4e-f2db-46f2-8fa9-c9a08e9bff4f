{"name": "lms-saas-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["lms", "saas", "education", "nextjs", "typescript", "tailwindcss"], "author": "LMS SAAS Team", "license": "MIT", "description": "LMS SAAS Frontend - Multi-tenant Learning Management System", "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formik": "^2.4.6", "lucide-react": "^0.525.0", "nanoid": "^3.3.0", "next": "^15.3.5", "picocolors": "^1.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "source-map-js": "^1.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "yup": "^1.6.1", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^24.0.13", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "^15.3.5", "playwright": "^1.54.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}