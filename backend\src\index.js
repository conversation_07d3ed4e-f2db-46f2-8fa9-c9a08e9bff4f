const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { testConnection } = require('./database/connection');
const { tenantMiddleware, requireTenant, requireSuperAdmin } = require('./middleware/tenantMiddleware');
const { createTenantService } = require('./services/tenantService');
const { createConfigService } = require('./services/configService');
const { cleanupExpiredSessions } = require('./middleware/authMiddleware');
const {
  configureHelmet,
  createRateLimiters,
  createSlowDown,
  sanitizeInput,
  preventSQLInjection,
  securityLogger
} = require('./middleware/securityMiddleware');
const { checkBlockedIP } = require('./middleware/securityMonitoring');
const securityService = require('./services/securityService');

// Import routes
const authRoutes = require('./routes/authRoutes');
const protectedRoutes = require('./routes/protectedRoutes');
const domainRoutes = require('./routes/domainRoutes');
const publicRoutes = require('./routes/publicRoutes');
const profileRoutes = require('./routes/profileRoutes');
const registrationRoutes = require('./routes/registrationRoutes');
const superAdminRoutes = require('./routes/superAdminRoutes');
const instituteAdminRoutes = require('./routes/instituteAdminRoutes');

const app = express();
const PORT = process.env.PORT || 5000;

// =============================================
// MIDDLEWARE SETUP
// =============================================

// Enhanced Security middleware
app.use(configureHelmet());

// Security monitoring and logging
app.use(securityLogger);

// Check for blocked IPs
app.use(checkBlockedIP);

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID']
}));

// Enhanced Rate limiting
const rateLimiters = createRateLimiters();
app.use(rateLimiters.general);

// Note: Slow down middleware removed due to compatibility issues

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Input sanitization and SQL injection prevention
app.use(sanitizeInput);
app.use(preventSQLInjection);

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} - ${req.method} ${req.path} - IP: ${req.ip}`);
  next();
});

// =============================================
// TENANT MIDDLEWARE
// =============================================

// Apply tenant identification to all requests
app.use(tenantMiddleware);

// Session cleanup middleware (run periodically)
app.use(cleanupExpiredSessions);

// =============================================
// HEALTH CHECK ROUTES
// =============================================

app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

app.get('/health/db', async (req, res) => {
  try {
    const isConnected = await testConnection();
    if (isConnected) {
      res.status(200).json({
        status: 'OK',
        database: 'Connected',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'ERROR',
        database: 'Disconnected',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      database: 'Error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// =============================================
// API ROUTES (TO BE IMPLEMENTED)
// =============================================

// Welcome route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to LMS SAAS API',
    version: '1.0.0',
    documentation: '/api/docs',
    health: '/health',
    timestamp: new Date().toISOString()
  });
});

// API base route
app.get('/api', (req, res) => {
  res.json({
    message: 'LMS SAAS API v1.0.0',
    endpoints: {
      auth: '/api/auth',
      institutes: '/api/institutes',
      users: '/api/users',
      admin: '/api/admin'
    },
    timestamp: new Date().toISOString()
  });
});

// =============================================
// AUTHENTICATION ROUTES
// =============================================

// Mount authentication routes
app.use('/api/auth', authRoutes);

// Mount protected routes with RBAC
app.use('/api/protected', protectedRoutes);

// Mount domain management routes
app.use('/api/domains', domainRoutes);

// Mount profile management routes
app.use('/api/profile', profileRoutes);

// Mount enhanced registration routes
app.use('/api/register', registrationRoutes);

// Mount super admin routes
app.use('/api/super-admin', superAdminRoutes);

// Mount institute admin routes
app.use('/api/institute-admin', instituteAdminRoutes);

// =============================================
// TENANT-AWARE API ROUTES
// =============================================

// Tenant information endpoint
app.get('/api/tenant', (req, res) => {
  res.json({
    tenant: req.tenant,
    timestamp: new Date().toISOString()
  });
});

// Institute-specific routes (require tenant context)
app.get('/api/institute/info', requireTenant, async (req, res) => {
  try {
    const tenantService = createTenantService(req);
    const institute = await tenantService.getInstituteInfo();

    if (!institute) {
      return res.status(404).json({ error: 'Institute not found' });
    }

    res.json({
      institute: {
        id: institute.id,
        name: institute.name,
        slug: institute.slug,
        email: institute.email,
        website: institute.website,
        logo_url: institute.logo_url,
        primary_color: institute.primary_color,
        secondary_color: institute.secondary_color
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Institute configuration endpoint
app.get('/api/institute/config', requireTenant, async (req, res) => {
  try {
    const configService = createConfigService(req);
    const config = await configService.getPublicConfigs();

    res.json({ config });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Institute users endpoint (tenant-scoped)
app.get('/api/institute/users', requireTenant, async (req, res) => {
  try {
    const tenantService = createTenantService(req);
    const { role, limit = 50, offset = 0 } = req.query;

    const result = await tenantService.getUsers({
      role,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      users: result.rows,
      total: result.rowCount,
      institute: req.tenant.institute?.name
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Super admin routes (no tenant context required)
app.get('/api/admin/institutes', requireSuperAdmin, async (req, res) => {
  try {
    const { SuperAdminService } = require('./services/tenantService');
    const adminService = new SuperAdminService();

    const result = await adminService.getAllInstitutes();

    res.json({
      institutes: result.rows,
      total: result.rowCount
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Placeholder for future routes
app.use('/api/auth', (req, res) => {
  res.status(501).json({
    message: 'Authentication routes - Coming soon!',
    tenant: req.tenant?.institute?.name || 'No tenant context'
  });
});

app.use('/api/courses', requireTenant, (req, res) => {
  res.status(501).json({
    message: 'Course management routes - Coming soon!',
    institute: req.tenant.institute?.name
  });
});

// =============================================
// PUBLIC ROUTES (Landing Pages)
// =============================================

// Mount public routes (must be last to catch all unmatched routes)
app.use('/', publicRoutes);

// =============================================
// ERROR HANDLING
// =============================================

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    method: req.method,
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('❌ Global error handler:', err.stack);
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(err.status || 500).json({
    error: err.message || 'Internal Server Error',
    ...(isDevelopment && { stack: err.stack }),
    timestamp: new Date().toISOString()
  });
});

// =============================================
// SERVER STARTUP
// =============================================

const startServer = async () => {
  try {
    // Test database connection
    console.log('🔍 Testing database connection...');
    const isDbConnected = await testConnection();
    
    if (!isDbConnected) {
      console.error('❌ Failed to connect to database. Please check your database configuration.');
      process.exit(1);
    }

    // Start server
    app.listen(PORT, () => {
      console.log('🚀 LMS SAAS Backend Server Started');
      console.log(`📡 Server running on port ${PORT}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      console.log(`📊 Database health: http://localhost:${PORT}/health/db`);
      console.log(`🎯 API base: http://localhost:${PORT}/api`);
      console.log('✅ Ready to accept connections');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🔄 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start the server
startServer();

module.exports = app;
