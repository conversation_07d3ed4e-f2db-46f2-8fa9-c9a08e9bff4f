'use client'

import React from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { CreditCard, Calendar, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormField, SelectField } from '@/components/forms/FormField'
import { Modal } from '@/components/ui/modal'

const subscriptionSchema = Yup.object().shape({
  plan: Yup.string()
    .oneOf(['basic', 'premium', 'enterprise'], 'Please select a valid subscription plan')
    .required('Subscription plan is required'),
  billingCycle: Yup.string()
    .oneOf(['monthly', 'yearly'], 'Please select a valid billing cycle')
    .required('Billing cycle is required'),
  status: Yup.string()
    .oneOf(['active', 'expired', 'cancelled', 'pending'], 'Please select a valid status')
    .required('Status is required'),
})

const subscriptionPlans = [
  { value: 'basic', label: 'Basic - $99/month, $1,090/year' },
  { value: 'premium', label: 'Premium - $299/month, $3,290/year' },
  { value: 'enterprise', label: 'Enterprise - $999/month, $10,990/year' },
]

const billingCycles = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly (Save 10%)' },
]

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'pending', label: 'Pending' },
  { value: 'expired', label: 'Expired' },
  { value: 'cancelled', label: 'Cancelled' },
]

interface SubscriptionFormData {
  plan: string
  billingCycle: string
  status: string
}

interface SubscriptionFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  subscription?: any
  mode: 'edit' | 'status'
}

export default function SubscriptionFormModal({
  isOpen,
  onClose,
  onSuccess,
  subscription,
  mode
}: SubscriptionFormModalProps) {
  const initialValues: SubscriptionFormData = {
    plan: subscription?.plan || 'basic',
    billingCycle: subscription?.billingCycle || 'monthly',
    status: subscription?.status || 'active',
  }

  const handleSubmit = async (values: SubscriptionFormData, { setSubmitting }: any) => {
    try {
      const endpoint = mode === 'edit' 
        ? `/api/super-admin/subscriptions/${subscription?.id}/plan`
        : `/api/super-admin/subscriptions/${subscription?.id}/status`

      // TODO: Replace with actual API call
      console.log(`${mode} subscription:`, values)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success(`Subscription ${mode === 'edit' ? 'plan updated' : 'status updated'} successfully!`)
      onSuccess()
      onClose()
      
    } catch (error) {
      console.error(`${mode} subscription error:`, error)
      toast.error(`Failed to ${mode} subscription`)
    } finally {
      setSubmitting(false)
    }
  }

  const getPlanDetails = (plan: string) => {
    const details = {
      basic: { monthly: 99, yearly: 1090, users: 1000, courses: 10 },
      premium: { monthly: 299, yearly: 3290, users: 5000, courses: 100 },
      enterprise: { monthly: 999, yearly: 10990, users: 20000, courses: 'Unlimited' },
    }
    return details[plan as keyof typeof details] || details.basic
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'edit' ? 'Edit Subscription Plan' : 'Update Subscription Status'}`}
      size="lg"
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <CreditCard className="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {subscription?.instituteName}
            </h3>
            <p className="text-sm text-gray-600">
              {mode === 'edit' 
                ? 'Update subscription plan and billing cycle'
                : 'Change subscription status'
              }
            </p>
          </div>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={subscriptionSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ isSubmitting, values }) => {
            const planDetails = getPlanDetails(values.plan)
            const price = values.billingCycle === 'monthly' ? planDetails.monthly : planDetails.yearly
            
            return (
              <Form className="space-y-6">
                {mode === 'edit' ? (
                  <>
                    <SelectField
                      name="plan"
                      label="Subscription Plan"
                      options={subscriptionPlans}
                      required
                    />

                    <SelectField
                      name="billingCycle"
                      label="Billing Cycle"
                      options={billingCycles}
                      required
                    />

                    {/* Plan Details */}
                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <h4 className="text-sm font-medium text-purple-800 mb-3">Plan Details</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-purple-700">Price:</span>
                          <div className="font-semibold text-purple-900">
                            ${price.toLocaleString()}/{values.billingCycle === 'monthly' ? 'month' : 'year'}
                          </div>
                        </div>
                        <div>
                          <span className="text-purple-700">Users Included:</span>
                          <div className="font-semibold text-purple-900">
                            {planDetails.users.toLocaleString()}
                          </div>
                        </div>
                        <div>
                          <span className="text-purple-700">Courses:</span>
                          <div className="font-semibold text-purple-900">
                            {planDetails.courses}
                          </div>
                        </div>
                        <div>
                          <span className="text-purple-700">Billing:</span>
                          <div className="font-semibold text-purple-900">
                            {values.billingCycle === 'yearly' ? 'Save 10%' : 'Standard'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <SelectField
                      name="status"
                      label="Subscription Status"
                      options={statusOptions}
                      required
                    />

                    {/* Current Plan Info */}
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-800 mb-2">Current Plan</h4>
                      <div className="text-sm text-gray-600">
                        <div>Plan: <span className="font-medium">{subscription?.plan}</span></div>
                        <div>Billing: <span className="font-medium">{subscription?.billingCycle}</span></div>
                        <div>Current Status: <span className="font-medium">{subscription?.status}</span></div>
                      </div>
                    </div>
                  </>
                )}

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">
                    {mode === 'edit' ? 'Plan Change Effects' : 'Status Change Effects'}
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {mode === 'edit' ? (
                      <>
                        <li>• Changes take effect at next billing cycle</li>
                        <li>• Prorated billing adjustments will be applied</li>
                        <li>• Institute admin will be notified of changes</li>
                        <li>• Feature access updates immediately</li>
                      </>
                    ) : (
                      <>
                        <li>• Status change takes effect immediately</li>
                        <li>• Institute access will be updated accordingly</li>
                        <li>• Billing will be adjusted based on new status</li>
                        <li>• Admin will receive status change notification</li>
                      </>
                    )}
                  </ul>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    loading={isSubmitting}
                  >
                    {mode === 'edit' ? 'Update Plan' : 'Update Status'}
                  </Button>
                </div>
              </Form>
            )
          }}
        </Formik>
      </div>
    </Modal>
  )
}
