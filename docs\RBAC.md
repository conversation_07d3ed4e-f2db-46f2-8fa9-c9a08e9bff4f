# Role-Based Access Control (RBAC) Documentation

This document describes the Role-Based Access Control system implemented in the LMS SAAS platform.

## Overview

The RBAC system provides fine-grained access control based on user roles and permissions. It ensures that users can only access resources and perform actions that are appropriate for their role within the system.

## User Roles

### 1. Super Admin (`super_admin`)
- **Scope**: Platform-wide access
- **Description**: Highest level of access with full platform management capabilities
- **Typical Users**: Platform owners, system administrators

**Key Responsibilities:**
- Manage all institutes on the platform
- Configure platform-wide settings
- Monitor platform analytics and performance
- Manage subscription plans and billing
- Create and manage other super admin accounts

### 2. Institute Admin (`institute_admin`)
- **Scope**: Institute-specific access
- **Description**: Full administrative access within their institute
- **Typical Users**: University administrators, school principals

**Key Responsibilities:**
- Manage institute settings and branding
- Manage all users within their institute
- Oversee all courses and academic programs
- View institute-specific analytics
- Configure institute policies and procedures

### 3. Teacher (`teacher`)
- **Scope**: Course and student management within their institute
- **Description**: Educational content management and student interaction
- **Typical Users**: Professors, instructors, teaching assistants

**Key Responsibilities:**
- Create and manage their own courses
- Develop course content and materials
- Grade assignments and assessments
- Monitor student progress
- Communicate with students

### 4. Student (`student`)
- **Scope**: Learning activities within their institute
- **Description**: Access to educational content and learning tools
- **Typical Users**: Students, learners

**Key Responsibilities:**
- View and enroll in available courses
- Access course materials and content
- Submit assignments and take assessments
- View their grades and progress
- Communicate with teachers

## Permissions System

### Permission Categories

#### Platform Management
- `MANAGE_PLATFORM`: Platform-wide configuration and settings
- `MANAGE_INSTITUTES`: Create, update, delete institutes
- `MANAGE_SUBSCRIPTIONS`: Subscription and billing management
- `VIEW_ANALYTICS`: Platform-wide analytics access
- `MANAGE_SUPER_ADMINS`: Super admin account management

#### Institute Management
- `MANAGE_INSTITUTE`: Institute settings and configuration
- `MANAGE_INSTITUTE_USERS`: User management within institute
- `MANAGE_INSTITUTE_SETTINGS`: Institute-specific settings
- `MANAGE_INSTITUTE_BRANDING`: Branding and appearance
- `VIEW_INSTITUTE_ANALYTICS`: Institute-specific analytics

#### Course Management
- `MANAGE_COURSES`: Full course management (admin level)
- `CREATE_COURSES`: Create new courses
- `EDIT_OWN_COURSES`: Edit courses owned by user
- `MANAGE_COURSE_CONTENT`: Course content and materials
- `VIEW_COURSES`: View available courses

#### User Management
- `MANAGE_TEACHERS`: Teacher account management
- `MANAGE_STUDENTS`: Student account management
- `MANAGE_ENROLLMENTS`: Course enrollment management

#### Academic Operations
- `GRADE_ASSIGNMENTS`: Grade student work
- `VIEW_STUDENT_PROGRESS`: Monitor student performance
- `SUBMIT_ASSIGNMENTS`: Submit academic work
- `VIEW_GRADES`: View own academic performance

#### Communication
- `COMMUNICATE_WITH_STUDENTS`: Teacher-student communication
- `COMMUNICATE_WITH_TEACHERS`: Student-teacher communication

#### Profile Management
- `UPDATE_PROFILE`: Update own profile information

### Role-Permission Matrix

| Permission | Super Admin | Institute Admin | Teacher | Student |
|------------|-------------|-----------------|---------|---------|
| MANAGE_PLATFORM | ✅ | ❌ | ❌ | ❌ |
| MANAGE_INSTITUTES | ✅ | ❌ | ❌ | ❌ |
| MANAGE_SUBSCRIPTIONS | ✅ | ❌ | ❌ | ❌ |
| VIEW_ANALYTICS | ✅ | ❌ | ❌ | ❌ |
| MANAGE_SUPER_ADMINS | ✅ | ❌ | ❌ | ❌ |
| MANAGE_INSTITUTE | ✅ | ✅ | ❌ | ❌ |
| MANAGE_INSTITUTE_USERS | ✅ | ✅ | ❌ | ❌ |
| MANAGE_INSTITUTE_SETTINGS | ✅ | ✅ | ❌ | ❌ |
| MANAGE_INSTITUTE_BRANDING | ✅ | ✅ | ❌ | ❌ |
| VIEW_INSTITUTE_ANALYTICS | ✅ | ✅ | ❌ | ❌ |
| MANAGE_COURSES | ✅ | ✅ | ❌ | ❌ |
| CREATE_COURSES | ✅ | ✅ | ✅ | ❌ |
| EDIT_OWN_COURSES | ✅ | ✅ | ✅ | ❌ |
| MANAGE_COURSE_CONTENT | ✅ | ✅ | ✅ | ❌ |
| VIEW_COURSES | ✅ | ✅ | ✅ | ✅ |
| MANAGE_TEACHERS | ✅ | ✅ | ❌ | ❌ |
| MANAGE_STUDENTS | ✅ | ✅ | ❌ | ❌ |
| MANAGE_ENROLLMENTS | ✅ | ✅ | ✅ | ❌ |
| GRADE_ASSIGNMENTS | ✅ | ✅ | ✅ | ❌ |
| VIEW_STUDENT_PROGRESS | ✅ | ✅ | ✅ | ❌ |
| SUBMIT_ASSIGNMENTS | ✅ | ✅ | ✅ | ✅ |
| VIEW_GRADES | ✅ | ✅ | ✅ | ✅ |
| COMMUNICATE_WITH_STUDENTS | ✅ | ✅ | ✅ | ❌ |
| COMMUNICATE_WITH_TEACHERS | ✅ | ✅ | ❌ | ✅ |
| UPDATE_PROFILE | ✅ | ✅ | ✅ | ✅ |

## Implementation

### Middleware

#### Authentication Middleware
```typescript
import { authenticate } from '../middleware/auth'

// Protect routes that require authentication
app.use('/api/protected', authenticate)
```

#### Role-Based Middleware
```typescript
import { requireRole } from '../middleware/auth'
import { UserRole } from '../types/auth'

// Require specific role(s)
app.get('/api/admin/platform', requireRole(UserRole.SUPER_ADMIN))
app.get('/api/admin/institute', requireRole(UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN))
```

#### Permission-Based Middleware
```typescript
import { requirePermission } from '../middleware/auth'
import { Permission } from '../types/auth'

// Require specific permission(s)
app.get('/api/courses', requirePermission(Permission.VIEW_COURSES))
app.post('/api/courses', requirePermission(Permission.CREATE_COURSES))
```

#### Context-Aware Permissions
```typescript
import { requirePermissionWithContext } from '../middleware/auth'

// Check permissions with resource context
app.put('/api/courses/:id', requirePermissionWithContext(Permission.EDIT_OWN_COURSES))
```

### JWT Token Structure

```typescript
interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  instituteId?: string  // Only for institute-scoped roles
  iat?: number         // Issued at
  exp?: number         // Expires at
}
```

### API Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user profile

#### Super Admin
- `GET /api/admin/platform/stats` - Platform statistics
- `GET /api/admin/institutes` - List all institutes
- `POST /api/admin/institutes` - Create new institute

#### Institute Admin
- `GET /api/admin/institute/dashboard` - Institute dashboard
- `GET /api/admin/institute/users` - Institute users
- `PUT /api/admin/institute/settings` - Update institute settings

#### Teachers & Students
- `GET /api/admin/courses` - View courses (role-filtered)

## Security Considerations

### Multi-Tenancy
- Institute-scoped roles (Institute Admin, Teacher, Student) are restricted to their institute
- Cross-institute access is prevented unless user has Super Admin role
- Resource ownership is validated for fine-grained access control

### Token Security
- JWT tokens include role and institute information
- Tokens are signed with secure secrets
- Refresh tokens provide secure token renewal
- Token expiration is enforced

### Permission Escalation Prevention
- Institute Admins cannot assign Super Admin roles
- Users cannot modify their own roles
- Permission checks are performed on every request

### Audit and Monitoring
- All authentication attempts are logged
- Permission denials are tracked
- Role changes are audited
- Suspicious activity is monitored

## Best Practices

### For Developers

1. **Always use middleware**: Never implement permission checks in route handlers
2. **Principle of least privilege**: Grant minimum necessary permissions
3. **Validate context**: Check resource ownership and institute membership
4. **Handle errors gracefully**: Provide clear error messages for permission denials

### For Administrators

1. **Regular role reviews**: Periodically review user roles and permissions
2. **Secure defaults**: New users should have minimal permissions
3. **Monitor access patterns**: Watch for unusual access attempts
4. **Document role assignments**: Keep records of why users have specific roles

## Testing

The RBAC system includes comprehensive tests covering:
- Authentication middleware functionality
- Role-based access control
- Permission-based access control
- Cross-institute access prevention
- Token validation and expiration

Run tests with:
```bash
npm run test:backend
```

## Troubleshooting

### Common Issues

1. **403 Forbidden**: User lacks required role or permission
2. **401 Unauthorized**: Invalid or expired token
3. **Cross-institute access denied**: User trying to access different institute's resources

### Debug Mode

Enable debug logging:
```bash
DEBUG_ROUTES=true npm run dev:backend
```

This will log all permission checks and access attempts for debugging purposes.
