'use client'

import React, { useState } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import { 
  Building2, 
  User, 
  Mail, 
  Phone, 
  Globe, 
  MapPin,
  CreditCard,
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormField, SelectField } from '@/components/forms/FormField'

const instituteRegistrationSchema = Yup.object().shape({
  // Institute Information
  instituteName: Yup.string()
    .min(2, 'Institute name must be at least 2 characters')
    .max(100, 'Institute name must be less than 100 characters')
    .required('Institute name is required'),
  instituteEmail: Yup.string()
    .email('Please enter a valid email')
    .required('Institute email is required'),
  website: Yup.string()
    .url('Please enter a valid URL')
    .nullable(),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  phone: Yup.string()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number'),
  
  // Address
  street: Yup.string().max(100, 'Street address is too long'),
  city: Yup.string().max(50, 'City name is too long'),
  state: Yup.string().max(50, 'State name is too long'),
  country: Yup.string().required('Country is required'),
  zipCode: Yup.string().max(20, 'ZIP code is too long'),
  
  // Admin User
  adminFirstName: Yup.string()
    .min(1, 'First name is required')
    .max(50, 'First name is too long')
    .required('First name is required'),
  adminLastName: Yup.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name is too long')
    .required('Last name is required'),
  adminEmail: Yup.string()
    .email('Please enter a valid email')
    .required('Admin email is required'),
  adminPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('adminPassword')], 'Passwords must match')
    .required('Please confirm your password'),
  
  // Subscription
  subscriptionPlan: Yup.string()
    .oneOf(['basic', 'premium', 'enterprise'], 'Please select a valid plan')
    .required('Subscription plan is required'),
  
  // Terms
  acceptTerms: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions')
    .required('You must accept the terms and conditions'),
})

const subscriptionPlans = [
  {
    value: 'basic',
    label: 'Basic Plan',
    description: 'Up to 100 users, 10 courses, 1GB storage',
    price: '$29/month',
    features: ['Email support', 'Basic analytics', 'Standard features'],
  },
  {
    value: 'premium',
    label: 'Premium Plan',
    description: 'Up to 1,000 users, 100 courses, 10GB storage',
    price: '$99/month',
    features: ['Priority support', 'Advanced analytics', 'Custom domain', 'All features'],
    popular: true,
  },
  {
    value: 'enterprise',
    label: 'Enterprise Plan',
    description: 'Unlimited users, courses, and storage',
    price: 'Contact us',
    features: ['Dedicated support', 'Custom integrations', 'SLA guarantee', 'White-label'],
  },
]

const countries = [
  { value: 'US', label: 'United States' },
  { value: 'CA', label: 'Canada' },
  { value: 'GB', label: 'United Kingdom' },
  { value: 'AU', label: 'Australia' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
  { value: 'IN', label: 'India' },
  { value: 'JP', label: 'Japan' },
  { value: 'BR', label: 'Brazil' },
  { value: 'MX', label: 'Mexico' },
]

interface FormValues {
  instituteName: string
  instituteEmail: string
  website: string
  description: string
  phone: string
  street: string
  city: string
  state: string
  country: string
  zipCode: string
  adminFirstName: string
  adminLastName: string
  adminEmail: string
  adminPassword: string
  confirmPassword: string
  subscriptionPlan: string
  acceptTerms: boolean
}

export default function RegisterInstitutePage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 4

  const initialValues: FormValues = {
    instituteName: '',
    instituteEmail: '',
    website: '',
    description: '',
    phone: '',
    street: '',
    city: '',
    state: '',
    country: '',
    zipCode: '',
    adminFirstName: '',
    adminLastName: '',
    adminEmail: '',
    adminPassword: '',
    confirmPassword: '',
    subscriptionPlan: 'premium',
    acceptTerms: false,
  }

  const handleSubmit = async (values: FormValues, { setSubmitting }: any) => {
    try {
      // Prepare the registration data
      const registrationData = {
        name: values.instituteName,
        email: values.instituteEmail,
        website: values.website || undefined,
        description: values.description || undefined,
        phone: values.phone || undefined,
        address: values.street ? {
          street: values.street,
          city: values.city,
          state: values.state,
          country: values.country,
          zipCode: values.zipCode,
        } : undefined,
        subscriptionPlan: values.subscriptionPlan,
        adminUser: {
          firstName: values.adminFirstName,
          lastName: values.adminLastName,
          email: values.adminEmail,
          password: values.adminPassword,
        },
      }

      // TODO: Replace with actual API call
      console.log('Institute registration data:', registrationData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('Institute registered successfully!')
      
      // TODO: Handle successful registration (store tokens, redirect)
      router.push('/admin/dashboard')
      
    } catch (error) {
      console.error('Registration error:', error)
      toast.error('Registration failed. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Institute Information'
      case 2: return 'Address & Contact'
      case 3: return 'Admin Account'
      case 4: return 'Subscription Plan'
      default: return 'Registration'
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return Building2
      case 2: return MapPin
      case 3: return User
      case 4: return CreditCard
      default: return Building2
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Register Your Institute</h1>
          <p className="mt-2 text-gray-600">
            Join thousands of educational institutions using our platform
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {Array.from({ length: totalSteps }, (_, i) => {
              const step = i + 1
              const Icon = getStepIcon(step)
              const isActive = step === currentStep
              const isCompleted = step < currentStep

              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 
                    ${isActive ? 'border-blue-600 bg-blue-600 text-white' : 
                      isCompleted ? 'border-green-600 bg-green-600 text-white' : 
                      'border-gray-300 bg-white text-gray-400'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </div>
                  <div className="ml-3 hidden sm:block">
                    <p className={`text-sm font-medium ${
                      isActive ? 'text-blue-600' : 
                      isCompleted ? 'text-green-600' : 
                      'text-gray-500'
                    }`}>
                      Step {step}
                    </p>
                    <p className={`text-xs ${
                      isActive ? 'text-blue-600' : 
                      isCompleted ? 'text-green-600' : 
                      'text-gray-500'
                    }`}>
                      {getStepTitle(step)}
                    </p>
                  </div>
                  {step < totalSteps && (
                    <div className={`
                      w-16 h-0.5 ml-4 
                      ${isCompleted ? 'bg-green-600' : 'bg-gray-300'}
                    `} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Registration Form */}
        <div className="bg-white rounded-lg shadow-sm border p-8">
          <Formik
            initialValues={initialValues}
            validationSchema={instituteRegistrationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, values, setFieldValue }) => (
              <Form className="space-y-6">
                {/* Step 1: Institute Information */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <Building2 className="mx-auto h-12 w-12 text-blue-600" />
                      <h2 className="mt-2 text-xl font-semibold text-gray-900">
                        Institute Information
                      </h2>
                      <p className="text-gray-600">
                        Tell us about your educational institution
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="instituteName"
                        label="Institute Name"
                        placeholder="Enter your institute name"
                        required
                      />

                      <FormField
                        name="instituteEmail"
                        label="Institute Email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />

                      <FormField
                        name="website"
                        label="Website"
                        type="url"
                        placeholder="https://yourinstitute.edu"
                      />

                      <FormField
                        name="phone"
                        label="Phone Number"
                        type="tel"
                        placeholder="+****************"
                      />
                    </div>

                    <FormField
                      name="description"
                      label="Description"
                      as="textarea"
                      rows={3}
                      placeholder="Brief description of your institute..."
                    />
                  </div>
                )}

                {/* Step 2: Address & Contact */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <MapPin className="mx-auto h-12 w-12 text-blue-600" />
                      <h2 className="mt-2 text-xl font-semibold text-gray-900">
                        Address & Contact
                      </h2>
                      <p className="text-gray-600">
                        Where is your institute located?
                      </p>
                    </div>

                    <div className="space-y-6">
                      <FormField
                        name="street"
                        label="Street Address"
                        placeholder="123 Education Street"
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          name="city"
                          label="City"
                          placeholder="City name"
                        />

                        <FormField
                          name="state"
                          label="State/Province"
                          placeholder="State or province"
                        />

                        <SelectField
                          name="country"
                          label="Country"
                          options={countries}
                          required
                        />

                        <FormField
                          name="zipCode"
                          label="ZIP/Postal Code"
                          placeholder="12345"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Admin Account */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <User className="mx-auto h-12 w-12 text-blue-600" />
                      <h2 className="mt-2 text-xl font-semibold text-gray-900">
                        Admin Account
                      </h2>
                      <p className="text-gray-600">
                        Create the primary administrator account
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="adminFirstName"
                        label="First Name"
                        placeholder="John"
                        required
                      />

                      <FormField
                        name="adminLastName"
                        label="Last Name"
                        placeholder="Doe"
                        required
                      />

                      <FormField
                        name="adminEmail"
                        label="Email Address"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />

                      <div></div>

                      <FormField
                        name="adminPassword"
                        label="Password"
                        type="password"
                        placeholder="Create a strong password"
                        required
                      />

                      <FormField
                        name="confirmPassword"
                        label="Confirm Password"
                        type="password"
                        placeholder="Confirm your password"
                        required
                      />
                    </div>

                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex">
                        <AlertCircle className="h-5 w-5 text-blue-400 mr-2" />
                        <div>
                          <h3 className="text-sm font-medium text-blue-800">
                            Password Requirements
                          </h3>
                          <ul className="text-sm text-blue-700 mt-1 list-disc list-inside">
                            <li>At least 8 characters long</li>
                            <li>Contains at least one lowercase letter</li>
                            <li>Contains at least one uppercase letter</li>
                            <li>Contains at least one number</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 4: Subscription Plan */}
                {currentStep === 4 && (
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <CreditCard className="mx-auto h-12 w-12 text-blue-600" />
                      <h2 className="mt-2 text-xl font-semibold text-gray-900">
                        Choose Your Plan
                      </h2>
                      <p className="text-gray-600">
                        Select the subscription plan that fits your needs
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {subscriptionPlans.map((plan) => (
                        <div
                          key={plan.value}
                          className={`
                            relative p-6 border-2 rounded-lg cursor-pointer transition-all
                            ${values.subscriptionPlan === plan.value
                              ? 'border-blue-600 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                            }
                            ${plan.popular ? 'ring-2 ring-blue-600' : ''}
                          `}
                          onClick={() => setFieldValue('subscriptionPlan', plan.value)}
                        >
                          {plan.popular && (
                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                              <span className="bg-blue-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                                Most Popular
                              </span>
                            </div>
                          )}

                          <div className="text-center">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {plan.label}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {plan.description}
                            </p>
                            <p className="text-2xl font-bold text-gray-900 mt-4">
                              {plan.price}
                            </p>
                          </div>

                          <ul className="mt-6 space-y-2">
                            {plan.features.map((feature, index) => (
                              <li key={index} className="flex items-center text-sm text-gray-600">
                                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                {feature}
                              </li>
                            ))}
                          </ul>

                          {values.subscriptionPlan === plan.value && (
                            <div className="absolute top-4 right-4">
                              <CheckCircle className="h-6 w-6 text-blue-600" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        id="acceptTerms"
                        checked={values.acceptTerms}
                        onChange={(e) => setFieldValue('acceptTerms', e.target.checked)}
                        className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="acceptTerms" className="text-sm text-gray-600">
                        I agree to the{' '}
                        <a href="/terms" className="text-blue-600 hover:underline">
                          Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href="/privacy" className="text-blue-600 hover:underline">
                          Privacy Policy
                        </a>
                      </label>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    Previous
                  </Button>

                  {currentStep < totalSteps ? (
                    <Button type="button" onClick={nextStep}>
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  ) : (
                    <Button type="submit" loading={isSubmitting}>
                      Register Institute
                    </Button>
                  )}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  )
}
