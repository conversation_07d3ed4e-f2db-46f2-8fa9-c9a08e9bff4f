'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/store/authStore'
import AccountStatusGuard from './AccountStatusGuard'
import { UserRole } from '@/types/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRoles?: UserRole[]
  requireActive?: boolean
  requireVerified?: boolean
  redirectTo?: string
  fallbackComponent?: React.ReactNode
}

export default function ProtectedRoute({
  children,
  requiredRoles = [],
  requireActive = true,
  requireVerified = true,
  redirectTo = '/auth/login',
  fallbackComponent
}: ProtectedRouteProps) {
  const router = useRouter()
  const { user, isLoading, isAuthenticated } = useAuth()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAccess = async () => {
      // Wait for auth to finish loading
      if (isLoading) {
        return
      }

      // If not authenticated, redirect to login
      if (!isAuthenticated || !user) {
        router.push(redirectTo)
        return
      }

      // Check role requirements
      if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
        // User doesn't have required role
        router.push('/unauthorized')
        return
      }

      setIsChecking(false)
    }

    checkAccess()
  }, [isLoading, isAuthenticated, user, requiredRoles, router, redirectTo])

  // Show loading while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center mb-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-center text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If not authenticated, don't render anything (redirect will happen)
  if (!isAuthenticated || !user) {
    return null
  }

  // Check role access
  if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-6">
              You don't have permission to access this page.
            </p>
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:underline"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Wrap with account status guard
  return (
    <AccountStatusGuard
      requireActive={requireActive}
      requireVerified={requireVerified}
      fallbackComponent={fallbackComponent}
    >
      {children}
    </AccountStatusGuard>
  )
}

// Convenience components for common protection patterns
export const StudentRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.STUDENT]}>
    {children}
  </ProtectedRoute>
)

export const TeacherRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.TEACHER]}>
    {children}
  </ProtectedRoute>
)

export const InstituteAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.INSTITUTE_ADMIN]}>
    {children}
  </ProtectedRoute>
)

export const SuperAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.SUPER_ADMIN]}>
    {children}
  </ProtectedRoute>
)

export const TeacherOrAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.TEACHER, UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN]}>
    {children}
  </ProtectedRoute>
)

export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={[UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN]}>
    {children}
  </ProtectedRoute>
)

// Hook for checking permissions in components
export const usePermissions = () => {
  const { user } = useAuth()

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role
  }

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false
  }

  const isStudent = (): boolean => hasRole(UserRole.STUDENT)
  const isTeacher = (): boolean => hasRole(UserRole.TEACHER)
  const isInstituteAdmin = (): boolean => hasRole(UserRole.INSTITUTE_ADMIN)
  const isSuperAdmin = (): boolean => hasRole(UserRole.SUPER_ADMIN)
  const isAdmin = (): boolean => hasAnyRole([UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN])
  const isTeacherOrAdmin = (): boolean => hasAnyRole([UserRole.TEACHER, UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN])

  return {
    user,
    hasRole,
    hasAnyRole,
    isStudent,
    isTeacher,
    isInstituteAdmin,
    isSuperAdmin,
    isAdmin,
    isTeacherOrAdmin,
  }
}
