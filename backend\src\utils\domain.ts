import dns from 'dns'
import { promisify } from 'util'
import { DomainStatus } from '../types/institute'

const resolveTxt = promisify(dns.resolveTxt)

/**
 * Verify domain ownership via DNS TXT record
 */
export const verifyDomainOwnership = async (
  domain: string, 
  expectedToken: string
): Promise<{ verified: boolean; error?: string }> => {
  try {
    const txtRecordName = `_lms-verification.${domain}`
    
    // Query DNS TXT records
    const txtRecords = await resolveTxt(txtRecordName)
    
    // Flatten the array of arrays and check for our token
    const allRecords = txtRecords.flat()
    const tokenFound = allRecords.some(record => record === expectedToken)
    
    if (tokenFound) {
      return { verified: true }
    } else {
      return { 
        verified: false, 
        error: `Verification token not found in TXT record for ${txtRecordName}` 
      }
    }
  } catch (error) {
    console.error('DNS verification error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('ENOTFOUND') || error.message.includes('ENODATA')) {
        return { 
          verified: false, 
          error: `TXT record not found for _lms-verification.${domain}` 
        }
      }
      return { 
        verified: false, 
        error: `DNS lookup failed: ${error.message}` 
      }
    }
    
    return { 
      verified: false, 
      error: 'Unknown DNS verification error' 
    }
  }
}

/**
 * Check if domain is accessible via HTTP/HTTPS
 */
export const checkDomainAccessibility = async (
  domain: string
): Promise<{ accessible: boolean; https: boolean; error?: string }> => {
  try {
    // Try HTTPS first
    const httpsResponse = await fetch(`https://${domain}`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })
    
    if (httpsResponse.ok) {
      return { accessible: true, https: true }
    }
  } catch (httpsError) {
    console.log('HTTPS check failed:', httpsError)
  }
  
  try {
    // Try HTTP as fallback
    const httpResponse = await fetch(`http://${domain}`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })
    
    if (httpResponse.ok) {
      return { accessible: true, https: false }
    }
  } catch (httpError) {
    console.log('HTTP check failed:', httpError)
  }
  
  return { 
    accessible: false, 
    https: false, 
    error: 'Domain is not accessible via HTTP or HTTPS' 
  }
}

/**
 * Validate domain format
 */
export const validateDomainFormat = (domain: string): { valid: boolean; error?: string } => {
  // Basic domain regex
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
  
  if (!domain) {
    return { valid: false, error: 'Domain is required' }
  }
  
  if (domain.length > 253) {
    return { valid: false, error: 'Domain is too long (max 253 characters)' }
  }
  
  if (!domainRegex.test(domain)) {
    return { valid: false, error: 'Invalid domain format' }
  }
  
  // Check for reserved domains
  const reservedDomains = [
    'localhost',
    'example.com',
    'example.org',
    'example.net',
    'test.com',
    'test.org',
    'test.net',
  ]
  
  if (reservedDomains.includes(domain.toLowerCase())) {
    return { valid: false, error: 'Reserved domain cannot be used' }
  }
  
  return { valid: true }
}

/**
 * Generate SSL certificate using Let's Encrypt (mock implementation)
 */
export const provisionSSLCertificate = async (
  domain: string
): Promise<{ success: boolean; certificateId?: string; error?: string }> => {
  try {
    // Mock SSL certificate provisioning
    // In a real implementation, this would use ACME protocol with Let's Encrypt
    
    console.log(`Provisioning SSL certificate for domain: ${domain}`)
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock success/failure (90% success rate)
    const success = Math.random() > 0.1
    
    if (success) {
      const certificateId = `cert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      return {
        success: true,
        certificateId,
      }
    } else {
      return {
        success: false,
        error: 'Failed to provision SSL certificate. Please try again later.',
      }
    }
  } catch (error) {
    console.error('SSL provisioning error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SSL provisioning error',
    }
  }
}

/**
 * Check SSL certificate status
 */
export const checkSSLCertificate = async (
  domain: string
): Promise<{ 
  valid: boolean
  issuer?: string
  validFrom?: Date
  validTo?: Date
  daysUntilExpiry?: number
  error?: string 
}> => {
  try {
    // Mock SSL certificate check
    // In a real implementation, this would check the actual certificate
    
    const response = await fetch(`https://${domain}`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000),
    })
    
    if (response.ok) {
      // Mock certificate info
      const validFrom = new Date()
      const validTo = new Date()
      validTo.setDate(validTo.getDate() + 90) // 90 days validity
      
      const daysUntilExpiry = Math.ceil((validTo.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
      
      return {
        valid: true,
        issuer: 'Let\'s Encrypt',
        validFrom,
        validTo,
        daysUntilExpiry,
      }
    } else {
      return {
        valid: false,
        error: 'SSL certificate not found or invalid',
      }
    }
  } catch (error) {
    console.error('SSL check error:', error)
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'SSL certificate check failed',
    }
  }
}

/**
 * Get domain verification instructions
 */
export const getDomainVerificationInstructions = (
  domain: string,
  verificationToken: string
): {
  txtRecord: {
    name: string
    value: string
    ttl: number
  }
  instructions: string[]
} => {
  return {
    txtRecord: {
      name: `_lms-verification.${domain}`,
      value: verificationToken,
      ttl: 300,
    },
    instructions: [
      '1. Log in to your domain registrar or DNS provider',
      '2. Navigate to the DNS management section',
      `3. Add a new TXT record with the following details:`,
      `   - Name: _lms-verification.${domain}`,
      `   - Value: ${verificationToken}`,
      `   - TTL: 300 (or 5 minutes)`,
      '4. Save the DNS record',
      '5. Wait for DNS propagation (usually 5-30 minutes, can take up to 24 hours)',
      '6. Click "Verify Domain" to complete the verification process',
      '',
      'Note: DNS changes can take time to propagate. If verification fails, please wait and try again.',
    ],
  }
}

/**
 * Monitor domain status and update accordingly
 */
export const updateDomainStatus = async (
  domain: string,
  currentStatus: DomainStatus,
  verificationToken?: string
): Promise<DomainStatus> => {
  try {
    switch (currentStatus) {
      case DomainStatus.PENDING:
        if (verificationToken) {
          const verification = await verifyDomainOwnership(domain, verificationToken)
          if (verification.verified) {
            return DomainStatus.VERIFIED
          }
        }
        return DomainStatus.PENDING
        
      case DomainStatus.VERIFIED:
        // Check if SSL should be provisioned
        const sslCheck = await checkSSLCertificate(domain)
        if (sslCheck.valid) {
          return DomainStatus.SSL_ACTIVE
        } else {
          return DomainStatus.SSL_PENDING
        }
        
      case DomainStatus.SSL_PENDING:
        // Check if SSL is now active
        const sslStatus = await checkSSLCertificate(domain)
        if (sslStatus.valid) {
          return DomainStatus.SSL_ACTIVE
        }
        return DomainStatus.SSL_PENDING
        
      case DomainStatus.SSL_ACTIVE:
        // Verify SSL is still valid
        const currentSSL = await checkSSLCertificate(domain)
        if (!currentSSL.valid) {
          return DomainStatus.SSL_FAILED
        }
        return DomainStatus.SSL_ACTIVE
        
      default:
        return currentStatus
    }
  } catch (error) {
    console.error('Domain status update error:', error)
    return DomainStatus.FAILED
  }
}
