[{"name": "max_connections", "setting": "100", "unit": null, "category": "Connections and Authentication / Connection Settings", "short_desc": "Sets the maximum number of concurrent connections.", "context": "postmaster", "vartype": "integer", "source": "configuration file", "min_val": "1", "max_val": "262143", "boot_val": "100", "reset_val": "100"}, {"name": "default_statistics_target", "setting": "100", "unit": null, "category": "Query Tuning / Other Planner Options", "short_desc": "Sets the default statistics target.", "context": "user", "vartype": "integer", "source": "default", "min_val": "1", "max_val": "10000", "boot_val": "100", "reset_val": "100"}, {"name": "effective_cache_size", "setting": "524288", "unit": "8kB", "category": "Query Tuning / Planner Cost Constants", "short_desc": "Sets the planner's assumption about the total size of the data caches.", "context": "user", "vartype": "integer", "source": "default", "min_val": "1", "max_val": "2147483647", "boot_val": "524288", "reset_val": "524288"}, {"name": "random_page_cost", "setting": "4", "unit": null, "category": "Query Tuning / Planner Cost Constants", "short_desc": "Sets the planner's estimate of the cost of a nonsequentially fetched disk page.", "context": "user", "vartype": "real", "source": "default", "min_val": "0", "max_val": "1.79769e+308", "boot_val": "4", "reset_val": "4"}, {"name": "effective_io_concurrency", "setting": "0", "unit": null, "category": "Resource Usage / Asynchronous Behavior", "short_desc": "Number of simultaneous requests that can be handled efficiently by the disk subsystem.", "context": "user", "vartype": "integer", "source": "default", "min_val": "0", "max_val": "1000", "boot_val": "0", "reset_val": "0"}, {"name": "max_parallel_maintenance_workers", "setting": "2", "unit": null, "category": "Resource Usage / Asynchronous Behavior", "short_desc": "Sets the maximum number of parallel processes per maintenance operation.", "context": "user", "vartype": "integer", "source": "default", "min_val": "0", "max_val": "1024", "boot_val": "2", "reset_val": "2"}, {"name": "max_parallel_workers", "setting": "8", "unit": null, "category": "Resource Usage / Asynchronous Behavior", "short_desc": "Sets the maximum number of parallel workers that can be active at one time.", "context": "user", "vartype": "integer", "source": "default", "min_val": "0", "max_val": "1024", "boot_val": "8", "reset_val": "8"}, {"name": "max_parallel_workers_per_gather", "setting": "2", "unit": null, "category": "Resource Usage / Asynchronous Behavior", "short_desc": "Sets the maximum number of parallel processes per executor node.", "context": "user", "vartype": "integer", "source": "default", "min_val": "0", "max_val": "1024", "boot_val": "2", "reset_val": "2"}, {"name": "max_worker_processes", "setting": "8", "unit": null, "category": "Resource Usage / Asynchronous Behavior", "short_desc": "Maximum number of concurrent worker processes.", "context": "postmaster", "vartype": "integer", "source": "default", "min_val": "0", "max_val": "262143", "boot_val": "8", "reset_val": "8"}, {"name": "maintenance_work_mem", "setting": "65536", "unit": "kB", "category": "Resource Usage / Memory", "short_desc": "Sets the maximum memory to be used for maintenance operations.", "context": "user", "vartype": "integer", "source": "default", "min_val": "64", "max_val": "2097151", "boot_val": "65536", "reset_val": "65536"}, {"name": "shared_buffers", "setting": "16384", "unit": "8kB", "category": "Resource Usage / Memory", "short_desc": "Sets the number of shared memory buffers used by the server.", "context": "postmaster", "vartype": "integer", "source": "configuration file", "min_val": "16", "max_val": "1073741823", "boot_val": "16384", "reset_val": "16384"}, {"name": "work_mem", "setting": "4096", "unit": "kB", "category": "Resource Usage / Memory", "short_desc": "Sets the maximum memory to be used for query workspaces.", "context": "user", "vartype": "integer", "source": "default", "min_val": "64", "max_val": "2097151", "boot_val": "4096", "reset_val": "4096"}, {"name": "checkpoint_completion_target", "setting": "0.9", "unit": null, "category": "Write-Ahead Log / Checkpoints", "short_desc": "Time spent flushing dirty buffers during checkpoint, as fraction of checkpoint interval.", "context": "sighup", "vartype": "real", "source": "default", "min_val": "0", "max_val": "1", "boot_val": "0.9", "reset_val": "0.9"}, {"name": "max_wal_size", "setting": "1024", "unit": "MB", "category": "Write-Ahead Log / Checkpoints", "short_desc": "Sets the WAL size that triggers a checkpoint.", "context": "sighup", "vartype": "integer", "source": "configuration file", "min_val": "2", "max_val": "2097151", "boot_val": "1024", "reset_val": "1024"}, {"name": "min_wal_size", "setting": "80", "unit": "MB", "category": "Write-Ahead Log / Checkpoints", "short_desc": "Sets the minimum size to shrink the WAL to.", "context": "sighup", "vartype": "integer", "source": "configuration file", "min_val": "2", "max_val": "2097151", "boot_val": "80", "reset_val": "80"}, {"name": "wal_buffers", "setting": "512", "unit": "8kB", "category": "Write-Ahead Log / Settings", "short_desc": "Sets the number of disk-page buffers in shared memory for WAL.", "context": "postmaster", "vartype": "integer", "source": "default", "min_val": "-1", "max_val": "262143", "boot_val": "-1", "reset_val": "512"}]