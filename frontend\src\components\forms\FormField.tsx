import React from 'react'
import { useField } from 'formik'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { Eye, EyeOff } from 'lucide-react'

interface FormFieldProps {
  name: string
  label: string
  type?: string
  placeholder?: string
  required?: boolean
  className?: string
  disabled?: boolean
  autoComplete?: string
  description?: string
}

export function FormField({
  name,
  label,
  type = 'text',
  placeholder,
  required = false,
  className,
  disabled = false,
  autoComplete,
  description,
  ...props
}: FormFieldProps) {
  const [field, meta] = useField(name)
  const [showPassword, setShowPassword] = React.useState(false)
  
  const hasError = meta.touched && meta.error
  const isPassword = type === 'password'
  const inputType = isPassword && showPassword ? 'text' : type

  return (
    <div className={cn('space-y-2', className)}>
      <Label htmlFor={name} className={cn(hasError && 'text-destructive')}>
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      
      <div className="relative">
        <Input
          {...field}
          {...props}
          id={name}
          type={inputType}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          className={cn(
            hasError && 'border-destructive focus-visible:ring-destructive',
            isPassword && 'pr-10'
          )}
        />
        
        {isPassword && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-muted-foreground hover:text-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-muted-foreground hover:text-foreground" />
            )}
          </button>
        )}
      </div>
      
      {description && !hasError && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      
      {hasError && (
        <p className="text-sm text-destructive">{meta.error}</p>
      )}
    </div>
  )
}

interface SelectFieldProps extends Omit<FormFieldProps, 'type'> {
  options: { value: string; label: string }[]
}

export function SelectField({
  name,
  label,
  placeholder = 'Select an option',
  required = false,
  className,
  disabled = false,
  options,
  description,
  ...props
}: SelectFieldProps) {
  const [field, meta] = useField(name)
  const hasError = meta.touched && meta.error

  return (
    <div className={cn('space-y-2', className)}>
      <Label htmlFor={name} className={cn(hasError && 'text-destructive')}>
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      
      <select
        {...field}
        {...props}
        id={name}
        disabled={disabled}
        className={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          hasError && 'border-destructive focus-visible:ring-destructive'
        )}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {description && !hasError && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      
      {hasError && (
        <p className="text-sm text-destructive">{meta.error}</p>
      )}
    </div>
  )
}

interface CheckboxFieldProps extends Omit<FormFieldProps, 'type' | 'placeholder' | 'label'> {
  label: React.ReactNode
  description?: string
}

export function CheckboxField({
  name,
  label,
  required = false,
  className,
  disabled = false,
  description,
  ...props
}: CheckboxFieldProps) {
  const [field, meta] = useField(name)
  const hasError = meta.touched && meta.error

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-start space-x-2">
        <input
          {...field}
          {...props}
          id={name}
          type="checkbox"
          disabled={disabled}
          checked={field.value}
          className={cn(
            'h-4 w-4 rounded border border-input bg-background text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            hasError && 'border-destructive'
          )}
        />
        <div className="space-y-1">
          <Label 
            htmlFor={name} 
            className={cn(
              'text-sm font-normal cursor-pointer',
              hasError && 'text-destructive'
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      </div>
      
      {hasError && (
        <p className="text-sm text-destructive ml-6">{meta.error}</p>
      )}
    </div>
  )
}
