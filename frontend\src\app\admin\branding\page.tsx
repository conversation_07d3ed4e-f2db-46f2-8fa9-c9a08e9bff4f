'use client'

import React, { useState } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { 
  Upload, 
  Palette, 
  Eye, 
  Save, 
  RotateCcw,
  Image as ImageIcon,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormField } from '@/components/forms/FormField'


const brandingSchema = Yup.object().shape({
  instituteName: Yup.string()
    .min(2, 'Institute name must be at least 2 characters')
    .max(100, 'Institute name must be less than 100 characters')
    .required('Institute name is required'),
  primaryColor: Yup.string()
    .matches(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color')
    .required('Primary color is required'),
  secondaryColor: Yup.string()
    .matches(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color')
    .required('Secondary color is required'),
  accentColor: Yup.string()
    .matches(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color')
    .required('Accent color is required'),
  tagline: Yup.string()
    .max(200, 'Tagline must be less than 200 characters'),
  website: Yup.string()
    .url('Please enter a valid URL')
    .nullable(),
})

interface BrandingFormValues {
  instituteName: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  tagline: string
  website: string
}

export default function BrandingPage() {
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string>('')
  const [faviconFile, setFaviconFile] = useState<File | null>(null)
  const [faviconPreview, setFaviconPreview] = useState<string>('')
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // Mock current branding data
  const currentBranding = {
    instituteName: 'Example University',
    primaryColor: '#3B82F6',
    secondaryColor: '#1E40AF',
    accentColor: '#F59E0B',
    tagline: 'Excellence in Education',
    website: 'https://example.edu',
    logoUrl: '',
    faviconUrl: '',
  }

  const handleFileUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: 'logo' | 'favicon'
  ) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPEG, PNG, or SVG)')
      return
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB')
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      if (type === 'logo') {
        setLogoFile(file)
        setLogoPreview(result)
      } else {
        setFaviconFile(file)
        setFaviconPreview(result)
      }
    }
    reader.readAsDataURL(file)
  }

  const removeFile = (type: 'logo' | 'favicon') => {
    if (type === 'logo') {
      setLogoFile(null)
      setLogoPreview('')
    } else {
      setFaviconFile(null)
      setFaviconPreview('')
    }
  }

  const handleSubmit = async (values: BrandingFormValues, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Branding update:', { ...values, logoFile, faviconFile })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success('Branding updated successfully!')
      
    } catch (error) {
      console.error('Branding update error:', error)
      toast.error('Failed to update branding. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const resetToDefaults = () => {
    setLogoFile(null)
    setLogoPreview('')
    setFaviconFile(null)
    setFaviconPreview('')
    toast('Reset to default values')
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Institute Branding</h1>
          <p className="text-gray-600 mt-1">
            Customize your institute's visual identity and branding
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {isPreviewMode ? 'Edit Mode' : 'Preview'}
          </Button>
          <Button variant="outline" onClick={resetToDefaults}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
        </div>
      </div>

      {isPreviewMode ? (
        /* Preview Mode */
        <div className="bg-white rounded-lg shadow-sm border p-8">
          <div className="text-center space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Preview Mode</h2>
            <p className="text-gray-600">
              This is how your branding will appear to users
            </p>
            
            {/* Preview content would go here */}
            <div className="p-8 border-2 border-dashed border-gray-300 rounded-lg">
              <p className="text-gray-500">
                Preview functionality will be implemented with actual branding data
              </p>
            </div>
          </div>
        </div>
      ) : (
        /* Edit Mode */
        <Formik
          initialValues={currentBranding}
          validationSchema={brandingSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values }) => (
            <Form className="space-y-6">
              {/* Logo and Favicon Upload */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Logo & Favicon
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Logo Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Institute Logo
                    </label>
                    <div className="space-y-4">
                      {logoPreview ? (
                        <div className="relative">
                          <img
                            src={logoPreview}
                            alt="Logo preview"
                            className="w-full h-32 object-contain border border-gray-200 rounded-lg bg-gray-50"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-2 right-2 h-6 w-6"
                            onClick={() => removeFile('logo')}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            Upload your institute logo
                          </p>
                        </div>
                      )}
                      
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e, 'logo')}
                        className="hidden"
                        id="logo-upload"
                      />
                      <label htmlFor="logo-upload">
                        <Button type="button" variant="outline" className="w-full" asChild>
                          <span>
                            <Upload className="mr-2 h-4 w-4" />
                            Choose Logo File
                          </span>
                        </Button>
                      </label>
                      <p className="text-xs text-gray-500">
                        Recommended: PNG, SVG, or JPEG. Max 5MB.
                      </p>
                    </div>
                  </div>

                  {/* Favicon Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Favicon
                    </label>
                    <div className="space-y-4">
                      {faviconPreview ? (
                        <div className="relative">
                          <img
                            src={faviconPreview}
                            alt="Favicon preview"
                            className="w-16 h-16 object-contain border border-gray-200 rounded-lg bg-gray-50 mx-auto"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-0 right-0 h-6 w-6"
                            onClick={() => removeFile('favicon')}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            Upload favicon
                          </p>
                        </div>
                      )}
                      
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e, 'favicon')}
                        className="hidden"
                        id="favicon-upload"
                      />
                      <label htmlFor="favicon-upload">
                        <Button type="button" variant="outline" className="w-full" asChild>
                          <span>
                            <Upload className="mr-2 h-4 w-4" />
                            Choose Favicon
                          </span>
                        </Button>
                      </label>
                      <p className="text-xs text-gray-500">
                        Recommended: 32x32px PNG or ICO file.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Institute Information */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Institute Information
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    name="instituteName"
                    label="Institute Name"
                    placeholder="Enter institute name"
                    required
                  />

                  <FormField
                    name="website"
                    label="Website URL"
                    type="url"
                    placeholder="https://example.edu"
                  />
                </div>

                <div className="mt-6">
                  <FormField
                    name="tagline"
                    label="Tagline"
                    placeholder="Enter a brief tagline or motto"
                    description="A short phrase that represents your institute's mission"
                  />
                </div>
              </div>

              {/* Color Scheme */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  <Palette className="inline mr-2 h-5 w-5" />
                  Color Scheme
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <FormField
                      name="primaryColor"
                      label="Primary Color"
                      type="color"
                      required
                    />
                    <div 
                      className="mt-2 w-full h-12 rounded-md border border-gray-200"
                      style={{ backgroundColor: values.primaryColor }}
                    />
                  </div>

                  <div>
                    <FormField
                      name="secondaryColor"
                      label="Secondary Color"
                      type="color"
                      required
                    />
                    <div 
                      className="mt-2 w-full h-12 rounded-md border border-gray-200"
                      style={{ backgroundColor: values.secondaryColor }}
                    />
                  </div>

                  <div>
                    <FormField
                      name="accentColor"
                      label="Accent Color"
                      type="color"
                      required
                    />
                    <div 
                      className="mt-2 w-full h-12 rounded-md border border-gray-200"
                      style={{ backgroundColor: values.accentColor }}
                    />
                  </div>
                </div>

                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Color Preview</h3>
                  <div className="flex space-x-2">
                    <div 
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: values.primaryColor }}
                      title="Primary"
                    />
                    <div 
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: values.secondaryColor }}
                      title="Secondary"
                    />
                    <div 
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: values.accentColor }}
                      title="Accent"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3">
                <Button type="button" variant="outline">
                  Cancel
                </Button>
                <Button type="submit" loading={isSubmitting}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      )}
    </div>
  )
}
