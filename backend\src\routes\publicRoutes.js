const express = require('express');
const domainService = require('../services/domainService');
const brandingService = require('../services/brandingService');
const { tenantMiddleware } = require('../middleware/tenantMiddleware');

const router = express.Router();

// Apply tenant identification to all public routes
router.use(tenantMiddleware);

/**
 * @route GET /
 * @desc Serve institute landing page based on domain
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    // Get domain from request
    const domain = req.get('host') || req.get('x-forwarded-host');
    
    if (!domain) {
      return res.status(400).send('Domain not specified');
    }

    // Check if this is a custom domain
    const domainRecord = await domainService.getDomainByName(domain);
    
    if (!domainRecord) {
      // Not a custom domain, serve default platform landing page
      return res.send(await generateDefaultLandingPage(domain));
    }

    // Check if domain is active and verified
    if (!domainRecord.is_active || !domainRecord.is_verified) {
      return res.status(503).send(await generateMaintenancePage(domainRecord));
    }

    // Generate institute-specific landing page
    const landingPage = await brandingService.generateLandingPage(
      domainRecord.institute_id,
      domain
    );

    // Set appropriate headers
    res.set({
      'Content-Type': 'text/html; charset=utf-8',
      'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
      'X-Institute-ID': domainRecord.institute_id,
      'X-Domain-Verified': domainRecord.is_verified
    });

    res.send(landingPage.html);

  } catch (error) {
    console.error('Landing page error:', error.message);
    res.status(500).send(await generateErrorPage(error.message));
  }
});

/**
 * @route GET /health
 * @desc Health check for custom domains
 * @access Public
 */
router.get('/health', async (req, res) => {
  try {
    const domain = req.get('host') || req.get('x-forwarded-host');
    const domainRecord = await domainService.getDomainByName(domain);

    res.json({
      status: 'healthy',
      domain,
      verified: domainRecord?.is_verified || false,
      active: domainRecord?.is_active || false,
      ssl_status: domainRecord?.ssl_status || 'unknown',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @route GET /robots.txt
 * @desc Serve robots.txt for SEO
 * @access Public
 */
router.get('/robots.txt', async (req, res) => {
  try {
    const domain = req.get('host') || req.get('x-forwarded-host');
    const domainRecord = await domainService.getDomainByName(domain);

    let robotsTxt = `User-agent: *\n`;
    
    if (domainRecord && domainRecord.is_active) {
      robotsTxt += `Allow: /\n`;
      robotsTxt += `Sitemap: https://${domain}/sitemap.xml\n`;
    } else {
      robotsTxt += `Disallow: /\n`;
    }

    res.set('Content-Type', 'text/plain');
    res.send(robotsTxt);
  } catch (error) {
    res.set('Content-Type', 'text/plain');
    res.send('User-agent: *\nDisallow: /\n');
  }
});

/**
 * @route GET /sitemap.xml
 * @desc Serve sitemap for SEO
 * @access Public
 */
router.get('/sitemap.xml', async (req, res) => {
  try {
    const domain = req.get('host') || req.get('x-forwarded-host');
    const domainRecord = await domainService.getDomainByName(domain);

    if (!domainRecord || !domainRecord.is_active) {
      return res.status(404).send('Sitemap not available');
    }

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://${domain}/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://${domain}/login</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`;

    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
  } catch (error) {
    res.status(500).send('Error generating sitemap');
  }
});

/**
 * Generate default platform landing page
 */
async function generateDefaultLandingPage(domain) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LMS SAAS Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .cta-button {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 LMS SAAS Platform</h1>
        <p>Welcome to our Learning Management System platform. Create your institute and start teaching today!</p>
        <a href="/register" class="cta-button">Get Started</a>
    </div>
</body>
</html>`;
}

/**
 * Generate maintenance page for unverified domains
 */
async function generateMaintenancePage(domainRecord) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Setup in Progress</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #ffc107;
            margin-bottom: 1rem;
        }
        .status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚧 Domain Setup in Progress</h1>
        <p>This domain is currently being configured. Please check back soon!</p>
        
        <div class="status">
            <strong>Status:</strong><br>
            Domain Verified: ${domainRecord.is_verified ? '✅' : '⏳'}<br>
            SSL Certificate: ${domainRecord.ssl_status === 'issued' ? '✅' : '⏳'}<br>
            Active: ${domainRecord.is_active ? '✅' : '⏳'}
        </div>
        
        <p><small>If you're the domain owner, please complete the verification process in your admin panel.</small></p>
    </div>
</body>
</html>`;
}

/**
 * Generate error page
 */
async function generateErrorPage(errorMessage) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Temporarily Unavailable</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        h1 {
            color: #dc3545;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Service Temporarily Unavailable</h1>
        <p>We're experiencing technical difficulties. Please try again later.</p>
        <p><small>Error: ${errorMessage}</small></p>
    </div>
</body>
</html>`;
}

module.exports = router;
