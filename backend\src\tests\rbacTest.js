const { RBACService, PERMISSIONS, ROLE_PERMISSIONS } = require('../services/rbacService');
const authService = require('../services/authService');
const { query } = require('../database/connection');

/**
 * Role-Based Access Control (RBAC) Test Suite
 * Tests permissions, role assignments, and access control enforcement
 */

class RBACTester {
  constructor() {
    this.testResults = [];
    this.testData = {
      testInstitute: null,
      testUsers: {},
      testTokens: {}
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test permission definitions and role mappings
   */
  async testPermissionDefinitions() {
    console.log('\n🔐 Testing Permission Definitions...');

    try {
      // Test that all roles have permissions defined
      const roles = ['super_admin', 'institute_admin', 'teacher', 'student'];
      
      roles.forEach(role => {
        const permissions = RBACService.getRolePermissions(role);
        this.logResult(
          `Role Permissions - ${role}`,
          permissions.length > 0,
          `${permissions.length} permissions defined`
        );
      });

      // Test permission hierarchy (super_admin should have most permissions)
      const superAdminPerms = RBACService.getRolePermissions('super_admin');
      const instituteAdminPerms = RBACService.getRolePermissions('institute_admin');
      const teacherPerms = RBACService.getRolePermissions('teacher');
      const studentPerms = RBACService.getRolePermissions('student');

      this.logResult(
        'Permission Hierarchy',
        superAdminPerms.length > instituteAdminPerms.length &&
        instituteAdminPerms.length > teacherPerms.length &&
        teacherPerms.length > studentPerms.length,
        `Super Admin: ${superAdminPerms.length}, Institute Admin: ${instituteAdminPerms.length}, Teacher: ${teacherPerms.length}, Student: ${studentPerms.length}`
      );

      // Test specific permission checks
      this.logResult(
        'Super Admin Platform Access',
        RBACService.hasPermission('super_admin', PERMISSIONS.PLATFORM_MANAGE),
        'Super admin can manage platform'
      );

      this.logResult(
        'Student Platform Access',
        !RBACService.hasPermission('student', PERMISSIONS.PLATFORM_MANAGE),
        'Student cannot manage platform'
      );

      this.logResult(
        'Teacher Course Creation',
        RBACService.hasPermission('teacher', PERMISSIONS.COURSE_CREATE),
        'Teacher can create courses'
      );

      this.logResult(
        'Student Course Creation',
        !RBACService.hasPermission('student', PERMISSIONS.COURSE_CREATE),
        'Student cannot create courses'
      );

    } catch (error) {
      this.logResult('Permission Definitions', false, error.message);
    }
  }

  /**
   * Test resource permission mapping
   */
  async testResourcePermissions() {
    console.log('\n🌐 Testing Resource Permissions...');

    try {
      // Test resource permission mapping
      const testCases = [
        {
          method: 'GET',
          path: '/api/admin/institutes',
          expectedPermissions: [PERMISSIONS.PLATFORM_VIEW_ALL]
        },
        {
          method: 'POST',
          path: '/api/courses',
          expectedPermissions: [PERMISSIONS.COURSE_CREATE]
        },
        {
          method: 'GET',
          path: '/api/institute/users',
          expectedPermissions: [PERMISSIONS.USER_READ]
        }
      ];

      testCases.forEach(testCase => {
        const requiredPermissions = RBACService.getResourcePermissions(testCase.method, testCase.path);
        const hasExpectedPermissions = testCase.expectedPermissions.every(perm => 
          requiredPermissions.includes(perm)
        );

        this.logResult(
          `Resource Mapping - ${testCase.method} ${testCase.path}`,
          hasExpectedPermissions,
          `Required: ${requiredPermissions.join(', ')}`
        );
      });

      // Test role access to resources
      this.logResult(
        'Super Admin Resource Access',
        RBACService.canAccessResource('super_admin', 'GET', '/api/admin/institutes'),
        'Super admin can access platform resources'
      );

      this.logResult(
        'Student Resource Access',
        !RBACService.canAccessResource('student', 'GET', '/api/admin/institutes'),
        'Student cannot access platform resources'
      );

      this.logResult(
        'Teacher Course Access',
        RBACService.canAccessResource('teacher', 'POST', '/api/courses'),
        'Teacher can access course creation'
      );

    } catch (error) {
      this.logResult('Resource Permissions', false, error.message);
    }
  }

  /**
   * Test user role assignment and database operations
   */
  async testUserRoleAssignment() {
    console.log('\n👥 Testing User Role Assignment...');

    try {
      // Create test institute and users
      await this.createTestData();

      if (!this.testData.testInstitute || !this.testData.testUsers.teacher) {
        this.logResult('User Role Assignment', false, 'Test data creation failed');
        return;
      }

      const teacherId = this.testData.testUsers.teacher.id;
      const instituteId = this.testData.testInstitute.id;

      // Test role assignment
      const roleAssignment = await RBACService.assignUserRole(
        teacherId,
        instituteId,
        'teacher',
        [PERMISSIONS.COURSE_PUBLISH] // Custom permission
      );

      this.logResult(
        'Role Assignment',
        roleAssignment && roleAssignment.role_name === 'teacher',
        `Assigned teacher role to user ${teacherId}`
      );

      // Test permission retrieval
      const userPermissions = await RBACService.getUserPermissions(teacherId, instituteId);
      
      this.logResult(
        'User Permission Retrieval',
        userPermissions.length > 0,
        `Retrieved ${userPermissions.length} permissions`
      );

      // Test specific permission check
      const hasCreatePermission = await RBACService.userHasPermission(
        teacherId,
        PERMISSIONS.COURSE_CREATE,
        instituteId
      );

      this.logResult(
        'User Permission Check',
        hasCreatePermission === true,
        'Teacher has course creation permission'
      );

      // Test custom permission
      const hasCustomPermission = await RBACService.userHasPermission(
        teacherId,
        PERMISSIONS.COURSE_PUBLISH,
        instituteId
      );

      this.logResult(
        'Custom Permission Check',
        hasCustomPermission === true,
        'Teacher has custom course publish permission'
      );

      // Test resource access
      const canAccessResource = await RBACService.userCanAccessResource(
        teacherId,
        'POST',
        '/api/courses',
        instituteId
      );

      this.logResult(
        'User Resource Access',
        canAccessResource === true,
        'Teacher can access course creation endpoint'
      );

    } catch (error) {
      this.logResult('User Role Assignment', false, error.message);
    }
  }

  /**
   * Test cross-tenant permission isolation
   */
  async testCrossTenantIsolation() {
    console.log('\n🏢 Testing Cross-Tenant Isolation...');

    try {
      if (!this.testData.testUsers.teacher) {
        this.logResult('Cross-Tenant Isolation', false, 'No test user available');
        return;
      }

      const teacherId = this.testData.testUsers.teacher.id;
      const instituteId = this.testData.testInstitute.id;

      // Create fake institute ID for cross-tenant test
      const fakeInstituteId = '00000000-0000-0000-0000-000000000000';

      // Test that user has no permissions in different institute
      const crossTenantPermissions = await RBACService.getUserPermissions(teacherId, fakeInstituteId);

      this.logResult(
        'Cross-Tenant Permission Isolation',
        crossTenantPermissions.length === 0,
        `User has ${crossTenantPermissions.length} permissions in different institute`
      );

      // Test that user cannot access resources in different institute
      const crossTenantAccess = await RBACService.userCanAccessResource(
        teacherId,
        'POST',
        '/api/courses',
        fakeInstituteId
      );

      this.logResult(
        'Cross-Tenant Resource Access',
        crossTenantAccess === false,
        'User cannot access resources in different institute'
      );

    } catch (error) {
      this.logResult('Cross-Tenant Isolation', false, error.message);
    }
  }

  /**
   * Test role hierarchy and permission inheritance
   */
  async testRoleHierarchy() {
    console.log('\n📊 Testing Role Hierarchy...');

    try {
      // Test that higher roles have more permissions than lower roles
      const roles = [
        { name: 'student', level: 1 },
        { name: 'teacher', level: 2 },
        { name: 'institute_admin', level: 3 },
        { name: 'super_admin', level: 4 }
      ];

      for (let i = 0; i < roles.length - 1; i++) {
        const lowerRole = roles[i];
        const higherRole = roles[i + 1];

        const lowerPermissions = RBACService.getRolePermissions(lowerRole.name);
        const higherPermissions = RBACService.getRolePermissions(higherRole.name);

        this.logResult(
          `Role Hierarchy - ${lowerRole.name} vs ${higherRole.name}`,
          higherPermissions.length >= lowerPermissions.length,
          `${lowerRole.name}: ${lowerPermissions.length}, ${higherRole.name}: ${higherPermissions.length}`
        );
      }

      // Test specific hierarchical permissions
      const studentCanManageUsers = RBACService.hasPermission('student', PERMISSIONS.USER_MANAGE_ROLES);
      const adminCanManageUsers = RBACService.hasPermission('institute_admin', PERMISSIONS.USER_MANAGE_ROLES);

      this.logResult(
        'Hierarchical Permission - User Management',
        !studentCanManageUsers && adminCanManageUsers,
        'Only admins can manage user roles'
      );

    } catch (error) {
      this.logResult('Role Hierarchy', false, error.message);
    }
  }

  /**
   * Create test data for RBAC testing
   */
  async createTestData() {
    try {
      // Create test institute
      const instituteData = {
        email: `test${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Admin',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `institute${Date.now()}@example.com`,
        instituteAddress: '123 Test Street'
      };

      const instituteResult = await authService.registerInstituteAdmin(instituteData);
      this.testData.testInstitute = instituteResult.institute;
      this.testData.testUsers.admin = instituteResult.user;

      // Create test teacher
      const teacherData = {
        email: `teacher${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Teacher',
        instituteId: this.testData.testInstitute.id
      };

      const teacherResult = await authService.registerUser(teacherData, 'teacher');
      this.testData.testUsers.teacher = teacherResult.user;

      // Create test student
      const studentData = {
        email: `student${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Student',
        instituteId: this.testData.testInstitute.id
      };

      const studentResult = await authService.registerUser(studentData, 'student');
      this.testData.testUsers.student = studentResult.user;

      console.log('✅ Test data created successfully');

    } catch (error) {
      console.error('❌ Test data creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test users
      for (const [role, user] of Object.entries(this.testData.testUsers)) {
        if (user?.id) {
          await query('DELETE FROM user_roles WHERE user_id = $1', [user.id]);
          await query('DELETE FROM email_verifications WHERE user_id = $1', [user.id]);
          await query('DELETE FROM user_sessions WHERE user_id = $1', [user.id]);
          await query('DELETE FROM users WHERE id = $1', [user.id]);
        }
      }

      // Delete test institute
      if (this.testData.testInstitute?.id) {
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.testInstitute.id]);
      }

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all RBAC tests
   */
  async runAllTests() {
    console.log('🚀 Starting RBAC System Tests...\n');

    await this.testPermissionDefinitions();
    await this.testResourcePermissions();
    await this.testUserRoleAssignment();
    await this.testCrossTenantIsolation();
    await this.testRoleHierarchy();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new RBACTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 RBAC testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { RBACTester };
