'use client'

import React, { useState, useEffect } from 'react'
import { 
  Activity,
  Server,
  Database,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Globe,
  Shield,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SystemMetric {
  name: string
  value: string | number
  status: 'healthy' | 'warning' | 'critical'
  change?: string
  unit?: string
}

interface ServiceStatus {
  name: string
  status: 'online' | 'offline' | 'degraded'
  uptime: string
  responseTime: number
  lastCheck: string
}

export default function SuperAdminSystemPage() {
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Mock system metrics
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([
    { name: 'CPU Usage', value: 45, status: 'healthy', change: '+2%', unit: '%' },
    { name: 'Memory Usage', value: 67, status: 'warning', change: '+5%', unit: '%' },
    { name: 'Disk Usage', value: 23, status: 'healthy', change: '+1%', unit: '%' },
    { name: 'Network I/O', value: 1247, status: 'healthy', change: '+12%', unit: 'MB/s' },
    { name: 'Active Connections', value: 1247, status: 'healthy', change: '+156' },
    { name: 'API Response Time', value: 145, status: 'healthy', change: '-12ms', unit: 'ms' },
    { name: 'Error Rate', value: 0.02, status: 'healthy', change: '-0.01%', unit: '%' },
    { name: 'Uptime', value: '99.9', status: 'healthy', change: '+0.1%', unit: '%' },
  ])

  const [services, setServices] = useState<ServiceStatus[]>([
    {
      name: 'Web Server',
      status: 'online',
      uptime: '99.9%',
      responseTime: 145,
      lastCheck: new Date().toISOString(),
    },
    {
      name: 'Database',
      status: 'online',
      uptime: '99.8%',
      responseTime: 23,
      lastCheck: new Date().toISOString(),
    },
    {
      name: 'Redis Cache',
      status: 'online',
      uptime: '99.9%',
      responseTime: 5,
      lastCheck: new Date().toISOString(),
    },
    {
      name: 'Email Service',
      status: 'degraded',
      uptime: '98.5%',
      responseTime: 2340,
      lastCheck: new Date().toISOString(),
    },
    {
      name: 'File Storage',
      status: 'online',
      uptime: '99.7%',
      responseTime: 89,
      lastCheck: new Date().toISOString(),
    },
    {
      name: 'CDN',
      status: 'online',
      uptime: '99.9%',
      responseTime: 67,
      lastCheck: new Date().toISOString(),
    },
  ])

  const getStatusIcon = (status: 'healthy' | 'warning' | 'critical' | 'online' | 'offline' | 'degraded') => {
    switch (status) {
      case 'healthy':
      case 'online':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'critical':
      case 'offline':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: 'healthy' | 'warning' | 'critical' | 'online' | 'offline' | 'degraded') => {
    switch (status) {
      case 'healthy':
      case 'online':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'warning':
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'critical':
      case 'offline':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getMetricIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'cpu usage':
        return <Cpu className="w-5 h-5" />
      case 'memory usage':
        return <MemoryStick className="w-5 h-5" />
      case 'disk usage':
        return <HardDrive className="w-5 h-5" />
      case 'network i/o':
        return <Wifi className="w-5 h-5" />
      case 'active connections':
        return <Globe className="w-5 h-5" />
      case 'api response time':
        return <Zap className="w-5 h-5" />
      case 'error rate':
        return <Shield className="w-5 h-5" />
      case 'uptime':
        return <Activity className="w-5 h-5" />
      default:
        return <Server className="w-5 h-5" />
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLastUpdated(new Date())
    setIsRefreshing(false)
  }

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date())
    }, 30000)
    return () => clearInterval(interval)
  }, [])

  const healthyMetrics = systemMetrics.filter(m => m.status === 'healthy').length
  const warningMetrics = systemMetrics.filter(m => m.status === 'warning').length
  const criticalMetrics = systemMetrics.filter(m => m.status === 'critical').length

  const onlineServices = services.filter(s => s.status === 'online').length
  const degradedServices = services.filter(s => s.status === 'degraded').length
  const offlineServices = services.filter(s => s.status === 'offline').length

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Health & Monitoring</h1>
          <p className="text-gray-600 mt-1">
            Real-time platform performance and system status
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overall Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">System Status</p>
              <p className="text-2xl font-bold text-green-600">Operational</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Platform Uptime</p>
              <p className="text-2xl font-bold text-blue-600">99.9%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Server className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Services Online</p>
              <p className="text-2xl font-bold text-purple-600">{onlineServices}/{services.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Metrics */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">System Metrics</h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span>Healthy ({healthyMetrics})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span>Warning ({warningMetrics})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span>Critical ({criticalMetrics})</span>
              </div>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {systemMetrics.map((metric, index) => (
              <div key={index} className={`p-4 rounded-lg border ${getStatusColor(metric.status)}`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getMetricIcon(metric.name)}
                    <span className="text-sm font-medium">{metric.name}</span>
                  </div>
                  {getStatusIcon(metric.status)}
                </div>
                <div className="text-2xl font-bold mb-1">
                  {metric.value}{metric.unit}
                </div>
                {metric.change && (
                  <div className="text-xs text-gray-600">
                    {metric.change} from last hour
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Service Status */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Service Status</h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span>Online ({onlineServices})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span>Degraded ({degradedServices})</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span>Offline ({offlineServices})</span>
              </div>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">{service.name}</h4>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {getStatusIcon(service.status)}
                    <span className="ml-1 capitalize">{service.status}</span>
                  </span>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Uptime:</span>
                    <span className="font-medium">{service.uptime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Response Time:</span>
                    <span className="font-medium">{service.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Check:</span>
                    <span className="font-medium">{new Date(service.lastCheck).toLocaleTimeString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Trends */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Trends (Last 24 Hours)</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Response Time Trend</h4>
            <div className="space-y-2">
              {[
                { time: '00:00', value: 142 },
                { time: '04:00', value: 138 },
                { time: '08:00', value: 156 },
                { time: '12:00', value: 167 },
                { time: '16:00', value: 145 },
                { time: '20:00', value: 139 },
              ].map((point, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{point.time}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${(point.value / 200) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-12">{point.value}ms</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Error Rate Trend</h4>
            <div className="space-y-2">
              {[
                { time: '00:00', value: 0.01 },
                { time: '04:00', value: 0.02 },
                { time: '08:00', value: 0.03 },
                { time: '12:00', value: 0.02 },
                { time: '16:00', value: 0.01 },
                { time: '20:00', value: 0.02 },
              ].map((point, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{point.time}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full" 
                        style={{ width: `${(point.value / 0.05) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-12">{point.value}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
