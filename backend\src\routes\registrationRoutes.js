const express = require('express');
const authService = require('../services/authService');
const profileService = require('../services/profileService');
const { responseUtils } = require('../utils/auth');
const { 
  registrationSchemas, 
  createValidationMiddleware,
  sanitizeInput
} = require('../validation/authValidation');
const { 
  createAuthRateLimit, 
  logAuthEvent
} = require('../middleware/authMiddleware');
const { requireTenant } = require('../middleware/tenantMiddleware');
const { query } = require('../database/connection');
const yup = require('yup');

const router = express.Router();

// Apply sanitization to all routes
router.use(sanitizeInput);

// Rate limiting for registration endpoints
const registrationRateLimit = createAuthRateLimit(60 * 60 * 1000, 3); // 3 attempts per hour

/**
 * Enhanced validation schemas for institute-specific registration
 */
const enhancedStudentRegistrationSchema = yup.object({
  email: yup
    .string()
    .email('Please provide a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .lowercase()
    .trim()
    .required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),
  firstName: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('First name is required'),
  lastName: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('Last name is required'),
  phone: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
    .max(20, 'Phone number must be less than 20 characters')
    .trim()
    .nullable(),
  studentId: yup
    .string()
    .min(3, 'Student ID must be at least 3 characters')
    .max(50, 'Student ID must be less than 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/, 'Student ID can only contain letters, numbers, hyphens, and underscores')
    .trim()
    .nullable(),
  major: yup
    .string()
    .max(100, 'Major must be less than 100 characters')
    .trim()
    .nullable(),
  graduationYear: yup
    .number()
    .integer('Graduation year must be an integer')
    .min(2020, 'Graduation year must be 2020 or later')
    .max(2050, 'Graduation year must be 2050 or earlier')
    .nullable(),
  agreeToTerms: yup
    .boolean()
    .oneOf([true], 'You must agree to the terms and conditions')
    .required('Agreement to terms is required')
});

const enhancedTeacherRegistrationSchema = yup.object({
  email: yup
    .string()
    .email('Please provide a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .lowercase()
    .trim()
    .required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),
  firstName: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('First name is required'),
  lastName: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('Last name is required'),
  phone: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
    .max(20, 'Phone number must be less than 20 characters')
    .trim()
    .nullable(),
  employeeId: yup
    .string()
    .min(3, 'Employee ID must be at least 3 characters')
    .max(50, 'Employee ID must be less than 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/, 'Employee ID can only contain letters, numbers, hyphens, and underscores')
    .trim()
    .nullable(),
  department: yup
    .string()
    .max(100, 'Department must be less than 100 characters')
    .trim()
    .nullable(),
  specialization: yup
    .string()
    .max(255, 'Specialization must be less than 255 characters')
    .trim()
    .nullable(),
  invitationToken: yup
    .string()
    .required('Invitation token is required for teacher registration')
    .trim(),
  agreeToTerms: yup
    .boolean()
    .oneOf([true], 'You must agree to the terms and conditions')
    .required('Agreement to terms is required')
});

/**
 * Helper function to validate email domain against institute
 */
async function validateInstituteEmailDomain(email, instituteId) {
  try {
    const emailDomain = email.split('@')[1].toLowerCase();
    
    // Get institute information
    const instituteResult = await query(
      'SELECT email, allowed_domains FROM institutes WHERE id = $1',
      [instituteId]
    );
    
    if (instituteResult.rows.length === 0) {
      return false;
    }
    
    const institute = instituteResult.rows[0];
    const instituteEmailDomain = institute.email.split('@')[1].toLowerCase();
    
    // Check if email domain matches institute domain
    if (emailDomain === instituteEmailDomain) {
      return true;
    }
    
    // Check against allowed domains (if configured)
    if (institute.allowed_domains) {
      try {
        const allowedDomains = JSON.parse(institute.allowed_domains);
        return allowedDomains.some(domain => domain.toLowerCase() === emailDomain);
      } catch (error) {
        console.warn('Failed to parse allowed domains:', error.message);
      }
    }
    
    // Check custom domains
    const customDomainsResult = await query(
      'SELECT domain FROM institute_domains WHERE institute_id = $1 AND is_active = TRUE',
      [instituteId]
    );
    
    const customDomains = customDomainsResult.rows.map(row => row.domain.toLowerCase());
    return customDomains.includes(emailDomain);
    
  } catch (error) {
    console.error('Email domain validation error:', error.message);
    return false;
  }
}

/**
 * @route POST /api/register/student
 * @desc Enhanced student registration with profile creation
 * @access Public (with tenant context)
 */
router.post('/student',
  requireTenant,
  registrationRateLimit,
  logAuthEvent('enhanced_student_registration'),
  createValidationMiddleware(enhancedStudentRegistrationSchema),
  async (req, res) => {
    try {
      const { 
        email, password, firstName, lastName, phone, 
        studentId, major, graduationYear, agreeToTerms 
      } = req.body;
      
      // Validate email domain against institute
      const isValidDomain = await validateInstituteEmailDomain(email, req.tenant.instituteId);
      if (!isValidDomain) {
        return res.status(400).json(
          responseUtils.createErrorResponse(
            'Email domain not allowed for this institute. Please use your institute email address.',
            'INVALID_EMAIL_DOMAIN',
            { 
              providedEmail: email,
              instituteName: req.tenant.instituteName
            }
          )
        );
      }
      
      // Register user
      const userResult = await authService.registerUser({
        email,
        password,
        firstName,
        lastName,
        phone,
        instituteId: req.tenant.instituteId
      }, 'student');

      // Create student profile if additional data provided
      if (studentId || major || graduationYear) {
        try {
          await profileService.updateUserProfile(
            userResult.user.id,
            {
              studentProfile: {
                studentId,
                major,
                graduationYear,
                academicStatus: 'active'
              }
            },
            userResult.user.id,
            'student'
          );
        } catch (profileError) {
          console.warn('Failed to create student profile:', profileError.message);
          // Continue with registration even if profile creation fails
        }
      }

      res.status(201).json({
        success: true,
        message: 'Student registration successful. Please check your email to verify your account.',
        user: {
          id: userResult.user.id,
          email: userResult.user.email,
          firstName: userResult.user.first_name,
          lastName: userResult.user.last_name,
          role: userResult.user.role,
          instituteId: userResult.user.institute_id
        },
        requiresVerification: userResult.requiresVerification,
        nextSteps: [
          'Check your email for verification link',
          'Complete email verification to access your account',
          'Login to complete your profile setup'
        ]
      });

    } catch (error) {
      console.error('Enhanced student registration error:', error.message);
      
      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'USER_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Registration failed', 'REGISTRATION_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/register/teacher
 * @desc Enhanced teacher registration with invitation validation
 * @access Public (with tenant context and invitation)
 */
router.post('/teacher',
  requireTenant,
  registrationRateLimit,
  logAuthEvent('enhanced_teacher_registration'),
  createValidationMiddleware(enhancedTeacherRegistrationSchema),
  async (req, res) => {
    try {
      const { 
        email, password, firstName, lastName, phone, 
        employeeId, department, specialization, invitationToken, agreeToTerms 
      } = req.body;
      
      // Validate invitation token
      const invitationResult = await query(
        'SELECT * FROM teacher_invitations WHERE token = $1 AND institute_id = $2 AND is_used = FALSE AND expires_at > CURRENT_TIMESTAMP',
        [invitationToken, req.tenant.instituteId]
      );

      if (invitationResult.rows.length === 0) {
        return res.status(400).json(
          responseUtils.createErrorResponse(
            'Invalid or expired invitation token',
            'INVALID_INVITATION'
          )
        );
      }

      const invitation = invitationResult.rows[0];

      // Check if invitation email matches provided email
      if (invitation.email.toLowerCase() !== email.toLowerCase()) {
        return res.status(400).json(
          responseUtils.createErrorResponse(
            'Email address does not match invitation',
            'EMAIL_MISMATCH'
          )
        );
      }
      
      // Register user
      const userResult = await authService.registerUser({
        email,
        password,
        firstName,
        lastName,
        phone,
        instituteId: req.tenant.instituteId
      }, 'teacher');

      // Create teacher profile
      try {
        await profileService.updateUserProfile(
          userResult.user.id,
          {
            teacherProfile: {
              employeeId,
              department,
              specialization
            }
          },
          userResult.user.id,
          'teacher'
        );
      } catch (profileError) {
        console.warn('Failed to create teacher profile:', profileError.message);
      }

      // Mark invitation as used
      await query(
        'UPDATE teacher_invitations SET is_used = TRUE, used_at = CURRENT_TIMESTAMP, used_by = $1 WHERE id = $2',
        [userResult.user.id, invitation.id]
      );

      res.status(201).json({
        success: true,
        message: 'Teacher registration successful. Please check your email to verify your account.',
        user: {
          id: userResult.user.id,
          email: userResult.user.email,
          firstName: userResult.user.first_name,
          lastName: userResult.user.last_name,
          role: userResult.user.role,
          instituteId: userResult.user.institute_id
        },
        requiresVerification: userResult.requiresVerification,
        nextSteps: [
          'Check your email for verification link',
          'Complete email verification to access your account',
          'Login to complete your profile setup'
        ]
      });

    } catch (error) {
      console.error('Enhanced teacher registration error:', error.message);
      
      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'USER_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Registration failed', 'REGISTRATION_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/register/institute-info
 * @desc Get institute information for registration page
 * @access Public (with tenant context)
 */
router.get('/institute-info',
  requireTenant,
  async (req, res) => {
    try {
      const instituteResult = await query(
        'SELECT name, email, website, address FROM institutes WHERE id = $1',
        [req.tenant.instituteId]
      );

      if (instituteResult.rows.length === 0) {
        return res.status(404).json(
          responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
        );
      }

      const institute = instituteResult.rows[0];

      // Get allowed domains
      const allowedDomains = await getInstituteAllowedDomains(req.tenant.instituteId);

      res.json({
        success: true,
        institute: {
          name: institute.name,
          email: institute.email,
          website: institute.website,
          address: institute.address,
          allowedDomains
        }
      });

    } catch (error) {
      console.error('Get institute info error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get institute information', 'INSTITUTE_INFO_ERROR')
      );
    }
  }
);

/**
 * Helper function to get allowed domains for an institute
 */
async function getInstituteAllowedDomains(instituteId) {
  try {
    const instituteResult = await query(
      'SELECT email, allowed_domains FROM institutes WHERE id = $1',
      [instituteId]
    );
    
    if (instituteResult.rows.length === 0) {
      return [];
    }
    
    const institute = instituteResult.rows[0];
    const domains = [institute.email.split('@')[1].toLowerCase()];
    
    // Add configured allowed domains
    if (institute.allowed_domains) {
      try {
        const allowedDomains = JSON.parse(institute.allowed_domains);
        domains.push(...allowedDomains.map(d => d.toLowerCase()));
      } catch (error) {
        console.warn('Failed to parse allowed domains:', error.message);
      }
    }
    
    // Add custom domains
    const customDomainsResult = await query(
      'SELECT domain FROM institute_domains WHERE institute_id = $1 AND is_active = TRUE',
      [instituteId]
    );
    
    domains.push(...customDomainsResult.rows.map(row => row.domain.toLowerCase()));
    
    return [...new Set(domains)]; // Remove duplicates
    
  } catch (error) {
    console.error('Get allowed domains error:', error.message);
    return [];
  }
}

module.exports = router;
