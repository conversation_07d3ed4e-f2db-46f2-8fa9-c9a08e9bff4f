'use client'

import React, { useState, useEffect } from 'react'
import { 
  Shield,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Lock,
  Key,
  Users,
  Activity,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SecurityCheck {
  id: string
  name: string
  description: string
  status: 'pass' | 'warning' | 'fail'
  lastChecked: string
  details?: string
}

interface SecurityAuditData {
  overallScore: number
  lastAudit: string
  checks: SecurityCheck[]
  recommendations: string[]
}

export default function SecurityAuditPanel() {
  const [auditData, setAuditData] = useState<SecurityAuditData | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const mockAuditData: SecurityAuditData = {
    overallScore: 95,
    lastAudit: new Date().toISOString(),
    checks: [
      {
        id: 'auth-jwt',
        name: 'JWT Authentication',
        description: 'JWT token validation and security',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'Strong JWT implementation with proper expiration'
      },
      {
        id: 'rbac-protection',
        name: 'RBAC Protection',
        description: 'Role-based access control enforcement',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'All endpoints properly protected with role validation'
      },
      {
        id: 'input-validation',
        name: 'Input Validation',
        description: 'Server-side input validation and sanitization',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'Express-validator implemented across all endpoints'
      },
      {
        id: 'xss-protection',
        name: 'XSS Protection',
        description: 'Cross-site scripting prevention',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'React built-in XSS protection + input sanitization'
      },
      {
        id: 'csrf-protection',
        name: 'CSRF Protection',
        description: 'Cross-site request forgery prevention',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'JWT-based stateless authentication prevents CSRF'
      },
      {
        id: 'sql-injection',
        name: 'SQL Injection Prevention',
        description: 'Database query security',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'Parameterized queries and ORM protection'
      },
      {
        id: 'rate-limiting',
        name: 'Rate Limiting',
        description: 'API endpoint rate limiting',
        status: 'warning',
        lastChecked: new Date().toISOString(),
        details: 'Consider implementing rate limiting for production'
      },
      {
        id: 'https-enforcement',
        name: 'HTTPS Enforcement',
        description: 'Secure transport layer',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'HTTPS enforced in production environment'
      },
      {
        id: 'session-security',
        name: 'Session Security',
        description: 'Session management and timeout',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'Secure session handling with proper timeout'
      },
      {
        id: 'error-handling',
        name: 'Secure Error Handling',
        description: 'Error responses without information leakage',
        status: 'pass',
        lastChecked: new Date().toISOString(),
        details: 'Generic error messages prevent information disclosure'
      }
    ],
    recommendations: [
      'Implement API rate limiting for production deployment',
      'Consider adding IP-based access restrictions for super admin accounts',
      'Set up automated security scanning in CI/CD pipeline',
      'Implement security event logging for audit trails',
      'Consider adding multi-factor authentication for super admins'
    ]
  }

  useEffect(() => {
    setAuditData(mockAuditData)
  }, [])

  const runSecurityAudit = async () => {
    setIsLoading(true)
    try {
      // Simulate security audit API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      setAuditData({
        ...mockAuditData,
        lastAudit: new Date().toISOString()
      })
    } catch (error) {
      console.error('Security audit error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: 'pass' | 'warning' | 'fail') => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'fail':
        return <XCircle className="w-4 h-4 text-red-500" />
    }
  }

  const getStatusColor = (status: 'pass' | 'warning' | 'fail') => {
    switch (status) {
      case 'pass':
        return 'bg-green-50 border-green-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'fail':
        return 'bg-red-50 border-red-200'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (!auditData) {
    return <div className="p-6">Loading security audit...</div>
  }

  const passedChecks = auditData.checks.filter(check => check.status === 'pass').length
  const warningChecks = auditData.checks.filter(check => check.status === 'warning').length
  const failedChecks = auditData.checks.filter(check => check.status === 'fail').length

  return (
    <div className="space-y-6">
      {/* Security Score */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Security Audit</h3>
          <Button 
            onClick={runSecurityAudit}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Run Audit
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="text-center">
            <div className={`text-3xl font-bold ${getScoreColor(auditData.overallScore)}`}>
              {auditData.overallScore}/100
            </div>
            <p className="text-sm text-gray-600">Security Score</p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{passedChecks}</div>
            <p className="text-sm text-gray-600">Passed</p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{warningChecks}</div>
            <p className="text-sm text-gray-600">Warnings</p>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{failedChecks}</div>
            <p className="text-sm text-gray-600">Failed</p>
          </div>
        </div>

        <p className="text-sm text-gray-600">
          Last audit: {new Date(auditData.lastAudit).toLocaleString()}
        </p>
      </div>

      {/* Security Checks */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h4 className="text-md font-semibold text-gray-900">Security Checks</h4>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {auditData.checks.map((check) => (
              <div key={check.id} className={`p-4 rounded-lg border ${getStatusColor(check.status)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(check.status)}
                    <div>
                      <h5 className="font-medium text-gray-900">{check.name}</h5>
                      <p className="text-sm text-gray-600 mt-1">{check.description}</p>
                      {check.details && (
                        <p className="text-xs text-gray-500 mt-2">{check.details}</p>
                      )}
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(check.lastChecked).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h4 className="text-md font-semibold text-gray-900">Security Recommendations</h4>
        </div>
        <div className="p-6">
          <div className="space-y-3">
            {auditData.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-sm text-gray-700">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Security Compliance */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h4 className="text-md font-semibold text-gray-900 mb-4">Compliance Status</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
            <Shield className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-800">OWASP Top 10</p>
            <p className="text-xs text-green-600">Compliant</p>
          </div>
          
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
            <Lock className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-800">GDPR</p>
            <p className="text-xs text-green-600">Compliant</p>
          </div>
          
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
            <Key className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-800">SOC 2</p>
            <p className="text-xs text-green-600">Compliant</p>
          </div>
          
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
            <Activity className="w-6 h-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-800">ISO 27001</p>
            <p className="text-xs text-green-600">Compliant</p>
          </div>
        </div>
      </div>
    </div>
  )
}
