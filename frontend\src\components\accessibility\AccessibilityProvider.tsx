import React, { createContext, useContext, useEffect, useState } from 'react'
import { useReducedMotion, useHighContrast, useColorScheme } from '@/hooks/useAccessibility'

interface AccessibilityContextType {
  // Preferences
  prefersReducedMotion: boolean
  prefersHighContrast: boolean
  colorScheme: 'light' | 'dark'
  fontSize: 'small' | 'medium' | 'large'
  
  // Settings
  announcements: boolean
  keyboardNavigation: boolean
  focusVisible: boolean
  
  // Actions
  setFontSize: (size: 'small' | 'medium' | 'large') => void
  setAnnouncements: (enabled: boolean) => void
  setKeyboardNavigation: (enabled: boolean) => void
  setFocusVisible: (enabled: boolean) => void
  announce: (message: string, priority?: 'polite' | 'assertive') => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export function useAccessibilityContext() {
  const context = useContext(AccessibilityContext)
  if (!context) {
    throw new Error('useAccessibilityContext must be used within AccessibilityProvider')
  }
  return context
}

interface AccessibilityProviderProps {
  children: React.ReactNode
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const prefersReducedMotion = useReducedMotion()
  const prefersHighContrast = useHighContrast()
  const colorScheme = useColorScheme()
  
  const [fontSize, setFontSize] = useState<'small' | 'medium' | 'large'>('medium')
  const [announcements, setAnnouncements] = useState(true)
  const [keyboardNavigation, setKeyboardNavigation] = useState(true)
  const [focusVisible, setFocusVisible] = useState(true)
  const [liveRegion, setLiveRegion] = useState<HTMLDivElement | null>(null)

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('accessibility-settings')
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings)
        setFontSize(settings.fontSize || 'medium')
        setAnnouncements(settings.announcements ?? true)
        setKeyboardNavigation(settings.keyboardNavigation ?? true)
        setFocusVisible(settings.focusVisible ?? true)
      } catch (error) {
        console.error('Failed to load accessibility settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage
  useEffect(() => {
    const settings = {
      fontSize,
      announcements,
      keyboardNavigation,
      focusVisible,
    }
    localStorage.setItem('accessibility-settings', JSON.stringify(settings))
  }, [fontSize, announcements, keyboardNavigation, focusVisible])

  // Apply font size to document
  useEffect(() => {
    const root = document.documentElement
    root.classList.remove('text-sm', 'text-base', 'text-lg')
    
    switch (fontSize) {
      case 'small':
        root.classList.add('text-sm')
        break
      case 'large':
        root.classList.add('text-lg')
        break
      default:
        root.classList.add('text-base')
    }
  }, [fontSize])

  // Apply high contrast mode
  useEffect(() => {
    const root = document.documentElement
    if (prefersHighContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
  }, [prefersHighContrast])

  // Apply reduced motion
  useEffect(() => {
    const root = document.documentElement
    if (prefersReducedMotion) {
      root.classList.add('reduce-motion')
    } else {
      root.classList.remove('reduce-motion')
    }
  }, [prefersReducedMotion])

  // Apply focus visible setting
  useEffect(() => {
    const root = document.documentElement
    if (focusVisible) {
      root.classList.add('focus-visible-enabled')
    } else {
      root.classList.remove('focus-visible-enabled')
    }
  }, [focusVisible])

  // Announce function
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announcements || !liveRegion) return

    liveRegion.setAttribute('aria-live', priority)
    liveRegion.textContent = message

    // Clear after announcement
    setTimeout(() => {
      if (liveRegion) {
        liveRegion.textContent = ''
      }
    }, 1000)
  }

  const value: AccessibilityContextType = {
    prefersReducedMotion,
    prefersHighContrast,
    colorScheme,
    fontSize,
    announcements,
    keyboardNavigation,
    focusVisible,
    setFontSize,
    setAnnouncements,
    setKeyboardNavigation,
    setFocusVisible,
    announce,
  }

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
      
      {/* Live region for announcements */}
      <div
        ref={setLiveRegion}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />
      
      {/* Skip links */}
      <div className="sr-only focus:not-sr-only">
        <a
          href="#main-content"
          className="absolute top-4 left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to main content
        </a>
        <a
          href="#navigation"
          className="absolute top-4 left-32 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to navigation
        </a>
      </div>
    </AccessibilityContext.Provider>
  )
}

// Accessibility settings panel component
export function AccessibilitySettings() {
  const {
    fontSize,
    announcements,
    keyboardNavigation,
    focusVisible,
    prefersReducedMotion,
    prefersHighContrast,
    setFontSize,
    setAnnouncements,
    setKeyboardNavigation,
    setFocusVisible,
  } = useAccessibilityContext()

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Accessibility Settings</h3>
        
        {/* Font Size */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Font Size</label>
          <div className="flex space-x-2">
            {(['small', 'medium', 'large'] as const).map((size) => (
              <button
                key={size}
                onClick={() => setFontSize(size)}
                className={`px-3 py-2 rounded-md text-sm border ${
                  fontSize === size
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'bg-background border-border hover:bg-accent'
                }`}
              >
                {size.charAt(0).toUpperCase() + size.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Toggles */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label htmlFor="announcements" className="text-sm font-medium">
              Screen Reader Announcements
            </label>
            <input
              id="announcements"
              type="checkbox"
              checked={announcements}
              onChange={(e) => setAnnouncements(e.target.checked)}
              className="rounded border-input"
            />
          </div>

          <div className="flex items-center justify-between">
            <label htmlFor="keyboard-nav" className="text-sm font-medium">
              Enhanced Keyboard Navigation
            </label>
            <input
              id="keyboard-nav"
              type="checkbox"
              checked={keyboardNavigation}
              onChange={(e) => setKeyboardNavigation(e.target.checked)}
              className="rounded border-input"
            />
          </div>

          <div className="flex items-center justify-between">
            <label htmlFor="focus-visible" className="text-sm font-medium">
              Visible Focus Indicators
            </label>
            <input
              id="focus-visible"
              type="checkbox"
              checked={focusVisible}
              onChange={(e) => setFocusVisible(e.target.checked)}
              className="rounded border-input"
            />
          </div>
        </div>

        {/* System Preferences (Read-only) */}
        <div className="pt-4 border-t border-border">
          <h4 className="text-sm font-medium mb-2">System Preferences</h4>
          <div className="space-y-2 text-sm text-muted-foreground">
            <div>Reduced Motion: {prefersReducedMotion ? 'Enabled' : 'Disabled'}</div>
            <div>High Contrast: {prefersHighContrast ? 'Enabled' : 'Disabled'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}
