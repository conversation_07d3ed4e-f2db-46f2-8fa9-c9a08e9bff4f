# Frontend Environment Variables Template
# Copy this file to .env.local and update the values

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5010

# Application Configuration
NEXT_PUBLIC_APP_NAME=LMS SAAS
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_VERSION=1.0.0

# Analytics (optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_HOTJAR_ID=

# Feature Flags (optional)
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_CHAT_SUPPORT=false
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# Third-party Services (optional)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=

# Development/Debug
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_SHOW_PERFORMANCE_METRICS=false

# SEO
NEXT_PUBLIC_SITE_DESCRIPTION=Multi-tenant Learning Management System for educational institutions
NEXT_PUBLIC_SITE_KEYWORDS=LMS,Learning Management System,Education,SAAS,Multi-tenant

# Social Media
NEXT_PUBLIC_TWITTER_HANDLE=@lmssaas
NEXT_PUBLIC_FACEBOOK_PAGE=
NEXT_PUBLIC_LINKEDIN_PAGE=

# Contact Information
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NEXT_PUBLIC_SALES_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=******-0123
