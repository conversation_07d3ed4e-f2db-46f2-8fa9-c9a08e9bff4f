const instituteAdminService = require('../services/instituteAdminService');
const authService = require('../services/authService');
const { query } = require('../database/connection');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

/**
 * Institute Admin Panel Test Suite
 * Tests institute admin functionality including branding, settings, and student management
 */

class InstituteAdminTester {
  constructor() {
    this.testResults = [];
    this.baseURL = 'http://localhost:5010';
    this.testData = {
      institute: null,
      admin: null,
      student: null,
      authToken: null
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test institute admin service functionality
   */
  async testInstituteAdminService() {
    console.log('\n🔧 Testing Institute Admin Service...');

    try {
      if (!this.testData.institute) {
        this.logResult('Institute Admin Service', false, 'No test institute available');
        return;
      }

      // Test dashboard statistics
      const stats = await instituteAdminService.getDashboardStats(this.testData.institute.id);
      
      this.logResult(
        'Dashboard Statistics',
        stats && typeof stats.total_users !== 'undefined',
        `Total users: ${stats.total_users}, Students: ${stats.total_students}`
      );

      // Test activity feed
      const activities = await instituteAdminService.getActivityFeed(this.testData.institute.id, 10);
      
      this.logResult(
        'Activity Feed',
        Array.isArray(activities),
        `Retrieved ${activities.length} activity records`
      );

      // Test analytics report generation
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      const endDate = new Date();

      const report = await instituteAdminService.generateAnalyticsReport(
        this.testData.institute.id,
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );

      this.logResult(
        'Analytics Report Generation',
        report && report.period && Array.isArray(report.userGrowth),
        `Generated report for ${report.period.startDate} to ${report.period.endDate}`
      );

      // Test public branding retrieval
      const branding = await instituteAdminService.getPublicBranding(this.testData.institute.id);
      
      this.logResult(
        'Public Branding Retrieval',
        branding && branding.institute,
        `Retrieved branding for ${branding.institute?.name}`
      );

    } catch (error) {
      this.logResult('Institute Admin Service', false, error.message);
    }
  }

  /**
   * Test branding management API
   */
  async testBrandingManagement() {
    console.log('\n🎨 Testing Branding Management...');

    if (!this.testData.authToken) {
      this.logResult('Branding Management', false, 'No auth token available');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${this.testData.authToken}`,
      'Content-Type': 'application/json',
      'X-Tenant-ID': this.testData.institute.name.toLowerCase().replace(/\s+/g, '-')
    };

    try {
      // Test getting current branding
      const getBrandingResponse = await axios.get(`${this.baseURL}/api/institute-admin/branding`, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Get Branding Settings',
        getBrandingResponse.status === 200 && getBrandingResponse.data.success,
        'Retrieved current branding settings'
      );

      // Test updating branding
      const updateBrandingResponse = await axios.put(`${this.baseURL}/api/institute-admin/branding`, {
        primaryColor: '#FF6B6B',
        secondaryColor: '#4ECDC4',
        accentColor: '#45B7D1',
        customCss: '/* Custom CSS for institute */',
        themeSettings: { darkMode: false, fontSize: 'medium' }
      }, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Update Branding Settings',
        updateBrandingResponse.status === 200 && updateBrandingResponse.data.success,
        `Updated branding: ${updateBrandingResponse.data.branding?.primary_color}`
      );

      // Test logo upload (create a simple test image)
      const testImagePath = path.join(__dirname, 'test-logo.png');
      await this.createTestImage(testImagePath);

      const formData = new FormData();
      formData.append('logo', fs.createReadStream(testImagePath));

      const uploadLogoResponse = await axios.post(`${this.baseURL}/api/institute-admin/branding/upload-logo`, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${this.testData.authToken}`,
          'X-Tenant-ID': this.testData.institute.name.toLowerCase().replace(/\s+/g, '-')
        },
        timeout: 10000
      });

      this.logResult(
        'Logo Upload',
        uploadLogoResponse.status === 200 && uploadLogoResponse.data.success,
        `Logo uploaded: ${uploadLogoResponse.data.logoUrl}`
      );

      // Clean up test image
      await fs.promises.unlink(testImagePath).catch(() => {});

    } catch (error) {
      this.logResult('Branding Management', false, error.response?.data?.error || error.message);
    }
  }

  /**
   * Test institute settings management
   */
  async testSettingsManagement() {
    console.log('\n⚙️ Testing Settings Management...');

    if (!this.testData.authToken) {
      this.logResult('Settings Management', false, 'No auth token available');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${this.testData.authToken}`,
      'Content-Type': 'application/json',
      'X-Tenant-ID': this.testData.institute.name.toLowerCase().replace(/\s+/g, '-')
    };

    try {
      // Test getting current settings
      const getSettingsResponse = await axios.get(`${this.baseURL}/api/institute-admin/settings`, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Get Institute Settings',
        getSettingsResponse.status === 200 && getSettingsResponse.data.success,
        `Retrieved settings for ${getSettingsResponse.data.settings?.name}`
      );

      // Test updating settings
      const updateSettingsResponse = await axios.put(`${this.baseURL}/api/institute-admin/settings`, {
        name: this.testData.institute.name + ' Updated',
        website: 'https://updated-institute.edu',
        address: '456 Updated Street, Test City',
        phone: '+1234567890',
        timezone: 'America/New_York',
        language: 'en',
        allowedDomains: ['updated-institute.edu', 'test.edu'],
        registrationSettings: {
          requireApproval: true,
          allowSelfRegistration: true,
          requireEmailVerification: true,
          defaultRole: 'student'
        },
        notificationSettings: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          weeklyDigest: true
        }
      }, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Update Institute Settings',
        updateSettingsResponse.status === 200 && updateSettingsResponse.data.success,
        'Settings updated successfully'
      );

    } catch (error) {
      this.logResult('Settings Management', false, error.response?.data?.error || error.message);
    }
  }

  /**
   * Test student management functionality
   */
  async testStudentManagement() {
    console.log('\n👨‍🎓 Testing Student Management...');

    if (!this.testData.authToken) {
      this.logResult('Student Management', false, 'No auth token available');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${this.testData.authToken}`,
      'Content-Type': 'application/json',
      'X-Tenant-ID': this.testData.institute.name.toLowerCase().replace(/\s+/g, '-')
    };

    try {
      // Test getting students list
      const getStudentsResponse = await axios.get(`${this.baseURL}/api/institute-admin/students`, {
        headers,
        params: { page: 1, limit: 10 },
        timeout: 5000
      });

      this.logResult(
        'Get Students List',
        getStudentsResponse.status === 200 && getStudentsResponse.data.success,
        `Retrieved ${getStudentsResponse.data.students?.length} students`
      );

      // Test registering a new student
      const registerStudentResponse = await axios.post(`${this.baseURL}/api/institute-admin/students`, {
        email: `teststudent${Date.now()}@${this.testData.institute.email.split('@')[1]}`,
        firstName: 'Test',
        lastName: 'Student',
        phone: '+1987654321',
        studentId: 'TS001',
        major: 'Computer Science',
        graduationYear: 2025,
        sendWelcomeEmail: false
      }, {
        headers,
        timeout: 5000
      });

      this.logResult(
        'Register New Student',
        registerStudentResponse.status === 201 && registerStudentResponse.data.success,
        `Registered student: ${registerStudentResponse.data.student?.email}`
      );

      // Store the new student for further tests
      if (registerStudentResponse.data.success) {
        this.testData.newStudent = registerStudentResponse.data.student;
      }

    } catch (error) {
      this.logResult('Student Management', false, error.response?.data?.error || error.message);
    }
  }

  /**
   * Test bulk student operations
   */
  async testBulkStudentOperations() {
    console.log('\n📊 Testing Bulk Student Operations...');

    try {
      if (!this.testData.institute || !this.testData.admin) {
        this.logResult('Bulk Student Operations', false, 'Missing test data');
        return;
      }

      // Test bulk import
      const studentsData = [
        {
          email: `bulk1${Date.now()}@${this.testData.institute.email.split('@')[1]}`,
          firstName: 'Bulk',
          lastName: 'Student1',
          studentId: 'BS001',
          major: 'Mathematics'
        },
        {
          email: `bulk2${Date.now()}@${this.testData.institute.email.split('@')[1]}`,
          firstName: 'Bulk',
          lastName: 'Student2',
          studentId: 'BS002',
          major: 'Physics'
        }
      ];

      const bulkImportResult = await instituteAdminService.bulkImportStudents(
        this.testData.institute.id,
        studentsData,
        this.testData.admin.id
      );

      this.logResult(
        'Bulk Student Import',
        bulkImportResult.successful === 2 && bulkImportResult.failed === 0,
        `Imported ${bulkImportResult.successful} students, ${bulkImportResult.failed} failed`
      );

    } catch (error) {
      this.logResult('Bulk Student Operations', false, error.message);
    }
  }

  /**
   * Create a simple test image for upload testing
   */
  async createTestImage(filePath) {
    // Create a simple 1x1 PNG image (base64 encoded)
    const pngData = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==',
      'base64'
    );
    
    await fs.promises.writeFile(filePath, pngData);
  }

  /**
   * Create test data
   */
  async createTestData() {
    try {
      // Create test institute and admin
      const instituteData = {
        email: `admin${Date.now()}@testinstitute.edu`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Admin',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `contact${Date.now()}@testinstitute.edu`,
        instituteAddress: '123 Test Street'
      };

      const instituteResult = await authService.registerInstituteAdmin(instituteData);
      this.testData.institute = instituteResult.institute;
      this.testData.admin = instituteResult.user;

      // Login to get auth token
      const mockReq = {
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test-Agent' : null,
        tenant: { instituteId: this.testData.institute.id }
      };

      // Verify email and assign permissions
      await query('UPDATE users SET is_email_verified = TRUE WHERE id = $1', [this.testData.admin.id]);

      // Assign institute admin role with permissions
      const { RBACService } = require('../services/rbacService');
      await RBACService.assignUserRole(
        this.testData.admin.id,
        this.testData.institute.id,
        'institute_admin',
        ['institute:read', 'institute:update', 'user:read', 'user:create', 'user:update']
      );

      const loginResult = await authService.loginUser(
        this.testData.admin.email,
        instituteData.password,
        mockReq
      );

      this.testData.authToken = loginResult.accessToken;

      console.log('✅ Test data created successfully');

    } catch (error) {
      console.error('❌ Test data creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test users and institute
      if (this.testData.admin?.id) {
        await query('DELETE FROM users WHERE id = $1', [this.testData.admin.id]);
      }

      if (this.testData.newStudent?.id) {
        await query('DELETE FROM users WHERE id = $1', [this.testData.newStudent.id]);
      }

      if (this.testData.institute?.id) {
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.institute.id]);
      }

      // Clean up bulk imported students
      await query(`
        DELETE FROM users 
        WHERE email LIKE 'bulk%@%' 
        AND created_at >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
      `);

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all institute admin tests
   */
  async runAllTests() {
    console.log('🚀 Starting Institute Admin Panel Tests...\n');

    await this.createTestData();
    await this.testInstituteAdminService();
    await this.testBrandingManagement();
    await this.testSettingsManagement();
    await this.testStudentManagement();
    await this.testBulkStudentOperations();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Institute Admin Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new InstituteAdminTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Institute admin testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { InstituteAdminTester };
