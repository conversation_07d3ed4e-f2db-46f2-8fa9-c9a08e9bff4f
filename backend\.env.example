# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lte_lms
DB_USER=postgres
DB_PASSWORD=1234

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=5010
NODE_ENV=development
HOST=localhost

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Super Admin Configuration
SUPER_ADMIN_MASTER_KEY=your-super-secret-master-key

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_PATH=uploads

# Additional JWT Configuration
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_REFRESH_EXPIRES_IN=30d

# Enhanced Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=LMS SAAS
SMTP_SECURE=false

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SWAGGER=true

# Development/Debug
DEBUG_SQL=false
DEBUG_ROUTES=false
