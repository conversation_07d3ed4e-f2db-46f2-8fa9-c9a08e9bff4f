import { BookO<PERSON> } from 'lucide-react'
import Link from 'next/link'

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="flex min-h-screen">
        {/* Left side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20" />
          <div className="relative z-10 flex flex-col justify-center px-12 text-white">
            <div className="mb-8">
              <div className="flex items-center space-x-3 mb-6">
                <BookOpen className="h-10 w-10" />
                <span className="text-3xl font-bold">LMS SAAS</span>
              </div>
              <h1 className="text-4xl font-bold mb-4">
                Welcome to the Future of Learning
              </h1>
              <p className="text-xl text-blue-100 mb-8">
                Join thousands of educational institutions using our platform to deliver 
                exceptional learning experiences.
              </p>
            </div>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold mb-1">Multi-Tenant Architecture</h3>
                  <p className="text-blue-100 text-sm">
                    Secure, isolated environments for each institution with custom branding.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold mb-1">Enterprise Security</h3>
                  <p className="text-blue-100 text-sm">
                    Advanced security features with RBAC and comprehensive audit logging.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold mb-1">Lightning Fast</h3>
                  <p className="text-blue-100 text-sm">
                    Optimized performance with modern technology stack.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Decorative elements */}
          <div className="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl" />
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/10 rounded-full blur-xl" />
          <div className="absolute top-1/2 right-10 w-16 h-16 bg-white/10 rounded-full blur-xl" />
        </div>

        {/* Right side - Auth forms */}
        <div className="flex-1 flex flex-col justify-center px-6 py-12 lg:px-12">
          <div className="w-full max-w-md mx-auto">
            {/* Mobile logo */}
            <div className="lg:hidden flex items-center justify-center space-x-2 mb-8">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">LMS SAAS</span>
            </div>
            
            {children}
            
            {/* Footer */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <p>
                By continuing, you agree to our{' '}
                <Link href="/terms" className="text-blue-600 hover:underline">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-blue-600 hover:underline">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
