const { RBACService, PERMISSIONS } = require('../services/rbacService');
const { responseUtils } = require('../utils/auth');

/**
 * Role-Based Access Control (RBAC) Middleware
 * Enforces permissions and access control based on user roles and permissions
 */

/**
 * Check if user has specific permission
 */
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(
          responseUtils.createErrorResponse(
            'Authentication required',
            'AUTH_REQUIRED'
          )
        );
      }

      // Super admin has all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has the required permission
      const hasPermission = await RBACService.userHasPermission(
        req.user.id,
        permission,
        req.user.institute_id
      );

      if (!hasPermission) {
        return res.status(403).json(
          responseUtils.createErrorResponse(
            `Access denied. Required permission: ${permission}`,
            'INSUFFICIENT_PERMISSIONS',
            {
              userRole: req.user.role,
              requiredPermission: permission,
              userId: req.user.id
            }
          )
        );
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error.message);
      return res.status(500).json(
        responseUtils.createErrorResponse(
          'Permission check failed',
          'PERMISSION_CHECK_ERROR'
        )
      );
    }
  };
};

/**
 * Check if user has any of the specified permissions
 */
const requireAnyPermission = (permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(
          responseUtils.createErrorResponse(
            'Authentication required',
            'AUTH_REQUIRED'
          )
        );
      }

      // Super admin has all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has any of the required permissions
      const userPermissions = await RBACService.getUserPermissions(
        req.user.id,
        req.user.institute_id
      );

      const hasAnyPermission = permissions.some(permission => 
        userPermissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return res.status(403).json(
          responseUtils.createErrorResponse(
            `Access denied. Required permissions: ${permissions.join(' or ')}`,
            'INSUFFICIENT_PERMISSIONS',
            {
              userRole: req.user.role,
              requiredPermissions: permissions,
              userPermissions: userPermissions
            }
          )
        );
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error.message);
      return res.status(500).json(
        responseUtils.createErrorResponse(
          'Permission check failed',
          'PERMISSION_CHECK_ERROR'
        )
      );
    }
  };
};

/**
 * Check if user has all specified permissions
 */
const requireAllPermissions = (permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(
          responseUtils.createErrorResponse(
            'Authentication required',
            'AUTH_REQUIRED'
          )
        );
      }

      // Super admin has all permissions
      if (req.user.role === 'super_admin') {
        return next();
      }

      // Check if user has all required permissions
      const userPermissions = await RBACService.getUserPermissions(
        req.user.id,
        req.user.institute_id
      );

      const hasAllPermissions = permissions.every(permission => 
        userPermissions.includes(permission)
      );

      if (!hasAllPermissions) {
        const missingPermissions = permissions.filter(permission => 
          !userPermissions.includes(permission)
        );

        return res.status(403).json(
          responseUtils.createErrorResponse(
            `Access denied. Missing permissions: ${missingPermissions.join(', ')}`,
            'INSUFFICIENT_PERMISSIONS',
            {
              userRole: req.user.role,
              requiredPermissions: permissions,
              missingPermissions: missingPermissions
            }
          )
        );
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error.message);
      return res.status(500).json(
        responseUtils.createErrorResponse(
          'Permission check failed',
          'PERMISSION_CHECK_ERROR'
        )
      );
    }
  };
};

/**
 * Automatic resource-based permission checking
 * Checks permissions based on the current route and HTTP method
 */
const checkResourcePermissions = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json(
        responseUtils.createErrorResponse(
          'Authentication required',
          'AUTH_REQUIRED'
        )
      );
    }

    // Super admin has access to all resources
    if (req.user.role === 'super_admin') {
      return next();
    }

    // Get required permissions for this resource
    const method = req.method;
    const path = req.route?.path || req.path;
    
    const canAccess = await RBACService.userCanAccessResource(
      req.user.id,
      method,
      path,
      req.user.institute_id
    );

    if (!canAccess) {
      const requiredPermissions = RBACService.getResourcePermissions(method, path);
      
      return res.status(403).json(
        responseUtils.createErrorResponse(
          'Access denied to this resource',
          'RESOURCE_ACCESS_DENIED',
          {
            resource: `${method} ${path}`,
            userRole: req.user.role,
            requiredPermissions: requiredPermissions
          }
        )
      );
    }

    next();
  } catch (error) {
    console.error('Resource permission check error:', error.message);
    return res.status(500).json(
      responseUtils.createErrorResponse(
        'Resource permission check failed',
        'RESOURCE_PERMISSION_ERROR'
      )
    );
  }
};

/**
 * Ownership-based access control
 * Checks if user owns or has access to a specific resource
 */
const requireOwnership = (resourceType, resourceIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(
          responseUtils.createErrorResponse(
            'Authentication required',
            'AUTH_REQUIRED'
          )
        );
      }

      // Super admin and institute admin have access to all resources in their scope
      if (['super_admin', 'institute_admin'].includes(req.user.role)) {
        return next();
      }

      const resourceId = req.params[resourceIdParam];
      
      if (!resourceId) {
        return res.status(400).json(
          responseUtils.createErrorResponse(
            'Resource ID is required',
            'RESOURCE_ID_MISSING'
          )
        );
      }

      // Check ownership based on resource type
      const hasAccess = await checkResourceOwnership(
        req.user.id,
        resourceType,
        resourceId,
        req.user.institute_id
      );

      if (!hasAccess) {
        return res.status(403).json(
          responseUtils.createErrorResponse(
            'Access denied. You do not own this resource.',
            'RESOURCE_OWNERSHIP_DENIED',
            {
              resourceType,
              resourceId,
              userId: req.user.id
            }
          )
        );
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error.message);
      return res.status(500).json(
        responseUtils.createErrorResponse(
          'Ownership check failed',
          'OWNERSHIP_CHECK_ERROR'
        )
      );
    }
  };
};

/**
 * Helper function to check resource ownership
 */
const checkResourceOwnership = async (userId, resourceType, resourceId, instituteId) => {
  const { query } = require('../database/connection');
  
  try {
    let sql;
    let params;

    switch (resourceType) {
      case 'course':
        sql = 'SELECT id FROM courses WHERE id = $1 AND created_by = $2 AND institute_id = $3';
        params = [resourceId, userId, instituteId];
        break;
        
      case 'content':
        sql = 'SELECT id FROM content WHERE id = $1 AND created_by = $2 AND institute_id = $3';
        params = [resourceId, userId, instituteId];
        break;
        
      case 'assessment':
        sql = 'SELECT id FROM assessments WHERE id = $1 AND created_by = $2 AND institute_id = $3';
        params = [resourceId, userId, instituteId];
        break;
        
      case 'user':
        // Users can access their own profile, admins can access users in their institute
        sql = 'SELECT id FROM users WHERE id = $1 AND (id = $2 OR institute_id = $3)';
        params = [resourceId, userId, instituteId];
        break;
        
      default:
        return false;
    }

    const result = await query(sql, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Resource ownership check error:', error.message);
    return false;
  }
};

/**
 * Middleware to attach user permissions to request
 */
const attachUserPermissions = async (req, res, next) => {
  try {
    if (req.user) {
      req.userPermissions = await RBACService.getUserPermissions(
        req.user.id,
        req.user.institute_id
      );
    }
    next();
  } catch (error) {
    console.error('Error attaching user permissions:', error.message);
    next(); // Continue without permissions
  }
};

/**
 * Convenience middleware combinations
 */

// Platform management (Super Admin only)
const requirePlatformAccess = requirePermission(PERMISSIONS.PLATFORM_MANAGE);

// Institute management
const requireInstituteManagement = requireAnyPermission([
  PERMISSIONS.INSTITUTE_MANAGE,
  PERMISSIONS.PLATFORM_MANAGE
]);

// User management
const requireUserManagement = requireAnyPermission([
  PERMISSIONS.USER_CREATE,
  PERMISSIONS.USER_UPDATE,
  PERMISSIONS.USER_DELETE
]);

// Course management
const requireCourseManagement = requireAnyPermission([
  PERMISSIONS.COURSE_CREATE,
  PERMISSIONS.COURSE_UPDATE,
  PERMISSIONS.COURSE_DELETE
]);

// Content management
const requireContentManagement = requireAnyPermission([
  PERMISSIONS.CONTENT_CREATE,
  PERMISSIONS.CONTENT_UPDATE,
  PERMISSIONS.CONTENT_DELETE
]);

// Assessment management
const requireAssessmentManagement = requireAnyPermission([
  PERMISSIONS.ASSESSMENT_CREATE,
  PERMISSIONS.ASSESSMENT_UPDATE,
  PERMISSIONS.ASSESSMENT_DELETE
]);

module.exports = {
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  checkResourcePermissions,
  requireOwnership,
  attachUserPermissions,
  
  // Convenience middleware
  requirePlatformAccess,
  requireInstituteManagement,
  requireUserManagement,
  requireCourseManagement,
  requireContentManagement,
  requireAssessmentManagement
};
