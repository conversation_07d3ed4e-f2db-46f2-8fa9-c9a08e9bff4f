'use client'

import React, { useState } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { 
  Globe, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  ExternalLink,
  Copy,
  RefreshCw,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormField } from '@/components/forms/FormField'
import { Modal } from '@/components/ui/modal'

const domainSchema = Yup.object().shape({
  domain: Yup.string()
    .matches(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/,
      'Please enter a valid domain name'
    )
    .required('Domain is required'),
})

enum DomainStatus {
  PENDING = 'pending',
  VERIFYING = 'verifying',
  VERIFIED = 'verified',
  FAILED = 'failed',
  SSL_PENDING = 'ssl_pending',
  SSL_ACTIVE = 'ssl_active',
  SSL_FAILED = 'ssl_failed',
}

interface DomainInfo {
  domain: string
  status: DomainStatus
  verificationToken?: string
  verifiedAt?: string
  sslCertificate?: {
    issuer: string
    validFrom: string
    validTo: string
    autoRenew: boolean
  }
  lastChecked?: string
}

export default function DomainManagementPage() {
  const [domainInfo, setDomainInfo] = useState<DomainInfo | null>(null)
  const [isSetupModalOpen, setIsSetupModalOpen] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [verificationInstructions, setVerificationInstructions] = useState<any>(null)

  // Mock current domain info
  const mockDomainInfo: DomainInfo = {
    domain: 'university.edu',
    status: DomainStatus.SSL_ACTIVE,
    verifiedAt: '2023-06-01T10:00:00Z',
    sslCertificate: {
      issuer: 'Let\'s Encrypt',
      validFrom: '2023-06-01T00:00:00Z',
      validTo: '2023-09-01T00:00:00Z',
      autoRenew: true,
    },
    lastChecked: new Date().toISOString(),
  }

  const getStatusBadge = (status: DomainStatus) => {
    const statusConfig = {
      [DomainStatus.PENDING]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        text: 'Pending Verification',
      },
      [DomainStatus.VERIFYING]: {
        color: 'bg-blue-100 text-blue-800',
        icon: RefreshCw,
        text: 'Verifying',
      },
      [DomainStatus.VERIFIED]: {
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
        text: 'Verified',
      },
      [DomainStatus.FAILED]: {
        color: 'bg-red-100 text-red-800',
        icon: AlertCircle,
        text: 'Verification Failed',
      },
      [DomainStatus.SSL_PENDING]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        text: 'SSL Pending',
      },
      [DomainStatus.SSL_ACTIVE]: {
        color: 'bg-green-100 text-green-800',
        icon: Shield,
        text: 'SSL Active',
      },
      [DomainStatus.SSL_FAILED]: {
        color: 'bg-red-100 text-red-800',
        icon: AlertCircle,
        text: 'SSL Failed',
      },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const handleSetupDomain = async (values: { domain: string }, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Setting up domain:', values.domain)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock verification instructions
      const instructions = {
        domain: values.domain,
        verificationToken: 'lms-verify-abc123def456',
        txtRecord: {
          name: `_lms-verification.${values.domain}`,
          value: 'lms-verify-abc123def456',
          ttl: 300,
        },
        instructions: [
          '1. Log in to your domain registrar or DNS provider',
          '2. Navigate to the DNS management section',
          `3. Add a new TXT record with the following details:`,
          `   - Name: _lms-verification.${values.domain}`,
          `   - Value: lms-verify-abc123def456`,
          `   - TTL: 300 (or 5 minutes)`,
          '4. Save the DNS record',
          '5. Wait for DNS propagation (usually 5-30 minutes)',
          '6. Click "Verify Domain" to complete the process',
        ],
      }
      
      setVerificationInstructions(instructions)
      setDomainInfo({
        domain: values.domain,
        status: DomainStatus.PENDING,
        verificationToken: instructions.verificationToken,
      })
      
      toast.success('Domain setup initiated. Please follow the verification instructions.')
      setIsSetupModalOpen(false)
      
    } catch (error) {
      console.error('Domain setup error:', error)
      toast.error('Failed to setup domain. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const handleVerifyDomain = async () => {
    if (!domainInfo) return
    
    setIsVerifying(true)
    try {
      // TODO: Replace with actual API call
      console.log('Verifying domain:', domainInfo.domain)
      
      // Simulate verification
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock verification result (70% success rate)
      const success = Math.random() > 0.3
      
      if (success) {
        setDomainInfo({
          ...domainInfo,
          status: DomainStatus.VERIFIED,
          verifiedAt: new Date().toISOString(),
        })
        toast.success('Domain verified successfully! SSL certificate provisioning will begin.')
      } else {
        toast.error('Domain verification failed. Please check your DNS settings and try again.')
      }
      
    } catch (error) {
      console.error('Domain verification error:', error)
      toast.error('Verification failed. Please try again.')
    } finally {
      setIsVerifying(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const currentDomain = domainInfo || mockDomainInfo

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Custom Domain</h1>
          <p className="text-gray-600 mt-1">
            Configure your custom domain and SSL certificate
          </p>
        </div>
        <Button onClick={() => setIsSetupModalOpen(true)}>
          <Globe className="mr-2 h-4 w-4" />
          {currentDomain ? 'Change Domain' : 'Setup Domain'}
        </Button>
      </div>

      {/* Current Domain Status */}
      {currentDomain && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Globe className="h-8 w-8 text-blue-600" />
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {currentDomain.domain}
                </h2>
                <p className="text-sm text-gray-600">
                  Your custom domain
                </p>
              </div>
            </div>
            {getStatusBadge(currentDomain.status)}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Domain Status</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Verification</span>
                  {currentDomain.status === DomainStatus.VERIFIED || 
                   currentDomain.status === DomainStatus.SSL_ACTIVE || 
                   currentDomain.status === DomainStatus.SSL_PENDING ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <Clock className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
                {currentDomain.verifiedAt && (
                  <p className="text-xs text-gray-500">
                    Verified on {new Date(currentDomain.verifiedAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">SSL Certificate</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  {currentDomain.status === DomainStatus.SSL_ACTIVE ? (
                    <Shield className="h-4 w-4 text-green-500" />
                  ) : (
                    <Clock className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
                {currentDomain.sslCertificate && (
                  <div className="text-xs text-gray-500">
                    <p>Issuer: {currentDomain.sslCertificate.issuer}</p>
                    <p>Expires: {new Date(currentDomain.sslCertificate.validTo).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Actions</h3>
              <div className="space-y-2">
                {currentDomain.status === DomainStatus.PENDING && (
                  <Button
                    size="sm"
                    onClick={handleVerifyDomain}
                    loading={isVerifying}
                  >
                    Verify Domain
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(`https://${currentDomain.domain}`, '_blank')}
                >
                  <ExternalLink className="mr-1 h-3 w-3" />
                  Visit Site
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Verification Instructions */}
      {verificationInstructions && currentDomain?.status === DomainStatus.PENDING && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-blue-400 mr-3 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Domain Verification Required
              </h3>
              <p className="text-sm text-blue-700 mb-4">
                Add the following TXT record to your domain's DNS settings:
              </p>
              
              <div className="bg-white border border-blue-200 rounded p-3 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Type:</span>
                    <p className="text-gray-900">TXT</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Name:</span>
                    <div className="flex items-center space-x-2">
                      <p className="text-gray-900 font-mono text-xs">
                        {verificationInstructions.txtRecord.name}
                      </p>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(verificationInstructions.txtRecord.name)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Value:</span>
                    <div className="flex items-center space-x-2">
                      <p className="text-gray-900 font-mono text-xs">
                        {verificationInstructions.txtRecord.value}
                      </p>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(verificationInstructions.txtRecord.value)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-sm text-blue-700">
                <p className="font-medium mb-2">Instructions:</p>
                <ol className="list-decimal list-inside space-y-1">
                  {verificationInstructions.instructions.map((instruction: string, index: number) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ol>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Setup Domain Modal */}
      <Modal
        isOpen={isSetupModalOpen}
        onClose={() => setIsSetupModalOpen(false)}
        title="Setup Custom Domain"
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Enter your custom domain name. Make sure you have access to the domain's DNS settings.
          </p>
          
          <Formik
            initialValues={{ domain: '' }}
            validationSchema={domainSchema}
            onSubmit={handleSetupDomain}
          >
            {({ isSubmitting }) => (
              <Form className="space-y-4">
                <FormField
                  name="domain"
                  label="Domain Name"
                  placeholder="yourinstitute.edu"
                  required
                />
                
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex">
                    <AlertCircle className="h-5 w-5 text-yellow-400 mr-2" />
                    <div>
                      <h3 className="text-sm font-medium text-yellow-800">
                        Requirements
                      </h3>
                      <ul className="text-sm text-yellow-700 mt-1 list-disc list-inside">
                        <li>You must own the domain</li>
                        <li>You need access to DNS settings</li>
                        <li>Premium or Enterprise subscription required</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsSetupModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" loading={isSubmitting}>
                    Setup Domain
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Modal>
    </div>
  )
}
