'use client'

import React, { useState } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { 
  Settings, 
  Globe, 
  Shield, 
  Bell, 
  Users,
  Save,
  Plus,
  X,
  AlertTriangle
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { FormField, SelectField, CheckboxField } from '@/components/forms/FormField'

const settingsSchema = Yup.object().shape({
  instituteName: Yup.string()
    .min(2, 'Institute name must be at least 2 characters')
    .required('Institute name is required'),
  instituteEmail: Yup.string()
    .email('Please enter a valid email')
    .required('Institute email is required'),
  timezone: Yup.string().required('Timezone is required'),
  language: Yup.string().required('Language is required'),
  allowSelfRegistration: Yup.boolean(),
  requireEmailVerification: Yup.boolean(),
  requireApproval: Yup.boolean(),
  defaultRole: Yup.string().required('Default role is required'),
  emailNotifications: Yup.boolean(),
  smsNotifications: Yup.boolean(),
  pushNotifications: Yup.boolean(),
  weeklyDigest: Yup.boolean(),
})

interface SettingsFormValues {
  instituteName: string
  instituteEmail: string
  timezone: string
  language: string
  allowSelfRegistration: boolean
  requireEmailVerification: boolean
  requireApproval: boolean
  defaultRole: string
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  weeklyDigest: boolean
}

const timezoneOptions = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
]

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ja', label: 'Japanese' },
  { value: 'hi', label: 'Hindi' },
]

const roleOptions = [
  { value: 'student', label: 'Student' },
  { value: 'teacher', label: 'Teacher' },
]

export default function SettingsPage() {
  const [allowedDomains, setAllowedDomains] = useState<string[]>(['example.edu', 'students.example.edu'])
  const [newDomain, setNewDomain] = useState('')

  // Mock current settings
  const currentSettings = {
    instituteName: 'Example University',
    instituteEmail: '<EMAIL>',
    timezone: 'America/New_York',
    language: 'en',
    allowSelfRegistration: true,
    requireEmailVerification: true,
    requireApproval: false,
    defaultRole: 'student',
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyDigest: true,
  }

  const addDomain = () => {
    if (newDomain && !allowedDomains.includes(newDomain)) {
      setAllowedDomains([...allowedDomains, newDomain])
      setNewDomain('')
    }
  }

  const removeDomain = (domain: string) => {
    setAllowedDomains(allowedDomains.filter(d => d !== domain))
  }

  const handleSubmit = async (values: SettingsFormValues, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Settings update:', { ...values, allowedDomains })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success('Settings updated successfully!')
      
    } catch (error) {
      console.error('Settings update error:', error)
      toast.error('Failed to update settings. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Institute Settings</h1>
        <p className="text-gray-600 mt-1">
          Configure your institute's operational settings and preferences
        </p>
      </div>

      <Formik
        initialValues={currentSettings}
        validationSchema={settingsSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form className="space-y-6">
            {/* General Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                <Settings className="inline mr-2 h-5 w-5" />
                General Settings
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  name="instituteName"
                  label="Institute Name"
                  placeholder="Enter institute name"
                  required
                />

                <FormField
                  name="instituteEmail"
                  label="Institute Email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                />

                <SelectField
                  name="timezone"
                  label="Timezone"
                  options={timezoneOptions}
                  required
                />

                <SelectField
                  name="language"
                  label="Default Language"
                  options={languageOptions}
                  required
                />
              </div>
            </div>

            {/* Domain Management */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                <Globe className="inline mr-2 h-5 w-5" />
                Allowed Email Domains
              </h2>
              
              <p className="text-sm text-gray-600 mb-4">
                Specify which email domains are allowed for user registration
              </p>

              <div className="space-y-4">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    placeholder="example.edu"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Button type="button" onClick={addDomain}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-2">
                  {allowedDomains.map((domain) => (
                    <div key={domain} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <span className="text-sm font-medium">{domain}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeDomain(domain)}
                        className="h-6 w-6"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Registration Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                <Users className="inline mr-2 h-5 w-5" />
                Registration Settings
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <CheckboxField
                    name="allowSelfRegistration"
                    label="Allow Self Registration"
                    description="Users can register themselves without admin approval"
                  />

                  <CheckboxField
                    name="requireEmailVerification"
                    label="Require Email Verification"
                    description="Users must verify their email before accessing the system"
                  />

                  <CheckboxField
                    name="requireApproval"
                    label="Require Admin Approval"
                    description="New registrations need admin approval before activation"
                  />

                  <SelectField
                    name="defaultRole"
                    label="Default Role for New Users"
                    options={roleOptions}
                    required
                  />
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex">
                    <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2" />
                    <div>
                      <h3 className="text-sm font-medium text-yellow-800">
                        Security Recommendation
                      </h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        For enhanced security, consider enabling both email verification and admin approval for new registrations.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                <Bell className="inline mr-2 h-5 w-5" />
                Notification Settings
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <CheckboxField
                  name="emailNotifications"
                  label="Email Notifications"
                  description="Send notifications via email"
                />

                <CheckboxField
                  name="smsNotifications"
                  label="SMS Notifications"
                  description="Send notifications via SMS (requires SMS service setup)"
                />

                <CheckboxField
                  name="pushNotifications"
                  label="Push Notifications"
                  description="Send browser push notifications"
                />

                <CheckboxField
                  name="weeklyDigest"
                  label="Weekly Digest"
                  description="Send weekly summary emails to administrators"
                />
              </div>
            </div>

            {/* Security Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                <Shield className="inline mr-2 h-5 w-5" />
                Security Settings
              </h2>
              
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h3 className="text-sm font-medium text-blue-800 mb-2">
                    Advanced Security Features
                  </h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Two-factor authentication (2FA) support</li>
                    <li>• Session timeout configuration</li>
                    <li>• Password policy enforcement</li>
                    <li>• IP-based access restrictions</li>
                    <li>• Audit logging and monitoring</li>
                  </ul>
                  <Button variant="outline" className="mt-3" size="sm">
                    Configure Advanced Security
                  </Button>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button type="submit" loading={isSubmitting}>
                <Save className="mr-2 h-4 w-4" />
                Save Settings
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  )
}
