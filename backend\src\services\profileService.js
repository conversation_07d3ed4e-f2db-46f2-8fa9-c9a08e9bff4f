const { query, transaction } = require('../database/connection');
const { passwordUtils } = require('../utils/auth');
const { v4: uuidv4 } = require('uuid');

/**
 * User Profile Management Service
 * Handles profile viewing, updating, and management for students and teachers
 */

class ProfileService {
  /**
   * Get user profile by ID
   */
  async getUserProfile(userId, requestingUserId = null, requestingUserRole = null) {
    try {
      // Check if user can access this profile
      if (userId !== requestingUserId && !['institute_admin', 'super_admin'].includes(requestingUserRole)) {
        throw new Error('Access denied. You can only view your own profile.');
      }

      const result = await query(`
        SELECT 
          u.id,
          u.email,
          u.first_name,
          u.last_name,
          u.phone,
          u.role,
          u.institute_id,
          u.is_active,
          u.is_email_verified,
          u.avatar_url,
          u.last_login_at,
          u.created_at,
          u.updated_at,
          i.name as institute_name,
          i.email as institute_email
        FROM users u
        LEFT JOIN institutes i ON u.institute_id = i.id
        WHERE u.id = $1
      `, [userId]);

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = result.rows[0];

      // Get additional profile data based on role
      let additionalData = {};
      
      if (user.role === 'student') {
        additionalData = await this.getStudentProfileData(userId);
      } else if (user.role === 'teacher') {
        additionalData = await this.getTeacherProfileData(userId);
      }

      return {
        ...user,
        ...additionalData
      };

    } catch (error) {
      console.error('Get user profile error:', error.message);
      throw error;
    }
  }

  /**
   * Get student-specific profile data
   */
  async getStudentProfileData(userId) {
    try {
      // Get student-specific information
      const studentResult = await query(`
        SELECT 
          student_id,
          enrollment_date,
          graduation_year,
          major,
          gpa,
          academic_status
        FROM student_profiles 
        WHERE user_id = $1
      `, [userId]);

      const studentData = studentResult.rows[0] || {};

      // Get enrollment information
      const enrollmentResult = await query(`
        SELECT 
          COUNT(*) as total_courses,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_courses,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_courses
        FROM course_enrollments 
        WHERE student_id = $1
      `, [userId]);

      const enrollmentStats = enrollmentResult.rows[0] || {
        total_courses: 0,
        active_courses: 0,
        completed_courses: 0
      };

      return {
        studentProfile: studentData,
        enrollmentStats
      };

    } catch (error) {
      console.error('Get student profile data error:', error.message);
      return {};
    }
  }

  /**
   * Get teacher-specific profile data
   */
  async getTeacherProfileData(userId) {
    try {
      // Get teacher-specific information
      const teacherResult = await query(`
        SELECT 
          employee_id,
          department,
          specialization,
          hire_date,
          office_location,
          office_hours,
          bio,
          qualifications
        FROM teacher_profiles 
        WHERE user_id = $1
      `, [userId]);

      const teacherData = teacherResult.rows[0] || {};

      // Get teaching statistics (placeholder until courses table is created)
      const teachingResult = await query(`
        SELECT
          0 as total_courses,
          0 as active_courses,
          0 as total_students
      `);

      const teachingStats = teachingResult.rows[0] || {
        total_courses: 0,
        active_courses: 0,
        total_students: 0
      };

      return {
        teacherProfile: teacherData,
        teachingStats
      };

    } catch (error) {
      console.error('Get teacher profile data error:', error.message);
      return {};
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId, updateData, requestingUserId = null, requestingUserRole = null) {
    try {
      // Check if user can update this profile
      if (userId !== requestingUserId && !['institute_admin', 'super_admin'].includes(requestingUserRole)) {
        throw new Error('Access denied. You can only update your own profile.');
      }

      const {
        firstName,
        lastName,
        phone,
        avatarUrl,
        // Role-specific fields
        studentProfile,
        teacherProfile
      } = updateData;

      const result = await transaction(async (client) => {
        // Update basic user information
        const userUpdateResult = await client.query(`
          UPDATE users 
          SET 
            first_name = COALESCE($2, first_name),
            last_name = COALESCE($3, last_name),
            phone = COALESCE($4, phone),
            avatar_url = COALESCE($5, avatar_url),
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          RETURNING *
        `, [userId, firstName, lastName, phone, avatarUrl]);

        if (userUpdateResult.rows.length === 0) {
          throw new Error('User not found');
        }

        const updatedUser = userUpdateResult.rows[0];

        // Update role-specific profile data
        if (updatedUser.role === 'student' && studentProfile) {
          await this.updateStudentProfile(client, userId, studentProfile);
        } else if (updatedUser.role === 'teacher' && teacherProfile) {
          await this.updateTeacherProfile(client, userId, teacherProfile);
        }

        return updatedUser;
      });

      return result;

    } catch (error) {
      console.error('Update user profile error:', error.message);
      throw error;
    }
  }

  /**
   * Update student-specific profile
   */
  async updateStudentProfile(client, userId, studentData) {
    const {
      studentId,
      major,
      graduationYear,
      academicStatus
    } = studentData;

    await client.query(`
      INSERT INTO student_profiles (
        user_id, student_id, major, graduation_year, academic_status
      )
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id)
      DO UPDATE SET
        student_id = COALESCE(EXCLUDED.student_id, student_profiles.student_id),
        major = COALESCE(EXCLUDED.major, student_profiles.major),
        graduation_year = COALESCE(EXCLUDED.graduation_year, student_profiles.graduation_year),
        academic_status = COALESCE(EXCLUDED.academic_status, student_profiles.academic_status),
        updated_at = CURRENT_TIMESTAMP
    `, [userId, studentId, major, graduationYear, academicStatus]);
  }

  /**
   * Update teacher-specific profile
   */
  async updateTeacherProfile(client, userId, teacherData) {
    const {
      employeeId,
      department,
      specialization,
      officeLocation,
      officeHours,
      bio,
      qualifications
    } = teacherData;

    await client.query(`
      INSERT INTO teacher_profiles (
        user_id, employee_id, department, specialization, 
        office_location, office_hours, bio, qualifications
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (user_id)
      DO UPDATE SET
        employee_id = COALESCE(EXCLUDED.employee_id, teacher_profiles.employee_id),
        department = COALESCE(EXCLUDED.department, teacher_profiles.department),
        specialization = COALESCE(EXCLUDED.specialization, teacher_profiles.specialization),
        office_location = COALESCE(EXCLUDED.office_location, teacher_profiles.office_location),
        office_hours = COALESCE(EXCLUDED.office_hours, teacher_profiles.office_hours),
        bio = COALESCE(EXCLUDED.bio, teacher_profiles.bio),
        qualifications = COALESCE(EXCLUDED.qualifications, teacher_profiles.qualifications),
        updated_at = CURRENT_TIMESTAMP
    `, [userId, employeeId, department, specialization, officeLocation, officeHours, bio, qualifications]);
  }

  /**
   * Change user password
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Get current password hash
      const userResult = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Verify current password
      const isCurrentPasswordValid = await passwordUtils.comparePassword(
        currentPassword,
        user.password_hash
      );

      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const newPasswordHash = await passwordUtils.hashPassword(newPassword);

      // Update password
      await query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [newPasswordHash, userId]
      );

      return { success: true, message: 'Password changed successfully' };

    } catch (error) {
      console.error('Change password error:', error.message);
      throw error;
    }
  }

  /**
   * Upload and update user avatar
   */
  async updateAvatar(userId, avatarUrl) {
    try {
      const result = await query(
        'UPDATE users SET avatar_url = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING avatar_url',
        [avatarUrl, userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      return {
        success: true,
        avatarUrl: result.rows[0].avatar_url,
        message: 'Avatar updated successfully'
      };

    } catch (error) {
      console.error('Update avatar error:', error.message);
      throw error;
    }
  }

  /**
   * Get user activity summary
   */
  async getUserActivitySummary(userId) {
    try {
      const result = await query(`
        SELECT 
          last_login_at,
          created_at,
          (SELECT COUNT(*) FROM user_sessions WHERE user_id = $1) as total_sessions,
          (SELECT COUNT(*) FROM user_sessions WHERE user_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '30 days') as recent_sessions
        FROM users 
        WHERE id = $1
      `, [userId]);

      return result.rows[0] || {};

    } catch (error) {
      console.error('Get user activity summary error:', error.message);
      return {};
    }
  }

  /**
   * Deactivate user account
   */
  async deactivateAccount(userId, requestingUserId, requestingUserRole) {
    try {
      // Only allow self-deactivation or admin deactivation
      if (userId !== requestingUserId && !['institute_admin', 'super_admin'].includes(requestingUserRole)) {
        throw new Error('Access denied. You can only deactivate your own account.');
      }

      await transaction(async (client) => {
        // Deactivate user
        await client.query(
          'UPDATE users SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
          [userId]
        );

        // Invalidate all sessions
        await client.query(
          'UPDATE user_sessions SET is_active = FALSE WHERE user_id = $1',
          [userId]
        );
      });

      return { success: true, message: 'Account deactivated successfully' };

    } catch (error) {
      console.error('Deactivate account error:', error.message);
      throw error;
    }
  }
}

module.exports = new ProfileService();
