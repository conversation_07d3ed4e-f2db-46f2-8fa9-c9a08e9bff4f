{"tables": [{"table_name": "blocked_ips", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "course_enrollments", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "custom_domains", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "domain_verification_logs", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "email_verifications", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "failed_login_attempts", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institute_branding", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institute_domains", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institute_settings", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institute_status_history", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institute_usage_stats", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "institutes", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "maintenance_windows", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "password_history", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "platform_analytics_cache", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "platform_feature_flags", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "platform_notifications", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "rate_limit_violations", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "security_audit_log", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "security_config", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "session_security", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "ssl_certificates", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "student_profiles", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "subscription_history", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "super_admin_activity_log", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "teacher_invitations", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "teacher_profiles", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "two_factor_auth", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "user_roles", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "user_sessions", "table_type": "BASE TABLE", "table_comment": null}, {"table_name": "users", "table_type": "BASE TABLE", "table_comment": null}], "columns": [{"table_name": "blocked_ips", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "blocked_ips", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "blocked_ips", "column_name": "reason", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "blocked_ips", "column_name": "event_count", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "1", "column_comment": null}, {"table_name": "blocked_ips", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "blocked_ips", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "blocked_ips", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "course_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "course_enrollments", "column_name": "student_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "course_enrollments", "column_name": "enrolled_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'active'::character varying", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "grade", "data_type": "character varying", "character_maximum_length": 5, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "course_enrollments", "column_name": "grade_points", "data_type": "numeric", "character_maximum_length": null, "numeric_precision": 3, "numeric_scale": 2, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "course_enrollments", "column_name": "completion_percentage", "data_type": "numeric", "character_maximum_length": null, "numeric_precision": 5, "numeric_scale": 2, "is_nullable": "YES", "column_default": "0.00", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "last_accessed_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "course_enrollments", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "course_enrollments", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "custom_domains", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "custom_domains", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "custom_domains", "column_name": "domain_name", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "custom_domains", "column_name": "verification_token", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "custom_domains", "column_name": "verification_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'pending'::character varying", "column_comment": null}, {"table_name": "custom_domains", "column_name": "ssl_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'pending'::character varying", "column_comment": null}, {"table_name": "custom_domains", "column_name": "ssl_expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "custom_domains", "column_name": "dns_configured", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "custom_domains", "column_name": "is_primary", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "custom_domains", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "custom_domains", "column_name": "verified_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "custom_domains", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "domain_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "verification_type", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "verification_status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "verification_details", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "error_message", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "domain_verification_logs", "column_name": "checked_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "email_verifications", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "email_verifications", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "verification_token", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "token_type", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "is_used", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "email_verifications", "column_name": "used_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "email_verifications", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "attempt_time", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "reason", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "failed_login_attempts", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "institute_branding", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "logo_url", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "favicon_url", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "primary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'#007bff'::character varying", "column_comment": null}, {"table_name": "institute_branding", "column_name": "secondary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'#6c757d'::character varying", "column_comment": null}, {"table_name": "institute_branding", "column_name": "accent_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'#28a745'::character varying", "column_comment": null}, {"table_name": "institute_branding", "column_name": "font_family", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'Inter'::character varying", "column_comment": null}, {"table_name": "institute_branding", "column_name": "custom_css", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "landing_page_template", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'default'::character varying", "column_comment": null}, {"table_name": "institute_branding", "column_name": "landing_page_content", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "social_links", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "contact_info", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "seo_settings", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_branding", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "institute_branding", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_branding", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_domains", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "institute_domains", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "domain", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "subdomain", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "domain_type", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'custom'::character varying", "column_comment": null}, {"table_name": "institute_domains", "column_name": "is_verified", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "institute_domains", "column_name": "verification_token", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "verification_method", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'dns_txt'::character varying", "column_comment": null}, {"table_name": "institute_domains", "column_name": "verification_record", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "verification_attempts", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_domains", "column_name": "last_verification_check", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "ssl_certificate_id", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "ssl_status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'pending'::character varying", "column_comment": null}, {"table_name": "institute_domains", "column_name": "ssl_expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "ssl_auto_renew", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "institute_domains", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "institute_domains", "column_name": "is_primary", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "institute_domains", "column_name": "redirect_to_primary", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "institute_domains", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_domains", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_domains", "column_name": "verified_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_domains", "column_name": "activated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "name", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "slug", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "phone", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "address", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "website", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "logo_url", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "primary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "secondary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "subscription_plan", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "subscription_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "subscription_expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "deleted_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "total_users", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "student_count", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "teacher_count", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "admin_count", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "domain_count", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_overview", "column_name": "active_sessions", "data_type": "bigint", "character_maximum_length": null, "numeric_precision": 64, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_settings", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "institute_settings", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_settings", "column_name": "setting_key", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_settings", "column_name": "setting_value", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_settings", "column_name": "setting_type", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'string'::character varying", "column_comment": null}, {"table_name": "institute_settings", "column_name": "is_public", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "institute_settings", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_settings", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_status_history", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "institute_status_history", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_status_history", "column_name": "changed_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_status_history", "column_name": "old_status", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_status_history", "column_name": "new_status", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_status_history", "column_name": "reason", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_status_history", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "stat_date", "data_type": "date", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "active_users", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "new_users", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "total_logins", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "storage_used_mb", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "api_requests", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "feature_usage", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institute_usage_stats", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institutes", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "institutes", "column_name": "name", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "slug", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "phone", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "address", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "website", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "logo_url", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "primary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'#1E40AF'::character varying", "column_comment": null}, {"table_name": "institutes", "column_name": "secondary_color", "data_type": "character varying", "character_maximum_length": 7, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'#3B82F6'::character varying", "column_comment": null}, {"table_name": "institutes", "column_name": "subscription_plan", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'basic'::character varying", "column_comment": null}, {"table_name": "institutes", "column_name": "subscription_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'active'::character varying", "column_comment": null}, {"table_name": "institutes", "column_name": "subscription_expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "institutes", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "institutes", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institutes", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "institutes", "column_name": "deleted_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "title", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "description", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "maintenance_type", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "start_time", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "end_time", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "affected_services", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'scheduled'::character varying", "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "created_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "maintenance_windows", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "password_history", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "password_history", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "password_history", "column_name": "password_hash", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "password_history", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "metric_type", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "metric_date", "data_type": "date", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "metric_data", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "platform_analytics_cache", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "feature_name", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "description", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "is_enabled", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "rollout_percentage", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "target_subscription_plans", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "target_institutes", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "created_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "platform_feature_flags", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "notification_type", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "title", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "message", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "severity", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'info'::character varying", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "target_audience", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'super_admin'::character varying", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "target_institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "is_read", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "is_dismissed", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "platform_notifications", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "created_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "platform_notifications", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "endpoint", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "violation_count", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "1", "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "window_start", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "window_end", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "rate_limit_violations", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "security_audit_log", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "security_audit_log", "column_name": "event_type", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "severity", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "endpoint", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "details", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "security_audit_log", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "security_config", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "security_config", "column_name": "config_key", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "security_config", "column_name": "config_value", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "security_config", "column_name": "description", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "security_config", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "security_config", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "security_config", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "session_security", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "session_security", "column_name": "session_id", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "location_country", "data_type": "character varying", "character_maximum_length": 2, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "location_city", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "session_security", "column_name": "is_suspicious", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "session_security", "column_name": "risk_score", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "session_security", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "session_security", "column_name": "last_activity", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "domain_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "certificate_authority", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'letsencrypt'::character varying", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "certificate_data", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "private_key_data", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "certificate_chain", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "issued_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "auto_renew", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "renewal_attempts", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": "0", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "last_renewal_attempt", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'pending'::character varying", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "ssl_certificates", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "student_profiles", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "student_profiles", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "student_id", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "enrollment_date", "data_type": "date", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_DATE", "column_comment": null}, {"table_name": "student_profiles", "column_name": "graduation_year", "data_type": "integer", "character_maximum_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "major", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "gpa", "data_type": "numeric", "character_maximum_length": null, "numeric_precision": 3, "numeric_scale": 2, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "academic_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'active'::character varying", "column_comment": null}, {"table_name": "student_profiles", "column_name": "emergency_contact_name", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "emergency_contact_phone", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "emergency_contact_relationship", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "address", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "date_of_birth", "data_type": "date", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "student_profiles", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "student_profiles", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "subscription_history", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "subscription_history", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "changed_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "old_plan", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "new_plan", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "old_status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "new_status", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "notes", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "subscription_history", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "admin_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "action_type", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "target_type", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "target_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "action_details", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "super_admin_activity_log", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "token", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "invited_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "department", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "message", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "(CURRENT_TIMESTAMP + '7 days'::interval)", "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "is_used", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "used_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "used_by", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_invitations", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "employee_id", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "department", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "specialization", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "hire_date", "data_type": "date", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_DATE", "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "office_location", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "office_hours", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "bio", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "qualifications", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "salary", "data_type": "numeric", "character_maximum_length": null, "numeric_precision": 10, "numeric_scale": 2, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "employment_status", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'active'::character varying", "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "teacher_profiles", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "gen_random_uuid()", "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "secret_key", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "backup_codes", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "is_enabled", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "last_used_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "two_factor_auth", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "user_roles", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "user_roles", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "user_roles", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "user_roles", "column_name": "role_name", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "user_roles", "column_name": "permissions", "data_type": "jsonb", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "'[]'::jsonb", "column_comment": null}, {"table_name": "user_roles", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "user_roles", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "user_roles", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "user_sessions", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "user_sessions", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "session_token", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "refresh_token", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "expires_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "user_sessions", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "user_sessions", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "user_sessions", "column_name": "last_accessed_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "users", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": "uuid_generate_v4()", "column_comment": null}, {"table_name": "users", "column_name": "institute_id", "data_type": "uuid", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "password_hash", "data_type": "character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "first_name", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "last_name", "data_type": "character varying", "character_maximum_length": 100, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "phone", "data_type": "character varying", "character_maximum_length": 20, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "avatar_url", "data_type": "character varying", "character_maximum_length": 500, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "role", "data_type": "character varying", "character_maximum_length": 50, "numeric_precision": null, "numeric_scale": null, "is_nullable": "NO", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "true", "column_comment": null}, {"table_name": "users", "column_name": "is_email_verified", "data_type": "boolean", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "false", "column_comment": null}, {"table_name": "users", "column_name": "last_login_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": null, "column_comment": null}, {"table_name": "users", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}, {"table_name": "users", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "column_comment": null}], "constraints": [{"table_name": "blocked_ips", "constraint_name": "2200_80242_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "blocked_ips", "constraint_name": "2200_80242_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "blocked_ips", "constraint_name": "2200_80242_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "blocked_ips", "constraint_name": "2200_80242_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "blocked_ips", "constraint_name": "blocked_ips_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "blocked_ips", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "blocked_ips", "constraint_name": "blocked_ips_ip_address_key", "constraint_type": "UNIQUE", "column_name": "ip_address", "foreign_table_name": "blocked_ips", "foreign_column_name": "ip_address", "update_rule": null, "delete_rule": null}, {"table_name": "course_enrollments", "constraint_name": "2200_80197_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "course_enrollments", "constraint_name": "2200_80197_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "course_enrollments", "constraint_name": "2200_80197_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "course_enrollments", "constraint_name": "course_enrollments_student_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "student_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "course_enrollments", "constraint_name": "course_enrollments_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "course_enrollments", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "2200_79884_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "2200_79884_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "2200_79884_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "2200_79884_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "custom_domains_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "custom_domains", "constraint_name": "custom_domains_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "custom_domains", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "custom_domains_domain_name_key", "constraint_type": "UNIQUE", "column_name": "domain_name", "foreign_table_name": "custom_domains", "foreign_column_name": "domain_name", "update_rule": null, "delete_rule": null}, {"table_name": "custom_domains", "constraint_name": "custom_domains_verification_token_key", "constraint_type": "UNIQUE", "column_name": "verification_token", "foreign_table_name": "custom_domains", "foreign_column_name": "verification_token", "update_rule": null, "delete_rule": null}, {"table_name": "domain_verification_logs", "constraint_name": "2200_80063_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "domain_verification_logs", "constraint_name": "2200_80063_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "domain_verification_logs", "constraint_name": "2200_80063_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "domain_verification_logs", "constraint_name": "2200_80063_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "domain_verification_logs", "constraint_name": "domain_verification_logs_domain_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "domain_id", "foreign_table_name": "institute_domains", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "domain_verification_logs", "constraint_name": "domain_verification_logs_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "domain_verification_logs", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_6_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "2200_79950_7_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "email_verifications_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "email_verifications", "constraint_name": "email_verifications_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "email_verifications", "constraint_name": "email_verifications_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "email_verifications", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "email_verifications", "constraint_name": "email_verifications_verification_token_key", "constraint_type": "UNIQUE", "column_name": "verification_token", "foreign_table_name": "email_verifications", "foreign_column_name": "verification_token", "update_rule": null, "delete_rule": null}, {"table_name": "failed_login_attempts", "constraint_name": "2200_80255_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "failed_login_attempts", "constraint_name": "2200_80255_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "failed_login_attempts", "constraint_name": "2200_80255_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "failed_login_attempts", "constraint_name": "failed_login_attempts_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "failed_login_attempts", "constraint_name": "failed_login_attempts_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "failed_login_attempts", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_branding", "constraint_name": "2200_80096_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_branding", "constraint_name": "2200_80096_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_branding", "constraint_name": "institute_branding_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "institute_branding", "constraint_name": "institute_branding_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institute_branding", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_domains", "constraint_name": "2200_80037_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_domains", "constraint_name": "2200_80037_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_domains", "constraint_name": "2200_80037_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_domains", "constraint_name": "institute_domains_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "institute_domains", "constraint_name": "institute_domains_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institute_domains", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_domains", "constraint_name": "institute_domains_domain_key", "constraint_type": "UNIQUE", "column_name": "domain", "foreign_table_name": "institute_domains", "foreign_column_name": "domain", "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "2200_79997_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "2200_79997_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "2200_79997_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "institute_settings_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "institute_settings", "constraint_name": "institute_settings_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institute_settings", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "unique_setting_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "institute_settings", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "unique_setting_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "institute_settings", "foreign_column_name": "setting_key", "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "unique_setting_per_institute", "constraint_type": "UNIQUE", "column_name": "setting_key", "foreign_table_name": "institute_settings", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_settings", "constraint_name": "unique_setting_per_institute", "constraint_type": "UNIQUE", "column_name": "setting_key", "foreign_table_name": "institute_settings", "foreign_column_name": "setting_key", "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "2200_80382_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "2200_80382_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "2200_80382_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "2200_80382_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "2200_80382_6_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_status_history", "constraint_name": "institute_status_history_changed_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "changed_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "institute_status_history", "constraint_name": "institute_status_history_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "institute_status_history", "constraint_name": "institute_status_history_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institute_status_history", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "2200_80466_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "2200_80466_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "2200_80466_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institute_usage_stats", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_institute_id_stat_date_key", "constraint_type": "UNIQUE", "column_name": "stat_date", "foreign_table_name": "institute_usage_stats", "foreign_column_name": "stat_date", "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_institute_id_stat_date_key", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "institute_usage_stats", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_institute_id_stat_date_key", "constraint_type": "UNIQUE", "column_name": "stat_date", "foreign_table_name": "institute_usage_stats", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "institute_usage_stats", "constraint_name": "institute_usage_stats_institute_id_stat_date_key", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "institute_usage_stats", "foreign_column_name": "stat_date", "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "2200_79865_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "2200_79865_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "2200_79865_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "2200_79865_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "institutes_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "institutes_email_key", "constraint_type": "UNIQUE", "column_name": "email", "foreign_table_name": "institutes", "foreign_column_name": "email", "update_rule": null, "delete_rule": null}, {"table_name": "institutes", "constraint_name": "institutes_slug_key", "constraint_type": "UNIQUE", "column_name": "slug", "foreign_table_name": "institutes", "foreign_column_name": "slug", "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_6_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "2200_80450_9_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "maintenance_windows", "constraint_name": "maintenance_windows_created_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "created_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "maintenance_windows", "constraint_name": "maintenance_windows_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "maintenance_windows", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "password_history", "constraint_name": "2200_80309_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "password_history", "constraint_name": "2200_80309_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "password_history", "constraint_name": "2200_80309_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "password_history", "constraint_name": "password_history_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "password_history", "constraint_name": "password_history_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "password_history", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "2200_80401_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "2200_80401_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "2200_80401_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "2200_80401_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "platform_analytics_cache_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "platform_analytics_cache", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "platform_analytics_cache_metric_type_metric_date_key", "constraint_type": "UNIQUE", "column_name": "metric_type", "foreign_table_name": "platform_analytics_cache", "foreign_column_name": "metric_date", "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "platform_analytics_cache_metric_type_metric_date_key", "constraint_type": "UNIQUE", "column_name": "metric_type", "foreign_table_name": "platform_analytics_cache", "foreign_column_name": "metric_type", "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "platform_analytics_cache_metric_type_metric_date_key", "constraint_type": "UNIQUE", "column_name": "metric_date", "foreign_table_name": "platform_analytics_cache", "foreign_column_name": "metric_type", "update_rule": null, "delete_rule": null}, {"table_name": "platform_analytics_cache", "constraint_name": "platform_analytics_cache_metric_type_metric_date_key", "constraint_type": "UNIQUE", "column_name": "metric_date", "foreign_table_name": "platform_analytics_cache", "foreign_column_name": "metric_date", "update_rule": null, "delete_rule": null}, {"table_name": "platform_feature_flags", "constraint_name": "2200_80487_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_feature_flags", "constraint_name": "2200_80487_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_feature_flags", "constraint_name": "2200_80487_8_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_feature_flags", "constraint_name": "platform_feature_flags_created_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "created_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "platform_feature_flags", "constraint_name": "platform_feature_flags_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "platform_feature_flags", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "platform_feature_flags", "constraint_name": "platform_feature_flags_feature_name_key", "constraint_type": "UNIQUE", "column_name": "feature_name", "foreign_table_name": "platform_feature_flags", "foreign_column_name": "feature_name", "update_rule": null, "delete_rule": null}, {"table_name": "platform_notifications", "constraint_name": "2200_80427_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_notifications", "constraint_name": "2200_80427_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_notifications", "constraint_name": "2200_80427_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_notifications", "constraint_name": "2200_80427_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "platform_notifications", "constraint_name": "platform_notifications_created_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "created_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "platform_notifications", "constraint_name": "platform_notifications_target_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "target_institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "platform_notifications", "constraint_name": "platform_notifications_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "platform_notifications", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "2200_80269_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "2200_80269_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "2200_80269_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "2200_80269_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "2200_80269_6_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "rate_limit_violations", "constraint_name": "rate_limit_violations_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "rate_limit_violations", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "security_audit_log", "constraint_name": "2200_80228_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_audit_log", "constraint_name": "2200_80228_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_audit_log", "constraint_name": "2200_80228_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_audit_log", "constraint_name": "2200_80228_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_audit_log", "constraint_name": "security_audit_log_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "SET NULL"}, {"table_name": "security_audit_log", "constraint_name": "security_audit_log_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "security_audit_log", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "security_config", "constraint_name": "2200_80279_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_config", "constraint_name": "2200_80279_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_config", "constraint_name": "2200_80279_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "security_config", "constraint_name": "security_config_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "security_config", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "security_config", "constraint_name": "security_config_config_key_key", "constraint_type": "UNIQUE", "column_name": "config_key", "foreign_table_name": "security_config", "foreign_column_name": "config_key", "update_rule": null, "delete_rule": null}, {"table_name": "session_security", "constraint_name": "2200_80292_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "session_security", "constraint_name": "2200_80292_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "session_security", "constraint_name": "2200_80292_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "session_security", "constraint_name": "2200_80292_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "session_security", "constraint_name": "session_security_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "session_security", "constraint_name": "session_security_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "session_security", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "ssl_certificates", "constraint_name": "2200_80077_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "ssl_certificates", "constraint_name": "2200_80077_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "ssl_certificates", "constraint_name": "ssl_certificates_domain_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "domain_id", "foreign_table_name": "institute_domains", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "ssl_certificates", "constraint_name": "ssl_certificates_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "ssl_certificates", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "student_profiles", "constraint_name": "2200_80131_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "student_profiles", "constraint_name": "2200_80131_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "student_profiles", "constraint_name": "student_profiles_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "student_profiles", "constraint_name": "student_profiles_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "student_profiles", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "student_profiles", "constraint_name": "student_profiles_user_id_key", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "student_profiles", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "2200_80363_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "2200_80363_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "2200_80363_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "2200_80363_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "2200_80363_7_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "subscription_history", "constraint_name": "subscription_history_changed_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "changed_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "subscription_history", "constraint_name": "subscription_history_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "subscription_history", "constraint_name": "subscription_history_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "subscription_history", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "2200_80413_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "2200_80413_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "2200_80413_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "2200_80413_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "2200_80413_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "super_admin_activity_log", "constraint_name": "super_admin_activity_log_admin_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "admin_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "super_admin_activity_log", "constraint_name": "super_admin_activity_log_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "super_admin_activity_log", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "2200_80169_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "2200_80169_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "2200_80169_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "2200_80169_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "2200_80169_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "teacher_invitations_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "teacher_invitations", "constraint_name": "teacher_invitations_invited_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "invited_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "teacher_invitations", "constraint_name": "teacher_invitations_used_by_fkey", "constraint_type": "FOREIGN KEY", "column_name": "used_by", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "NO ACTION"}, {"table_name": "teacher_invitations", "constraint_name": "teacher_invitations_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "teacher_invitations", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "teacher_invitations", "constraint_name": "teacher_invitations_token_key", "constraint_type": "UNIQUE", "column_name": "token", "foreign_table_name": "teacher_invitations", "foreign_column_name": "token", "update_rule": null, "delete_rule": null}, {"table_name": "teacher_profiles", "constraint_name": "2200_80150_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_profiles", "constraint_name": "2200_80150_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "teacher_profiles", "constraint_name": "teacher_profiles_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "teacher_profiles", "constraint_name": "teacher_profiles_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "teacher_profiles", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "teacher_profiles", "constraint_name": "teacher_profiles_user_id_key", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "teacher_profiles", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "two_factor_auth", "constraint_name": "2200_80321_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "two_factor_auth", "constraint_name": "2200_80321_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "two_factor_auth", "constraint_name": "2200_80321_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "two_factor_auth", "constraint_name": "two_factor_auth_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "two_factor_auth", "constraint_name": "two_factor_auth_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "two_factor_auth", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "two_factor_auth", "constraint_name": "two_factor_auth_user_id_key", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "two_factor_auth", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "2200_79926_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "2200_79926_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "2200_79926_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "user_roles_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "user_roles", "constraint_name": "user_roles_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "user_roles", "constraint_name": "user_roles_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "user_roles", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "user_roles", "foreign_column_name": "role_name", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "user_roles", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "user_roles", "foreign_column_name": "role_name", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "user_id", "foreign_table_name": "user_roles", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "user_roles", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "role_name", "foreign_table_name": "user_roles", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "role_name", "foreign_table_name": "user_roles", "foreign_column_name": "role_name", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "role_name", "foreign_table_name": "user_roles", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_roles", "constraint_name": "unique_user_role_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "user_roles", "foreign_column_name": "user_id", "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "2200_79972_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "2200_79972_2_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "2200_79972_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "2200_79972_8_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "user_sessions_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "user_sessions", "constraint_name": "user_sessions_user_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "user_sessions", "constraint_name": "user_sessions_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "user_sessions", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "user_sessions_refresh_token_key", "constraint_type": "UNIQUE", "column_name": "refresh_token", "foreign_table_name": "user_sessions", "foreign_column_name": "refresh_token", "update_rule": null, "delete_rule": null}, {"table_name": "user_sessions", "constraint_name": "user_sessions_session_token_key", "constraint_type": "UNIQUE", "column_name": "session_token", "foreign_table_name": "user_sessions", "foreign_column_name": "session_token", "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_1_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_3_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_4_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_5_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_6_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "2200_79907_9_not_null", "constraint_type": "CHECK", "column_name": null, "foreign_table_name": null, "foreign_column_name": null, "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "users_institute_id_fkey", "constraint_type": "FOREIGN KEY", "column_name": "institute_id", "foreign_table_name": "institutes", "foreign_column_name": "id", "update_rule": "NO ACTION", "delete_rule": "CASCADE"}, {"table_name": "users", "constraint_name": "users_pkey", "constraint_type": "PRIMARY KEY", "column_name": "id", "foreign_table_name": "users", "foreign_column_name": "id", "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "unique_email_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "users", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "unique_email_per_institute", "constraint_type": "UNIQUE", "column_name": "email", "foreign_table_name": "users", "foreign_column_name": "email", "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "unique_email_per_institute", "constraint_type": "UNIQUE", "column_name": "email", "foreign_table_name": "users", "foreign_column_name": "institute_id", "update_rule": null, "delete_rule": null}, {"table_name": "users", "constraint_name": "unique_email_per_institute", "constraint_type": "UNIQUE", "column_name": "institute_id", "foreign_table_name": "users", "foreign_column_name": "email", "update_rule": null, "delete_rule": null}], "indexes": [{"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_ip_address_key", "indexdef": "CREATE UNIQUE INDEX blocked_ips_ip_address_key ON public.blocked_ips USING btree (ip_address)"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_pkey", "indexdef": "CREATE UNIQUE INDEX blocked_ips_pkey ON public.blocked_ips USING btree (id)"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_expires_at", "indexdef": "CREATE INDEX idx_blocked_ips_expires_at ON public.blocked_ips USING btree (expires_at)"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_ip_address", "indexdef": "CREATE INDEX idx_blocked_ips_ip_address ON public.blocked_ips USING btree (ip_address)"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "course_enrollments_pkey", "indexdef": "CREATE UNIQUE INDEX course_enrollments_pkey ON public.course_enrollments USING btree (id)"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_course_id", "indexdef": "CREATE INDEX idx_course_enrollments_course_id ON public.course_enrollments USING btree (course_id)"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_status", "indexdef": "CREATE INDEX idx_course_enrollments_status ON public.course_enrollments USING btree (status)"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_student_id", "indexdef": "CREATE INDEX idx_course_enrollments_student_id ON public.course_enrollments USING btree (student_id)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_domain_name_key", "indexdef": "CREATE UNIQUE INDEX custom_domains_domain_name_key ON public.custom_domains USING btree (domain_name)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_pkey", "indexdef": "CREATE UNIQUE INDEX custom_domains_pkey ON public.custom_domains USING btree (id)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_verification_token_key", "indexdef": "CREATE UNIQUE INDEX custom_domains_verification_token_key ON public.custom_domains USING btree (verification_token)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_domain_name", "indexdef": "CREATE INDEX idx_custom_domains_domain_name ON public.custom_domains USING btree (domain_name)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_institute_id", "indexdef": "CREATE INDEX idx_custom_domains_institute_id ON public.custom_domains USING btree (institute_id)"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_verification_status", "indexdef": "CREATE INDEX idx_custom_domains_verification_status ON public.custom_domains USING btree (verification_status)"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "domain_verification_logs_pkey", "indexdef": "CREATE UNIQUE INDEX domain_verification_logs_pkey ON public.domain_verification_logs USING btree (id)"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_checked_at", "indexdef": "CREATE INDEX idx_domain_verification_logs_checked_at ON public.domain_verification_logs USING btree (checked_at)"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_domain_id", "indexdef": "CREATE INDEX idx_domain_verification_logs_domain_id ON public.domain_verification_logs USING btree (domain_id)"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_status", "indexdef": "CREATE INDEX idx_domain_verification_logs_status ON public.domain_verification_logs USING btree (verification_status)"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_pkey", "indexdef": "CREATE UNIQUE INDEX email_verifications_pkey ON public.email_verifications USING btree (id)"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_verification_token_key", "indexdef": "CREATE UNIQUE INDEX email_verifications_verification_token_key ON public.email_verifications USING btree (verification_token)"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_expires_at", "indexdef": "CREATE INDEX idx_email_verifications_expires_at ON public.email_verifications USING btree (expires_at)"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_token", "indexdef": "CREATE INDEX idx_email_verifications_token ON public.email_verifications USING btree (verification_token)"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_user_id", "indexdef": "CREATE INDEX idx_email_verifications_user_id ON public.email_verifications USING btree (user_id)"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "failed_login_attempts_pkey", "indexdef": "CREATE UNIQUE INDEX failed_login_attempts_pkey ON public.failed_login_attempts USING btree (id)"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_attempt_time", "indexdef": "CREATE INDEX idx_failed_login_attempts_attempt_time ON public.failed_login_attempts USING btree (attempt_time)"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_email", "indexdef": "CREATE INDEX idx_failed_login_attempts_email ON public.failed_login_attempts USING btree (email)"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_ip_address", "indexdef": "CREATE INDEX idx_failed_login_attempts_ip_address ON public.failed_login_attempts USING btree (ip_address)"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_institute_id", "indexdef": "CREATE INDEX idx_institute_branding_institute_id ON public.institute_branding USING btree (institute_id)"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_is_active", "indexdef": "CREATE INDEX idx_institute_branding_is_active ON public.institute_branding USING btree (is_active)"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "institute_branding_pkey", "indexdef": "CREATE UNIQUE INDEX institute_branding_pkey ON public.institute_branding USING btree (id)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_domain", "indexdef": "CREATE INDEX idx_institute_domains_domain ON public.institute_domains USING btree (domain)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_institute_id", "indexdef": "CREATE INDEX idx_institute_domains_institute_id ON public.institute_domains USING btree (institute_id)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_active", "indexdef": "CREATE INDEX idx_institute_domains_is_active ON public.institute_domains USING btree (is_active)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_verified", "indexdef": "CREATE INDEX idx_institute_domains_is_verified ON public.institute_domains USING btree (is_verified)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_ssl_status", "indexdef": "CREATE INDEX idx_institute_domains_ssl_status ON public.institute_domains USING btree (ssl_status)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_domain_key", "indexdef": "CREATE UNIQUE INDEX institute_domains_domain_key ON public.institute_domains USING btree (domain)"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_pkey", "indexdef": "CREATE UNIQUE INDEX institute_domains_pkey ON public.institute_domains USING btree (id)"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_institute_id", "indexdef": "CREATE INDEX idx_institute_settings_institute_id ON public.institute_settings USING btree (institute_id)"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_key", "indexdef": "CREATE INDEX idx_institute_settings_key ON public.institute_settings USING btree (setting_key)"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "institute_settings_pkey", "indexdef": "CREATE UNIQUE INDEX institute_settings_pkey ON public.institute_settings USING btree (id)"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "unique_setting_per_institute", "indexdef": "CREATE UNIQUE INDEX unique_setting_per_institute ON public.institute_settings USING btree (institute_id, setting_key)"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_changed_by", "indexdef": "CREATE INDEX idx_institute_status_history_changed_by ON public.institute_status_history USING btree (changed_by)"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_created_at", "indexdef": "CREATE INDEX idx_institute_status_history_created_at ON public.institute_status_history USING btree (created_at)"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_institute_id", "indexdef": "CREATE INDEX idx_institute_status_history_institute_id ON public.institute_status_history USING btree (institute_id)"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "institute_status_history_pkey", "indexdef": "CREATE UNIQUE INDEX institute_status_history_pkey ON public.institute_status_history USING btree (id)"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_institute_id", "indexdef": "CREATE INDEX idx_institute_usage_stats_institute_id ON public.institute_usage_stats USING btree (institute_id)"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_stat_date", "indexdef": "CREATE INDEX idx_institute_usage_stats_stat_date ON public.institute_usage_stats USING btree (stat_date)"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_institute_id_stat_date_key", "indexdef": "CREATE UNIQUE INDEX institute_usage_stats_institute_id_stat_date_key ON public.institute_usage_stats USING btree (institute_id, stat_date)"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_pkey", "indexdef": "CREATE UNIQUE INDEX institute_usage_stats_pkey ON public.institute_usage_stats USING btree (id)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_email", "indexdef": "CREATE INDEX idx_institutes_email ON public.institutes USING btree (email)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_slug", "indexdef": "CREATE INDEX idx_institutes_slug ON public.institutes USING btree (slug)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_subscription_status", "indexdef": "CREATE INDEX idx_institutes_subscription_status ON public.institutes USING btree (subscription_status)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_email_key", "indexdef": "CREATE UNIQUE INDEX institutes_email_key ON public.institutes USING btree (email)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_pkey", "indexdef": "CREATE UNIQUE INDEX institutes_pkey ON public.institutes USING btree (id)"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_slug_key", "indexdef": "CREATE UNIQUE INDEX institutes_slug_key ON public.institutes USING btree (slug)"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_end_time", "indexdef": "CREATE INDEX idx_maintenance_windows_end_time ON public.maintenance_windows USING btree (end_time)"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_start_time", "indexdef": "CREATE INDEX idx_maintenance_windows_start_time ON public.maintenance_windows USING btree (start_time)"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_status", "indexdef": "CREATE INDEX idx_maintenance_windows_status ON public.maintenance_windows USING btree (status)"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "maintenance_windows_pkey", "indexdef": "CREATE UNIQUE INDEX maintenance_windows_pkey ON public.maintenance_windows USING btree (id)"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_created_at", "indexdef": "CREATE INDEX idx_password_history_created_at ON public.password_history USING btree (created_at)"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_user_id", "indexdef": "CREATE INDEX idx_password_history_user_id ON public.password_history USING btree (user_id)"}, {"schemaname": "public", "tablename": "password_history", "indexname": "password_history_pkey", "indexdef": "CREATE UNIQUE INDEX password_history_pkey ON public.password_history USING btree (id)"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_date", "indexdef": "CREATE INDEX idx_platform_analytics_cache_metric_date ON public.platform_analytics_cache USING btree (metric_date)"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_type", "indexdef": "CREATE INDEX idx_platform_analytics_cache_metric_type ON public.platform_analytics_cache USING btree (metric_type)"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_metric_type_metric_date_key", "indexdef": "CREATE UNIQUE INDEX platform_analytics_cache_metric_type_metric_date_key ON public.platform_analytics_cache USING btree (metric_type, metric_date)"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_pkey", "indexdef": "CREATE UNIQUE INDEX platform_analytics_cache_pkey ON public.platform_analytics_cache USING btree (id)"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_feature_name", "indexdef": "CREATE INDEX idx_platform_feature_flags_feature_name ON public.platform_feature_flags USING btree (feature_name)"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_is_enabled", "indexdef": "CREATE INDEX idx_platform_feature_flags_is_enabled ON public.platform_feature_flags USING btree (is_enabled)"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_feature_name_key", "indexdef": "CREATE UNIQUE INDEX platform_feature_flags_feature_name_key ON public.platform_feature_flags USING btree (feature_name)"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_pkey", "indexdef": "CREATE UNIQUE INDEX platform_feature_flags_pkey ON public.platform_feature_flags USING btree (id)"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_created_at", "indexdef": "CREATE INDEX idx_platform_notifications_created_at ON public.platform_notifications USING btree (created_at)"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_is_read", "indexdef": "CREATE INDEX idx_platform_notifications_is_read ON public.platform_notifications USING btree (is_read)"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_audience", "indexdef": "CREATE INDEX idx_platform_notifications_target_audience ON public.platform_notifications USING btree (target_audience)"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_institute_id", "indexdef": "CREATE INDEX idx_platform_notifications_target_institute_id ON public.platform_notifications USING btree (target_institute_id)"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "platform_notifications_pkey", "indexdef": "CREATE UNIQUE INDEX platform_notifications_pkey ON public.platform_notifications USING btree (id)"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_endpoint", "indexdef": "CREATE INDEX idx_rate_limit_violations_endpoint ON public.rate_limit_violations USING btree (endpoint)"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_ip_address", "indexdef": "CREATE INDEX idx_rate_limit_violations_ip_address ON public.rate_limit_violations USING btree (ip_address)"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_window_start", "indexdef": "CREATE INDEX idx_rate_limit_violations_window_start ON public.rate_limit_violations USING btree (window_start)"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "rate_limit_violations_pkey", "indexdef": "CREATE UNIQUE INDEX rate_limit_violations_pkey ON public.rate_limit_violations USING btree (id)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_created_at", "indexdef": "CREATE INDEX idx_security_audit_log_created_at ON public.security_audit_log USING btree (created_at)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_event_type", "indexdef": "CREATE INDEX idx_security_audit_log_event_type ON public.security_audit_log USING btree (event_type)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_ip_address", "indexdef": "CREATE INDEX idx_security_audit_log_ip_address ON public.security_audit_log USING btree (ip_address)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_severity", "indexdef": "CREATE INDEX idx_security_audit_log_severity ON public.security_audit_log USING btree (severity)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_user_id", "indexdef": "CREATE INDEX idx_security_audit_log_user_id ON public.security_audit_log USING btree (user_id)"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "security_audit_log_pkey", "indexdef": "CREATE UNIQUE INDEX security_audit_log_pkey ON public.security_audit_log USING btree (id)"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_config_key", "indexdef": "CREATE INDEX idx_security_config_config_key ON public.security_config USING btree (config_key)"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_is_active", "indexdef": "CREATE INDEX idx_security_config_is_active ON public.security_config USING btree (is_active)"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_config_key_key", "indexdef": "CREATE UNIQUE INDEX security_config_config_key_key ON public.security_config USING btree (config_key)"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_pkey", "indexdef": "CREATE UNIQUE INDEX security_config_pkey ON public.security_config USING btree (id)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_ip_address", "indexdef": "CREATE INDEX idx_session_security_ip_address ON public.session_security USING btree (ip_address)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_is_suspicious", "indexdef": "CREATE INDEX idx_session_security_is_suspicious ON public.session_security USING btree (is_suspicious)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_last_activity", "indexdef": "CREATE INDEX idx_session_security_last_activity ON public.session_security USING btree (last_activity)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_session_id", "indexdef": "CREATE INDEX idx_session_security_session_id ON public.session_security USING btree (session_id)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_user_id", "indexdef": "CREATE INDEX idx_session_security_user_id ON public.session_security USING btree (user_id)"}, {"schemaname": "public", "tablename": "session_security", "indexname": "session_security_pkey", "indexdef": "CREATE UNIQUE INDEX session_security_pkey ON public.session_security USING btree (id)"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_domain_id", "indexdef": "CREATE INDEX idx_ssl_certificates_domain_id ON public.ssl_certificates USING btree (domain_id)"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_expires_at", "indexdef": "CREATE INDEX idx_ssl_certificates_expires_at ON public.ssl_certificates USING btree (expires_at)"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_status", "indexdef": "CREATE INDEX idx_ssl_certificates_status ON public.ssl_certificates USING btree (status)"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "ssl_certificates_pkey", "indexdef": "CREATE UNIQUE INDEX ssl_certificates_pkey ON public.ssl_certificates USING btree (id)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_academic_status", "indexdef": "CREATE INDEX idx_student_profiles_academic_status ON public.student_profiles USING btree (academic_status)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_graduation_year", "indexdef": "CREATE INDEX idx_student_profiles_graduation_year ON public.student_profiles USING btree (graduation_year)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_student_id", "indexdef": "CREATE INDEX idx_student_profiles_student_id ON public.student_profiles USING btree (student_id)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_user_id", "indexdef": "CREATE INDEX idx_student_profiles_user_id ON public.student_profiles USING btree (user_id)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_pkey", "indexdef": "CREATE UNIQUE INDEX student_profiles_pkey ON public.student_profiles USING btree (id)"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_user_id_key", "indexdef": "CREATE UNIQUE INDEX student_profiles_user_id_key ON public.student_profiles USING btree (user_id)"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_changed_by", "indexdef": "CREATE INDEX idx_subscription_history_changed_by ON public.subscription_history USING btree (changed_by)"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_created_at", "indexdef": "CREATE INDEX idx_subscription_history_created_at ON public.subscription_history USING btree (created_at)"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_institute_id", "indexdef": "CREATE INDEX idx_subscription_history_institute_id ON public.subscription_history USING btree (institute_id)"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "subscription_history_pkey", "indexdef": "CREATE UNIQUE INDEX subscription_history_pkey ON public.subscription_history USING btree (id)"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_action_type", "indexdef": "CREATE INDEX idx_super_admin_activity_log_action_type ON public.super_admin_activity_log USING btree (action_type)"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_admin_id", "indexdef": "CREATE INDEX idx_super_admin_activity_log_admin_id ON public.super_admin_activity_log USING btree (admin_id)"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_created_at", "indexdef": "CREATE INDEX idx_super_admin_activity_log_created_at ON public.super_admin_activity_log USING btree (created_at)"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_target_type", "indexdef": "CREATE INDEX idx_super_admin_activity_log_target_type ON public.super_admin_activity_log USING btree (target_type)"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "super_admin_activity_log_pkey", "indexdef": "CREATE UNIQUE INDEX super_admin_activity_log_pkey ON public.super_admin_activity_log USING btree (id)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_email", "indexdef": "CREATE INDEX idx_teacher_invitations_email ON public.teacher_invitations USING btree (email)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_expires_at", "indexdef": "CREATE INDEX idx_teacher_invitations_expires_at ON public.teacher_invitations USING btree (expires_at)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_institute_id", "indexdef": "CREATE INDEX idx_teacher_invitations_institute_id ON public.teacher_invitations USING btree (institute_id)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_token", "indexdef": "CREATE INDEX idx_teacher_invitations_token ON public.teacher_invitations USING btree (token)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_pkey", "indexdef": "CREATE UNIQUE INDEX teacher_invitations_pkey ON public.teacher_invitations USING btree (id)"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_token_key", "indexdef": "CREATE UNIQUE INDEX teacher_invitations_token_key ON public.teacher_invitations USING btree (token)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_department", "indexdef": "CREATE INDEX idx_teacher_profiles_department ON public.teacher_profiles USING btree (department)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employee_id", "indexdef": "CREATE INDEX idx_teacher_profiles_employee_id ON public.teacher_profiles USING btree (employee_id)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employment_status", "indexdef": "CREATE INDEX idx_teacher_profiles_employment_status ON public.teacher_profiles USING btree (employment_status)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_user_id", "indexdef": "CREATE INDEX idx_teacher_profiles_user_id ON public.teacher_profiles USING btree (user_id)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_pkey", "indexdef": "CREATE UNIQUE INDEX teacher_profiles_pkey ON public.teacher_profiles USING btree (id)"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_user_id_key", "indexdef": "CREATE UNIQUE INDEX teacher_profiles_user_id_key ON public.teacher_profiles USING btree (user_id)"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_is_enabled", "indexdef": "CREATE INDEX idx_two_factor_auth_is_enabled ON public.two_factor_auth USING btree (is_enabled)"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_user_id", "indexdef": "CREATE INDEX idx_two_factor_auth_user_id ON public.two_factor_auth USING btree (user_id)"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_pkey", "indexdef": "CREATE UNIQUE INDEX two_factor_auth_pkey ON public.two_factor_auth USING btree (id)"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_user_id_key", "indexdef": "CREATE UNIQUE INDEX two_factor_auth_user_id_key ON public.two_factor_auth USING btree (user_id)"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_institute_id", "indexdef": "CREATE INDEX idx_user_roles_institute_id ON public.user_roles USING btree (institute_id)"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_role_name", "indexdef": "CREATE INDEX idx_user_roles_role_name ON public.user_roles USING btree (role_name)"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_user_id", "indexdef": "CREATE INDEX idx_user_roles_user_id ON public.user_roles USING btree (user_id)"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "unique_user_role_per_institute", "indexdef": "CREATE UNIQUE INDEX unique_user_role_per_institute ON public.user_roles USING btree (user_id, institute_id, role_name)"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "user_roles_pkey", "indexdef": "CREATE UNIQUE INDEX user_roles_pkey ON public.user_roles USING btree (id)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_expires_at", "indexdef": "CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions USING btree (expires_at)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_session_token", "indexdef": "CREATE INDEX idx_user_sessions_session_token ON public.user_sessions USING btree (session_token)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_user_id", "indexdef": "CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_pkey", "indexdef": "CREATE UNIQUE INDEX user_sessions_pkey ON public.user_sessions USING btree (id)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_refresh_token_key", "indexdef": "CREATE UNIQUE INDEX user_sessions_refresh_token_key ON public.user_sessions USING btree (refresh_token)"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_session_token_key", "indexdef": "CREATE UNIQUE INDEX user_sessions_session_token_key ON public.user_sessions USING btree (session_token)"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email", "indexdef": "CREATE INDEX idx_users_email ON public.users USING btree (email)"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email_institute", "indexdef": "CREATE INDEX idx_users_email_institute ON public.users USING btree (email, institute_id)"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_institute_id", "indexdef": "CREATE INDEX idx_users_institute_id ON public.users USING btree (institute_id)"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_role", "indexdef": "CREATE INDEX idx_users_role ON public.users USING btree (role)"}, {"schemaname": "public", "tablename": "users", "indexname": "unique_email_per_institute", "indexdef": "CREATE UNIQUE INDEX unique_email_per_institute ON public.users USING btree (email, institute_id)"}, {"schemaname": "public", "tablename": "users", "indexname": "users_pkey", "indexdef": "CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id)"}]}