'use client'

import React from 'react'
import { 
  Building2,
  Users,
  CreditCard,
  TrendingUp,
  Activity,
  Globe,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

// Mock data for dashboard
const dashboardStats = {
  totalInstitutes: 156,
  totalUsers: 12437,
  activeSubscriptions: 142,
  monthlyRevenue: 89750,
  systemUptime: 99.9,
  pendingApprovals: 8
}

const recentActivity = [
  {
    id: 1,
    type: 'institute_registered',
    title: 'New Institute Registration',
    description: 'Harvard Medical School completed registration',
    timestamp: '2 hours ago',
    icon: Building2,
    color: 'text-blue-600 bg-blue-100'
  },
  {
    id: 2,
    type: 'subscription_renewed',
    title: 'Subscription Renewed',
    description: 'MIT renewed Enterprise plan for 1 year',
    timestamp: '4 hours ago',
    icon: CreditCard,
    color: 'text-green-600 bg-green-100'
  },
  {
    id: 3,
    type: 'user_milestone',
    title: 'User Milestone',
    description: 'Platform reached 12,000+ active users',
    timestamp: '1 day ago',
    icon: Users,
    color: 'text-purple-600 bg-purple-100'
  },
  {
    id: 4,
    type: 'system_alert',
    title: 'System Alert Resolved',
    description: 'High CPU usage alert automatically resolved',
    timestamp: '2 days ago',
    icon: AlertTriangle,
    color: 'text-orange-600 bg-orange-100'
  }
]

const systemHealth = [
  {
    name: 'API Response Time',
    value: '145ms',
    status: 'good',
    change: '-12ms'
  },
  {
    name: 'Database Performance',
    value: '98.5%',
    status: 'good',
    change: '+0.3%'
  },
  {
    name: 'Error Rate',
    value: '0.02%',
    status: 'good',
    change: '-0.01%'
  },
  {
    name: 'Active Connections',
    value: '1,247',
    status: 'warning',
    change: '+156'
  }
]

export default function SuperAdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Platform Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Overview of your LMS SAAS platform performance and metrics
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Institutes</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardStats.totalInstitutes}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardStats.totalUsers.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardStats.activeSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${dashboardStats.monthlyRevenue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">System Uptime</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardStats.systemUptime}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardStats.pendingApprovals}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <p className="text-sm text-gray-600">Latest platform events and updates</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivity.map((activity) => {
                const Icon = activity.icon
                return (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${activity.color}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-sm text-gray-600">{activity.description}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
            <p className="text-sm text-gray-600">Real-time platform performance metrics</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {systemHealth.map((metric, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{metric.name}</p>
                    <p className="text-lg font-bold text-gray-900">{metric.value}</p>
                  </div>
                  <div className="text-right">
                    <div className={`
                      inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${metric.status === 'good' ? 'bg-green-100 text-green-800' : 
                        metric.status === 'warning' ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-red-100 text-red-800'}
                    `}>
                      {metric.status === 'good' ? (
                        <CheckCircle className="w-3 h-3 mr-1" />
                      ) : (
                        <AlertTriangle className="w-3 h-3 mr-1" />
                      )}
                      {metric.status}
                    </div>
                    <p className={`
                      text-xs mt-1
                      ${metric.change.startsWith('+') ? 'text-red-600' : 'text-green-600'}
                    `}>
                      {metric.change}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Building2 className="h-8 w-8 text-blue-600 mb-2" />
            <p className="font-medium text-gray-900">Add Institute</p>
            <p className="text-sm text-gray-600">Register a new educational institution</p>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Users className="h-8 w-8 text-green-600 mb-2" />
            <p className="font-medium text-gray-900">Manage Users</p>
            <p className="text-sm text-gray-600">View and manage platform users</p>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <CreditCard className="h-8 w-8 text-purple-600 mb-2" />
            <p className="font-medium text-gray-900">Billing Overview</p>
            <p className="text-sm text-gray-600">Review subscriptions and payments</p>
          </button>
          
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Shield className="h-8 w-8 text-orange-600 mb-2" />
            <p className="font-medium text-gray-900">Security Settings</p>
            <p className="text-sm text-gray-600">Configure platform security</p>
          </button>
        </div>
      </div>
    </div>
  )
}
