import jwt from 'jsonwebtoken'
import { JWTPayload, User } from '../types/auth'

/**
 * Generate JWT access token
 */
export const generateAccessToken = (user: User): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    instituteId: user.instituteId,
  }

  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: 'lms-saas',
    audience: 'lms-saas-users',
  } as jwt.SignOptions)
}

/**
 * Generate JWT refresh token
 */
export const generateRefreshToken = (user: User): string => {
  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET not configured')
  }

  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    instituteId: user.instituteId,
  }

  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: 'lms-saas',
    audience: 'lms-saas-refresh',
  } as jwt.SignOptions)
}

/**
 * Verify and decode access token
 */
export const verifyAccessToken = (token: string): JWTPayload => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  return jwt.verify(token, process.env.JWT_SECRET, {
    issuer: 'lms-saas',
    audience: 'lms-saas-users',
  }) as JWTPayload
}

/**
 * Verify and decode refresh token
 */
export const verifyRefreshToken = (token: string): JWTPayload => {
  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET not configured')
  }

  return jwt.verify(token, process.env.JWT_REFRESH_SECRET, {
    issuer: 'lms-saas',
    audience: 'lms-saas-refresh',
  }) as JWTPayload
}

/**
 * Generate password reset token
 */
export const generatePasswordResetToken = (userId: string, email: string): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  const payload = {
    userId,
    email,
    type: 'password_reset',
  }

  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: '1h', // Password reset tokens expire in 1 hour
    issuer: 'lms-saas',
    audience: 'lms-saas-password-reset',
  })
}

/**
 * Verify password reset token
 */
export const verifyPasswordResetToken = (token: string): { userId: string; email: string } => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  const decoded = jwt.verify(token, process.env.JWT_SECRET, {
    issuer: 'lms-saas',
    audience: 'lms-saas-password-reset',
  }) as any

  if (decoded.type !== 'password_reset') {
    throw new Error('Invalid token type')
  }

  return {
    userId: decoded.userId,
    email: decoded.email,
  }
}

/**
 * Generate email verification token
 */
export const generateEmailVerificationToken = (userId: string, email: string): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  const payload = {
    userId,
    email,
    type: 'email_verification',
  }

  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: '24h', // Email verification tokens expire in 24 hours
    issuer: 'lms-saas',
    audience: 'lms-saas-email-verification',
  })
}

/**
 * Verify email verification token
 */
export const verifyEmailVerificationToken = (token: string): { userId: string; email: string } => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured')
  }

  const decoded = jwt.verify(token, process.env.JWT_SECRET, {
    issuer: 'lms-saas',
    audience: 'lms-saas-email-verification',
  }) as any

  if (decoded.type !== 'email_verification') {
    throw new Error('Invalid token type')
  }

  return {
    userId: decoded.userId,
    email: decoded.email,
  }
}

/**
 * Extract token from Authorization header
 */
export const extractTokenFromHeader = (authHeader?: string): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7)
}

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = jwt.decode(token) as any
    if (!decoded || !decoded.exp) {
      return true
    }
    
    const currentTime = Math.floor(Date.now() / 1000)
    return decoded.exp < currentTime
  } catch {
    return true
  }
}

/**
 * Get token expiration time
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    const decoded = jwt.decode(token) as any
    if (!decoded || !decoded.exp) {
      return null
    }
    
    return new Date(decoded.exp * 1000)
  } catch {
    return null
  }
}

/**
 * Decode token without verification (for debugging)
 */
export const decodeTokenUnsafe = (token: string): any => {
  try {
    return jwt.decode(token)
  } catch {
    return null
  }
}
