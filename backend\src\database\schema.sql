-- LMS SAAS Database Schema
-- Multi-tenant architecture with institute-based isolation
-- Created: 2025-01-13

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- INSTITUTES TABLE (Tenant Management)
-- =============================================
CREATE TABLE institutes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL, -- URL-friendly identifier
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    website VARCHAR(255),
    logo_url VARCHAR(500),
    primary_color VARCHAR(7) DEFAULT '#1E40AF', -- Hex color code
    secondary_color VARCHAR(7) DEFAULT '#3B82F6',
    subscription_plan VARCHAR(50) DEFAULT 'basic', -- basic, professional, enterprise
    subscription_status VARCHAR(20) DEFAULT 'active', -- active, suspended, cancelled
    subscription_expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- CUSTOM DOMAINS TABLE
-- =============================================
CREATE TABLE custom_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    domain_name VARCHAR(255) UNIQUE NOT NULL,
    verification_token VARCHAR(255) UNIQUE NOT NULL,
    verification_status VARCHAR(20) DEFAULT 'pending', -- pending, verified, failed
    ssl_status VARCHAR(20) DEFAULT 'pending', -- pending, active, failed, expired
    ssl_expires_at TIMESTAMP,
    dns_configured BOOLEAN DEFAULT false,
    is_primary BOOLEAN DEFAULT false, -- One primary domain per institute
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- USERS TABLE (All user types)
-- =============================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE, -- NULL for super admins
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    role VARCHAR(50) NOT NULL, -- super_admin, institute_admin, teacher, student
    is_active BOOLEAN DEFAULT true,
    is_email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique email per institute (except super admins)
    CONSTRAINT unique_email_per_institute UNIQUE (email, institute_id)
);

-- =============================================
-- USER ROLES TABLE (RBAC)
-- =============================================
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    role_name VARCHAR(50) NOT NULL,
    permissions JSONB DEFAULT '[]', -- Array of permission strings
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique role per user per institute
    CONSTRAINT unique_user_role_per_institute UNIQUE (user_id, institute_id, role_name)
);

-- =============================================
-- EMAIL VERIFICATIONS TABLE
-- =============================================
CREATE TABLE email_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    verification_token VARCHAR(255) UNIQUE NOT NULL,
    token_type VARCHAR(20) NOT NULL, -- email_verification, password_reset
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- USER SESSIONS TABLE
-- =============================================
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    session_token VARCHAR(500) UNIQUE NOT NULL,
    refresh_token VARCHAR(500) UNIQUE,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INSTITUTE SETTINGS TABLE
-- =============================================
CREATE TABLE institute_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB,
    setting_type VARCHAR(50) DEFAULT 'string', -- string, number, boolean, json
    is_public BOOLEAN DEFAULT false, -- Can be accessed by students/teachers
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique setting per institute
    CONSTRAINT unique_setting_per_institute UNIQUE (institute_id, setting_key)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Institutes indexes
CREATE INDEX idx_institutes_slug ON institutes(slug);
CREATE INDEX idx_institutes_email ON institutes(email);
CREATE INDEX idx_institutes_subscription_status ON institutes(subscription_status);

-- Custom domains indexes
CREATE INDEX idx_custom_domains_institute_id ON custom_domains(institute_id);
CREATE INDEX idx_custom_domains_domain_name ON custom_domains(domain_name);
CREATE INDEX idx_custom_domains_verification_status ON custom_domains(verification_status);

-- Users indexes
CREATE INDEX idx_users_institute_id ON users(institute_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email_institute ON users(email, institute_id);

-- User roles indexes
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_institute_id ON user_roles(institute_id);
CREATE INDEX idx_user_roles_role_name ON user_roles(role_name);

-- Email verifications indexes
CREATE INDEX idx_email_verifications_user_id ON email_verifications(user_id);
CREATE INDEX idx_email_verifications_token ON email_verifications(verification_token);
CREATE INDEX idx_email_verifications_expires_at ON email_verifications(expires_at);

-- User sessions indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Institute settings indexes
CREATE INDEX idx_institute_settings_institute_id ON institute_settings(institute_id);
CREATE INDEX idx_institute_settings_key ON institute_settings(setting_key);

-- =============================================
-- CUSTOM DOMAIN MANAGEMENT TABLES
-- =============================================

-- Institute custom domains table
CREATE TABLE institute_domains (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL UNIQUE,
    subdomain VARCHAR(100), -- For subdomain.exampllms.com
    domain_type VARCHAR(20) DEFAULT 'custom', -- 'custom', 'subdomain'
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_method VARCHAR(50) DEFAULT 'dns_txt',
    verification_record VARCHAR(500), -- The actual DNS record to add
    verification_attempts INTEGER DEFAULT 0,
    last_verification_check TIMESTAMP,
    ssl_certificate_id VARCHAR(255),
    ssl_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'issued', 'expired', 'failed'
    ssl_expires_at TIMESTAMP,
    ssl_auto_renew BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT FALSE,
    is_primary BOOLEAN DEFAULT FALSE, -- Primary domain for the institute
    redirect_to_primary BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,
    activated_at TIMESTAMP
);

-- Domain verification logs table
CREATE TABLE domain_verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain_id UUID NOT NULL REFERENCES institute_domains(id) ON DELETE CASCADE,
    verification_type VARCHAR(50) NOT NULL, -- 'dns_txt', 'file_upload', 'meta_tag'
    verification_status VARCHAR(50) NOT NULL, -- 'pending', 'success', 'failed'
    verification_details JSONB, -- Store verification response details
    error_message TEXT,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SSL certificate management table
CREATE TABLE ssl_certificates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain_id UUID NOT NULL REFERENCES institute_domains(id) ON DELETE CASCADE,
    certificate_authority VARCHAR(100) DEFAULT 'letsencrypt',
    certificate_data TEXT, -- PEM encoded certificate
    private_key_data TEXT, -- PEM encoded private key (encrypted)
    certificate_chain TEXT, -- PEM encoded certificate chain
    issued_at TIMESTAMP,
    expires_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT TRUE,
    renewal_attempts INTEGER DEFAULT 0,
    last_renewal_attempt TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'issued', 'expired', 'revoked'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Institute branding and customization table
CREATE TABLE institute_branding (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    logo_url VARCHAR(500),
    favicon_url VARCHAR(500),
    primary_color VARCHAR(7) DEFAULT '#007bff', -- Hex color code
    secondary_color VARCHAR(7) DEFAULT '#6c757d',
    accent_color VARCHAR(7) DEFAULT '#28a745',
    font_family VARCHAR(100) DEFAULT 'Inter',
    custom_css TEXT,
    landing_page_template VARCHAR(50) DEFAULT 'default',
    landing_page_content JSONB, -- Custom landing page content
    social_links JSONB, -- Social media links
    contact_info JSONB, -- Contact information
    seo_settings JSONB, -- SEO meta tags and settings
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Custom domain indexes
CREATE INDEX idx_institute_domains_institute_id ON institute_domains(institute_id);
CREATE INDEX idx_institute_domains_domain ON institute_domains(domain);
CREATE INDEX idx_institute_domains_is_active ON institute_domains(is_active);
CREATE INDEX idx_institute_domains_is_verified ON institute_domains(is_verified);
CREATE INDEX idx_institute_domains_ssl_status ON institute_domains(ssl_status);

-- Domain verification logs indexes
CREATE INDEX idx_domain_verification_logs_domain_id ON domain_verification_logs(domain_id);
CREATE INDEX idx_domain_verification_logs_status ON domain_verification_logs(verification_status);
CREATE INDEX idx_domain_verification_logs_checked_at ON domain_verification_logs(checked_at);

-- SSL certificates indexes
CREATE INDEX idx_ssl_certificates_domain_id ON ssl_certificates(domain_id);
CREATE INDEX idx_ssl_certificates_expires_at ON ssl_certificates(expires_at);
CREATE INDEX idx_ssl_certificates_status ON ssl_certificates(status);

-- Institute branding indexes
CREATE INDEX idx_institute_branding_institute_id ON institute_branding(institute_id);
CREATE INDEX idx_institute_branding_is_active ON institute_branding(is_active);

-- =============================================
-- USER PROFILE MANAGEMENT TABLES
-- =============================================

-- Student profiles table
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    student_id VARCHAR(50), -- Institute-specific student ID
    enrollment_date DATE DEFAULT CURRENT_DATE,
    graduation_year INTEGER,
    major VARCHAR(100),
    gpa DECIMAL(3,2), -- Grade Point Average
    academic_status VARCHAR(20) DEFAULT 'active', -- 'active', 'probation', 'suspended', 'graduated', 'withdrawn'
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(50),
    address TEXT,
    date_of_birth DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teacher profiles table
CREATE TABLE teacher_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    employee_id VARCHAR(50), -- Institute-specific employee ID
    department VARCHAR(100),
    specialization VARCHAR(255),
    hire_date DATE DEFAULT CURRENT_DATE,
    office_location VARCHAR(100),
    office_hours VARCHAR(255),
    bio TEXT,
    qualifications TEXT,
    salary DECIMAL(10,2),
    employment_status VARCHAR(20) DEFAULT 'active', -- 'active', 'on_leave', 'terminated'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teacher invitations table
CREATE TABLE teacher_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    institute_id UUID NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    invited_by UUID NOT NULL REFERENCES users(id),
    department VARCHAR(100),
    message TEXT,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP,
    used_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Course enrollments table (basic structure for future use)
CREATE TABLE course_enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL, -- Will reference courses table when created
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'dropped', 'failed'
    grade VARCHAR(5), -- Letter grade (A, B, C, D, F)
    grade_points DECIMAL(3,2), -- Numeric grade
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_accessed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User profile indexes
CREATE INDEX idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX idx_student_profiles_student_id ON student_profiles(student_id);
CREATE INDEX idx_student_profiles_academic_status ON student_profiles(academic_status);
CREATE INDEX idx_student_profiles_graduation_year ON student_profiles(graduation_year);

CREATE INDEX idx_teacher_profiles_user_id ON teacher_profiles(user_id);
CREATE INDEX idx_teacher_profiles_employee_id ON teacher_profiles(employee_id);
CREATE INDEX idx_teacher_profiles_department ON teacher_profiles(department);
CREATE INDEX idx_teacher_profiles_employment_status ON teacher_profiles(employment_status);

CREATE INDEX idx_teacher_invitations_institute_id ON teacher_invitations(institute_id);
CREATE INDEX idx_teacher_invitations_email ON teacher_invitations(email);
CREATE INDEX idx_teacher_invitations_token ON teacher_invitations(token);
CREATE INDEX idx_teacher_invitations_expires_at ON teacher_invitations(expires_at);

CREATE INDEX idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX idx_course_enrollments_student_id ON course_enrollments(student_id);
CREATE INDEX idx_course_enrollments_status ON course_enrollments(status);
