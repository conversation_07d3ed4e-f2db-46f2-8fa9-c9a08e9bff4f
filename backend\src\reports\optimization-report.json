{"timestamp": "2025-07-14T05:13:47.901Z", "queryAnalysis": {"tableStats": [{"schemaname": "public", "tablename": "users", "seq_scan": "145", "seq_tup_read": "1278", "idx_scan": "233", "idx_tup_fetch": "189", "n_tup_ins": "31", "n_tup_upd": "25", "n_tup_del": "21", "n_live_tup": "10", "index_usage_percent": "61.64"}, {"schemaname": "public", "tablename": "user_sessions", "seq_scan": "74", "seq_tup_read": "259", "idx_scan": "69", "idx_tup_fetch": "44", "n_tup_ins": "12", "n_tup_upd": "25", "n_tup_del": "8", "n_live_tup": "4", "index_usage_percent": "48.25"}, {"schemaname": "public", "tablename": "security_audit_log", "seq_scan": "15", "seq_tup_read": "9", "idx_scan": "38", "idx_tup_fetch": "51", "n_tup_ins": "10", "n_tup_upd": "3", "n_tup_del": "7", "n_live_tup": "3", "index_usage_percent": "71.70"}, {"schemaname": "public", "tablename": "institute_domains", "seq_scan": "7", "seq_tup_read": "0", "idx_scan": "35", "idx_tup_fetch": "15", "n_tup_ins": "2", "n_tup_upd": "4", "n_tup_del": "1", "n_live_tup": "1", "index_usage_percent": "83.33"}, {"schemaname": "public", "tablename": "institute_branding", "seq_scan": "3", "seq_tup_read": "0", "idx_scan": "14", "idx_tup_fetch": "4", "n_tup_ins": "2", "n_tup_upd": "1", "n_tup_del": "1", "n_live_tup": "1", "index_usage_percent": "82.35"}, {"schemaname": "public", "tablename": "student_profiles", "seq_scan": "6", "seq_tup_read": "0", "idx_scan": "24", "idx_tup_fetch": "11", "n_tup_ins": "8", "n_tup_upd": "0", "n_tup_del": "8", "n_live_tup": "1", "index_usage_percent": "80.00"}, {"schemaname": "public", "tablename": "failed_login_attempts", "seq_scan": "16", "seq_tup_read": "0", "idx_scan": "0", "idx_tup_fetch": "0", "n_tup_ins": "0", "n_tup_upd": "0", "n_tup_del": "0", "n_live_tup": "0", "index_usage_percent": "0.00"}, {"schemaname": "public", "tablename": "teacher_profiles", "seq_scan": "6", "seq_tup_read": "0", "idx_scan": "21", "idx_tup_fetch": "2", "n_tup_ins": "1", "n_tup_upd": "0", "n_tup_del": "1", "n_live_tup": "0", "index_usage_percent": "77.78"}, {"schemaname": "public", "tablename": "teacher_invitations", "seq_scan": "38", "seq_tup_read": "0", "idx_scan": "5", "idx_tup_fetch": "0", "n_tup_ins": "0", "n_tup_upd": "0", "n_tup_del": "0", "n_live_tup": "0", "index_usage_percent": "11.63"}, {"schemaname": "public", "tablename": "course_enrollments", "seq_scan": "4", "seq_tup_read": "0", "idx_scan": "20", "idx_tup_fetch": "0", "n_tup_ins": "0", "n_tup_upd": "0", "n_tup_del": "0", "n_live_tup": "0", "index_usage_percent": "83.33"}], "indexStats": [{"schemaname": "public", "tablename": "users", "indexname": "users_pkey", "idx_scan": "152", "idx_tup_read": "152", "idx_tup_fetch": "104"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_user_id", "idx_scan": "44", "idx_tup_read": "22", "idx_tup_fetch": "19"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_institute_id", "idx_scan": "31", "idx_tup_read": "53", "idx_tup_fetch": "42"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_session_token", "idx_scan": "24", "idx_tup_read": "24", "idx_tup_fetch": "24"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_user_id", "idx_scan": "23", "idx_tup_read": "12", "idx_tup_fetch": "11"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_role", "idx_scan": "21", "idx_tup_read": "36", "idx_tup_fetch": "27"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_user_id", "idx_scan": "20", "idx_tup_read": "3", "idx_tup_fetch": "2"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email_institute", "idx_scan": "20", "idx_tup_read": "20", "idx_tup_fetch": "15"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_student_id", "idx_scan": "20", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_institute_id", "idx_scan": "18", "idx_tup_read": "5", "idx_tup_fetch": "2"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_institute_id", "idx_scan": "14", "idx_tup_read": "5", "idx_tup_fetch": "4"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_user_id", "idx_scan": "12", "idx_tup_read": "3", "idx_tup_fetch": "3"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_ip_address", "idx_scan": "10", "idx_tup_read": "31", "idx_tup_fetch": "28"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_pkey", "idx_scan": "9", "idx_tup_read": "11", "idx_tup_fetch": "9"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_domain", "idx_scan": "8", "idx_tup_read": "6", "idx_tup_fetch": "4"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email", "idx_scan": "6", "idx_tup_read": "1", "idx_tup_fetch": "1"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_created_at", "idx_scan": "6", "idx_tup_read": "15", "idx_tup_fetch": "6"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_institute_id", "idx_scan": "5", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_event_type", "idx_scan": "5", "idx_tup_read": "8", "idx_tup_fetch": "1"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_severity", "idx_scan": "4", "idx_tup_read": "18", "idx_tup_fetch": "6"}, {"schemaname": "public", "tablename": "users", "indexname": "unique_email_per_institute", "idx_scan": "3", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "security_audit_log_pkey", "idx_scan": "1", "idx_tup_read": "1", "idx_tup_fetch": "1"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_user_id_key", "idx_scan": "1", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_user_id_key", "idx_scan": "1", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_refresh_token_key", "idx_scan": "1", "idx_tup_read": "1", "idx_tup_fetch": "1"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_status", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "failed_login_attempts_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_email", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_ip_address", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_attempt_time", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_session_token_key", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_expires_at", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_domain_key", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "institute_branding_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_active", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_verified", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_ssl_status", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_is_active", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_token_key", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "course_enrollments_pkey", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_student_id", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_academic_status", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_graduation_year", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employee_id", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_department", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employment_status", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_email", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_token", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_expires_at", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_course_id", "idx_scan": "0", "idx_tup_read": "0", "idx_tup_fetch": "0"}], "statsEnabled": false}, "indexResults": [{"name": "idx_users_institute_email", "table": "users", "purpose": "Optimize user lookup by institute and email", "created": true, "duration": 13, "error": null}, {"name": "idx_users_institute_role_active", "table": "users", "purpose": "Optimize active user queries by institute and role", "created": true, "duration": 6, "error": null}, {"name": "idx_user_sessions_institute_active", "table": "user_sessions", "purpose": "Optimize active session queries by institute", "created": true, "duration": 5, "error": null}, {"name": "idx_user_sessions_user_active", "table": "user_sessions", "purpose": "Optimize user session lookup", "created": true, "duration": 4, "error": null}, {"name": "idx_student_profiles_user_status", "table": "student_profiles", "purpose": "Optimize student profile queries by status", "created": true, "duration": 5, "error": null}, {"name": "idx_security_audit_institute_time", "table": "security_audit_log", "purpose": "Optimize security audit queries by institute and time", "created": false, "duration": 0, "error": "column \"institute_id\" does not exist"}, {"name": "idx_security_audit_event_severity", "table": "security_audit_log", "purpose": "Optimize security event filtering by type and severity", "created": true, "duration": 54, "error": null}, {"name": "idx_failed_logins_institute_time", "table": "failed_login_attempts", "purpose": "Optimize failed login analysis by institute", "created": true, "duration": 4, "error": null}, {"name": "idx_failed_logins_ip_time", "table": "failed_login_attempts", "purpose": "Optimize IP-based security analysis", "created": true, "duration": 4, "error": null}, {"name": "idx_institute_domains_domain_active", "table": "institute_domains", "purpose": "Optimize domain verification and routing", "created": true, "duration": 5, "error": null}, {"name": "idx_course_enrollments_student_status", "table": "course_enrollments", "purpose": "Optimize student enrollment queries", "created": false, "duration": 0, "error": "column \"enrollment_status\" does not exist"}], "queryResults": [{"name": "Institute User Lookup", "executionTime": 0.086, "planningTime": 10.985, "totalCost": 11.56, "actualRows": 0, "indexUsed": ["idx_user_roles_user_id"], "bufferHits": {"hits": 10, "reads": 0, "hitRatio": 100}}, {"name": "Active Sessions by Institute", "error": "column us.last_activity does not exist"}, {"name": "Student Profiles by Institute", "executionTime": 0.088, "planningTime": 7.206, "totalCost": 9.65, "actualRows": 0, "indexUsed": ["idx_student_profiles_user_status"], "bufferHits": {"hits": 10, "reads": 0, "hitRatio": 100}}, {"name": "Security Events by Institute", "error": "column sal.institute_id does not exist"}, {"name": "Failed Login Analysis", "executionTime": 0.093, "planningTime": 3.085, "totalCost": 8.22, "actualRows": 0, "indexUsed": ["idx_failed_logins_institute_time"], "bufferHits": {"hits": 20, "reads": 0, "hitRatio": 100}}], "recommendations": [{"type": "Multi-Tenancy", "priority": "high", "issue": "Ensure all tenant-scoped queries use institute_id in WHERE clauses", "recommendation": "Review application queries to ensure proper tenant isolation and index usage"}], "summary": {"indexesCreated": 9, "totalIndexes": 11, "avgQueryTime": 0.0534, "recommendationCount": 1}}