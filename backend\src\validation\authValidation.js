const yup = require('yup');
const validator = require('validator');
const { sanitizeString } = require('../middleware/securityMiddleware');

/**
 * Enhanced Authentication Input Validation Schemas
 * Uses Yup for comprehensive input validation and sanitization with security hardening
 */

/**
 * Common validation rules
 */
const commonRules = {
  email: yup
    .string()
    .email('Please provide a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .lowercase()
    .trim()
    .required('Email is required'),

  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),

  firstName: yup
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('First name is required'),

  lastName: yup
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
    .trim()
    .required('Last name is required'),

  phone: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid phone number')
    .max(20, 'Phone number must be less than 20 characters')
    .trim()
    .nullable(),

  role: yup
    .string()
    .oneOf(['super_admin', 'institute_admin', 'teacher', 'student'], 'Invalid role specified')
    .required('Role is required'),

  token: yup
    .string()
    .min(10, 'Token must be at least 10 characters')
    .max(500, 'Token is too long')
    .trim()
    .required('Token is required')
};

/**
 * Registration validation schemas
 */
const registrationSchemas = {
  // Super admin registration (platform level)
  superAdmin: yup.object({
    email: commonRules.email,
    password: commonRules.password,
    firstName: commonRules.firstName,
    lastName: commonRules.lastName,
    phone: commonRules.phone,
    masterKey: yup
      .string()
      .required('Master key is required for super admin registration')
      .trim()
  }),

  // Institute admin registration
  instituteAdmin: yup.object({
    email: commonRules.email,
    password: commonRules.password,
    firstName: commonRules.firstName,
    lastName: commonRules.lastName,
    phone: commonRules.phone,
    instituteName: yup
      .string()
      .min(2, 'Institute name must be at least 2 characters')
      .max(255, 'Institute name must be less than 255 characters')
      .trim()
      .required('Institute name is required'),
    instituteEmail: yup
      .string()
      .email('Please provide a valid institute email address')
      .max(255, 'Institute email must be less than 255 characters')
      .lowercase()
      .trim()
      .required('Institute email is required'),
    institutePhone: yup
      .string()
      .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Please provide a valid institute phone number')
      .max(20, 'Institute phone number must be less than 20 characters')
      .trim()
      .nullable(),
    instituteAddress: yup
      .string()
      .max(500, 'Institute address must be less than 500 characters')
      .trim()
      .nullable(),
    instituteWebsite: yup
      .string()
      .url('Please provide a valid website URL')
      .max(255, 'Website URL must be less than 255 characters')
      .trim()
      .nullable()
  }),

  // Student registration
  student: yup.object({
    email: commonRules.email,
    password: commonRules.password,
    firstName: commonRules.firstName,
    lastName: commonRules.lastName,
    phone: commonRules.phone,
    studentId: yup
      .string()
      .min(3, 'Student ID must be at least 3 characters')
      .max(50, 'Student ID must be less than 50 characters')
      .matches(/^[a-zA-Z0-9_-]+$/, 'Student ID can only contain letters, numbers, hyphens, and underscores')
      .trim()
      .nullable()
  }),

  // Teacher registration (usually invited by institute admin)
  teacher: yup.object({
    email: commonRules.email,
    password: commonRules.password,
    firstName: commonRules.firstName,
    lastName: commonRules.lastName,
    phone: commonRules.phone,
    department: yup
      .string()
      .min(2, 'Department must be at least 2 characters')
      .max(100, 'Department must be less than 100 characters')
      .trim()
      .nullable(),
    specialization: yup
      .string()
      .max(255, 'Specialization must be less than 255 characters')
      .trim()
      .nullable(),
    invitationToken: yup
      .string()
      .required('Invitation token is required for teacher registration')
      .trim()
  })
};

/**
 * Login validation schema
 */
const loginSchema = yup.object({
  email: commonRules.email,
  password: yup
    .string()
    .min(1, 'Password is required')
    .max(128, 'Password is too long')
    .required('Password is required'),
  rememberMe: yup
    .boolean()
    .default(false)
});

/**
 * Email verification schema
 */
const emailVerificationSchema = yup.object({
  token: commonRules.token,
  email: commonRules.email.optional()
});

/**
 * Password reset request schema
 */
const passwordResetRequestSchema = yup.object({
  email: commonRules.email
});

/**
 * Password reset confirmation schema
 */
const passwordResetConfirmSchema = yup.object({
  token: commonRules.token,
  newPassword: commonRules.password,
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Passwords must match')
    .required('Password confirmation is required')
});

/**
 * Change password schema (for authenticated users)
 */
const changePasswordSchema = yup.object({
  currentPassword: yup
    .string()
    .min(1, 'Current password is required')
    .max(128, 'Current password is too long')
    .required('Current password is required'),
  newPassword: commonRules.password,
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Passwords must match')
    .required('Password confirmation is required')
});

/**
 * Update profile schema
 */
const updateProfileSchema = yup.object({
  firstName: commonRules.firstName.optional(),
  lastName: commonRules.lastName.optional(),
  phone: commonRules.phone,
  avatarUrl: yup
    .string()
    .url('Please provide a valid avatar URL')
    .max(500, 'Avatar URL must be less than 500 characters')
    .trim()
    .nullable()
});

/**
 * Refresh token schema
 */
const refreshTokenSchema = yup.object({
  refreshToken: yup
    .string()
    .min(10, 'Refresh token must be at least 10 characters')
    .max(500, 'Refresh token is too long')
    .trim()
    .required('Refresh token is required')
});

/**
 * Validation middleware factory
 */
const createValidationMiddleware = (schema) => {
  return async (req, res, next) => {
    try {
      // Validate and transform the request body
      const validatedData = await schema.validate(req.body, {
        abortEarly: false, // Return all validation errors
        stripUnknown: true // Remove unknown fields
      });

      // Replace request body with validated data
      req.body = validatedData;
      next();
    } catch (error) {
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors,
          timestamp: new Date().toISOString()
        });
      }

      console.error('Validation middleware error:', error.message);
      return res.status(500).json({
        success: false,
        error: 'Validation processing failed',
        code: 'VALIDATION_PROCESSING_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  };
};

/**
 * Sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return value
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .substring(0, 1000); // Limit length
    }
    return value;
  };

  const sanitizeObject = (obj) => {
    if (typeof obj !== 'object' || obj === null) return obj;
    
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        sanitized[key] = value.map(sanitizeValue);
      } else if (typeof value === 'object') {
        sanitized[key] = sanitizeObject(value);
      } else {
        sanitized[key] = sanitizeValue(value);
      }
    }
    return sanitized;
  };

  req.body = sanitizeObject(req.body);
  req.query = sanitizeObject(req.query);
  
  next();
};

module.exports = {
  registrationSchemas,
  loginSchema,
  emailVerificationSchema,
  passwordResetRequestSchema,
  passwordResetConfirmSchema,
  changePasswordSchema,
  updateProfileSchema,
  refreshTokenSchema,
  createValidationMiddleware,
  sanitizeInput,
  commonRules
};
