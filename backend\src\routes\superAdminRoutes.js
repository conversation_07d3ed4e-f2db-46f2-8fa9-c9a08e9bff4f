const express = require('express');
const { authenticateToken } = require('../middleware/authMiddleware');
const { requireSuperAdmin } = require('../middleware/tenantMiddleware');
const { requireAnyPermission } = require('../middleware/rbacMiddleware');
const { PERMISSIONS } = require('../services/rbacService');
const { query, transaction } = require('../database/connection');
const { responseUtils } = require('../utils/auth');
const yup = require('yup');

const router = express.Router();

// Apply authentication and super admin requirement to all routes
router.use(authenticateToken);
router.use(requireSuperAdmin);

/**
 * @route GET /api/super-admin/dashboard/stats
 * @desc Get platform overview statistics
 * @access Super Admin
 */
router.get('/dashboard/stats', async (req, res) => {
  try {
    // Get platform statistics
    const stats = await query(`
      SELECT 
        (SELECT COUNT(*) FROM institutes WHERE is_active = TRUE) as total_institutes,
        (SELECT COUNT(*) FROM institutes WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_institutes_30d,
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
        (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
        (SELECT COUNT(*) FROM users WHERE role = 'teacher') as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'institute_admin') as total_admins,
        (SELECT COUNT(*) FROM user_sessions WHERE is_active = TRUE) as active_sessions,
        (SELECT COUNT(*) FROM security_audit_log WHERE created_at >= CURRENT_DATE) as security_events_today
    `);

    // Get subscription statistics
    const subscriptionStats = await query(`
      SELECT 
        subscription_plan,
        subscription_status,
        COUNT(*) as count
      FROM institutes 
      WHERE is_active = TRUE
      GROUP BY subscription_plan, subscription_status
      ORDER BY subscription_plan, subscription_status
    `);

    // Get recent activity
    const recentActivity = await query(`
      SELECT 
        i.name as institute_name,
        i.created_at,
        i.subscription_plan,
        i.subscription_status,
        (SELECT COUNT(*) FROM users WHERE institute_id = i.id) as user_count
      FROM institutes i
      WHERE i.is_active = TRUE
      ORDER BY i.created_at DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      stats: stats.rows[0],
      subscriptionBreakdown: subscriptionStats.rows,
      recentActivity: recentActivity.rows
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get dashboard statistics', 'STATS_ERROR')
    );
  }
});

/**
 * @route GET /api/super-admin/institutes
 * @desc Get all institutes with pagination and filtering
 * @access Super Admin
 */
router.get('/institutes', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      status = '',
      subscription_plan = '',
      subscription_status = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build WHERE clause
    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    if (search) {
      whereConditions.push(`(i.name ILIKE $${paramIndex} OR i.email ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`i.is_active = $${paramIndex}`);
      queryParams.push(status === 'active');
      paramIndex++;
    }

    if (subscription_plan) {
      whereConditions.push(`i.subscription_plan = $${paramIndex}`);
      queryParams.push(subscription_plan);
      paramIndex++;
    }

    if (subscription_status) {
      whereConditions.push(`i.subscription_status = $${paramIndex}`);
      queryParams.push(subscription_status);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get institutes with user counts
    const institutes = await query(`
      SELECT 
        i.*,
        (SELECT COUNT(*) FROM users WHERE institute_id = i.id) as user_count,
        (SELECT COUNT(*) FROM users WHERE institute_id = i.id AND role = 'student') as student_count,
        (SELECT COUNT(*) FROM users WHERE institute_id = i.id AND role = 'teacher') as teacher_count,
        (SELECT COUNT(*) FROM institute_domains WHERE institute_id = i.id AND is_active = TRUE) as domain_count
      FROM institutes i
      WHERE ${whereClause}
      ORDER BY i.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, parseInt(limit), offset]);

    // Get total count
    const totalResult = await query(`
      SELECT COUNT(*) as total
      FROM institutes i
      WHERE ${whereClause}
    `, queryParams);

    const total = parseInt(totalResult.rows[0].total);
    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      institutes: institutes.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get institutes error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get institutes', 'INSTITUTES_ERROR')
    );
  }
});

/**
 * @route GET /api/super-admin/institutes/:id
 * @desc Get detailed institute information
 * @access Super Admin
 */
router.get('/institutes/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get institute details
    const instituteResult = await query(`
      SELECT * FROM institutes WHERE id = $1
    `, [id]);

    if (instituteResult.rows.length === 0) {
      return res.status(404).json(
        responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
      );
    }

    const institute = instituteResult.rows[0];

    // Get user statistics
    const userStats = await query(`
      SELECT 
        role,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_count,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as recent_count
      FROM users 
      WHERE institute_id = $1
      GROUP BY role
    `, [id]);

    // Get domains
    const domains = await query(`
      SELECT * FROM institute_domains 
      WHERE institute_id = $1
      ORDER BY is_primary DESC, created_at ASC
    `, [id]);

    // Get recent activity
    const recentActivity = await query(`
      SELECT 
        event_type,
        severity,
        created_at,
        details
      FROM security_audit_log 
      WHERE details->>'instituteId' = $1
      ORDER BY created_at DESC
      LIMIT 20
    `, [id]);

    res.json({
      success: true,
      institute,
      userStats: userStats.rows,
      domains: domains.rows,
      recentActivity: recentActivity.rows
    });

  } catch (error) {
    console.error('Get institute details error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get institute details', 'INSTITUTE_DETAILS_ERROR')
    );
  }
});

/**
 * @route PUT /api/super-admin/institutes/:id/subscription
 * @desc Update institute subscription
 * @access Super Admin
 */
router.put('/institutes/:id/subscription', async (req, res) => {
  try {
    const { id } = req.params;
    const { subscription_plan, subscription_status, notes } = req.body;

    // Validate input
    const schema = yup.object({
      subscription_plan: yup.string().oneOf(['free', 'basic', 'premium', 'enterprise']).required(),
      subscription_status: yup.string().oneOf(['active', 'inactive', 'suspended', 'cancelled']).required(),
      notes: yup.string().max(500).nullable()
    });

    await schema.validate(req.body);

    // Update subscription
    const result = await query(`
      UPDATE institutes 
      SET 
        subscription_plan = $1,
        subscription_status = $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [subscription_plan, subscription_status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json(
        responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
      );
    }

    // Log the subscription change
    await query(`
      INSERT INTO subscription_history (
        institute_id, changed_by, old_plan, new_plan, old_status, new_status, notes
      )
      SELECT 
        $1, $2, 
        (SELECT subscription_plan FROM institutes WHERE id = $1), $3,
        (SELECT subscription_status FROM institutes WHERE id = $1), $4,
        $5
    `, [id, req.user.id, subscription_plan, subscription_status, notes]);

    res.json({
      success: true,
      message: 'Subscription updated successfully',
      institute: result.rows[0]
    });

  } catch (error) {
    console.error('Update subscription error:', error.message);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json(
        responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
      );
    }

    res.status(500).json(
      responseUtils.createErrorResponse('Failed to update subscription', 'SUBSCRIPTION_UPDATE_ERROR')
    );
  }
});

/**
 * @route PUT /api/super-admin/institutes/:id/status
 * @desc Activate or deactivate institute
 * @access Super Admin
 */
router.put('/institutes/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active, reason } = req.body;

    // Validate input
    const schema = yup.object({
      is_active: yup.boolean().required(),
      reason: yup.string().max(500).required()
    });

    await schema.validate(req.body);

    const result = await transaction(async (client) => {
      // Update institute status
      const instituteResult = await client.query(`
        UPDATE institutes 
        SET 
          is_active = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [is_active, id]);

      if (instituteResult.rows.length === 0) {
        throw new Error('Institute not found');
      }

      // If deactivating, also deactivate all users
      if (!is_active) {
        await client.query(`
          UPDATE users 
          SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
          WHERE institute_id = $1
        `, [id]);

        // Invalidate all sessions
        await client.query(`
          UPDATE user_sessions 
          SET is_active = FALSE
          WHERE user_id IN (SELECT id FROM users WHERE institute_id = $1)
        `, [id]);
      }

      // Log the status change
      await client.query(`
        INSERT INTO institute_status_history (
          institute_id, changed_by, old_status, new_status, reason
        )
        VALUES ($1, $2, NOT $3, $3, $4)
      `, [id, req.user.id, is_active, reason]);

      return instituteResult.rows[0];
    });

    res.json({
      success: true,
      message: `Institute ${is_active ? 'activated' : 'deactivated'} successfully`,
      institute: result
    });

  } catch (error) {
    console.error('Update institute status error:', error.message);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json(
        responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
      );
    }

    if (error.message === 'Institute not found') {
      return res.status(404).json(
        responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
      );
    }

    res.status(500).json(
      responseUtils.createErrorResponse('Failed to update institute status', 'STATUS_UPDATE_ERROR')
    );
  }
});

/**
 * @route GET /api/super-admin/analytics/growth
 * @desc Get platform growth analytics
 * @access Super Admin
 */
router.get('/analytics/growth', async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    let interval;
    switch (period) {
      case '7d':
        interval = '7 days';
        break;
      case '30d':
        interval = '30 days';
        break;
      case '90d':
        interval = '90 days';
        break;
      case '1y':
        interval = '1 year';
        break;
      default:
        interval = '30 days';
    }

    // Get institute growth
    const instituteGrowth = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as new_institutes,
        SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as cumulative_institutes
      FROM institutes
      WHERE created_at >= CURRENT_DATE - INTERVAL '${interval}'
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    // Get user growth
    const userGrowth = await query(`
      SELECT
        DATE(created_at) as date,
        role,
        COUNT(*) as new_users
      FROM users
      WHERE created_at >= CURRENT_DATE - INTERVAL '${interval}'
      GROUP BY DATE(created_at), role
      ORDER BY date, role
    `);

    // Get subscription trends
    const subscriptionTrends = await query(`
      SELECT
        subscription_plan,
        subscription_status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
      FROM institutes
      WHERE is_active = TRUE
      GROUP BY subscription_plan, subscription_status
      ORDER BY subscription_plan, subscription_status
    `);

    res.json({
      success: true,
      period,
      instituteGrowth: instituteGrowth.rows,
      userGrowth: userGrowth.rows,
      subscriptionTrends: subscriptionTrends.rows
    });

  } catch (error) {
    console.error('Get growth analytics error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get growth analytics', 'ANALYTICS_ERROR')
    );
  }
});

/**
 * @route GET /api/super-admin/analytics/security
 * @desc Get security analytics and threat overview
 * @access Super Admin
 */
router.get('/analytics/security', async (req, res) => {
  try {
    const { period = '7d' } = req.query;

    let interval;
    switch (period) {
      case '24h':
        interval = '24 hours';
        break;
      case '7d':
        interval = '7 days';
        break;
      case '30d':
        interval = '30 days';
        break;
      default:
        interval = '7 days';
    }

    // Get security events by type
    const securityEvents = await query(`
      SELECT
        event_type,
        severity,
        COUNT(*) as count
      FROM security_audit_log
      WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '${interval}'
      GROUP BY event_type, severity
      ORDER BY count DESC
    `);

    // Get blocked IPs
    const blockedIPs = await query(`
      SELECT
        ip_address,
        reason,
        event_count,
        expires_at,
        created_at
      FROM blocked_ips
      WHERE expires_at > CURRENT_TIMESTAMP
      ORDER BY created_at DESC
      LIMIT 20
    `);

    // Get failed login attempts by institute
    const failedLogins = await query(`
      SELECT
        i.name as institute_name,
        COUNT(fla.*) as failed_attempts
      FROM failed_login_attempts fla
      LEFT JOIN institutes i ON fla.institute_id = i.id
      WHERE fla.attempt_time >= CURRENT_TIMESTAMP - INTERVAL '${interval}'
      GROUP BY i.id, i.name
      ORDER BY failed_attempts DESC
      LIMIT 10
    `);

    // Get security trends over time
    const securityTrends = await query(`
      SELECT
        DATE(created_at) as date,
        severity,
        COUNT(*) as count
      FROM security_audit_log
      WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '${interval}'
      GROUP BY DATE(created_at), severity
      ORDER BY date, severity
    `);

    res.json({
      success: true,
      period,
      securityEvents: securityEvents.rows,
      blockedIPs: blockedIPs.rows,
      failedLogins: failedLogins.rows,
      securityTrends: securityTrends.rows
    });

  } catch (error) {
    console.error('Get security analytics error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get security analytics', 'SECURITY_ANALYTICS_ERROR')
    );
  }
});

/**
 * @route GET /api/super-admin/users
 * @desc Get platform users with filtering and pagination
 * @access Super Admin
 */
router.get('/users', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = '',
      institute_id = '',
      status = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build WHERE clause
    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    if (search) {
      whereConditions.push(`(u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (role) {
      whereConditions.push(`u.role = $${paramIndex}`);
      queryParams.push(role);
      paramIndex++;
    }

    if (institute_id) {
      whereConditions.push(`u.institute_id = $${paramIndex}`);
      queryParams.push(institute_id);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`u.is_active = $${paramIndex}`);
      queryParams.push(status === 'active');
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get users with institute information
    const users = await query(`
      SELECT
        u.*,
        i.name as institute_name,
        i.subscription_plan,
        (SELECT COUNT(*) FROM user_sessions WHERE user_id = u.id AND is_active = TRUE) as active_sessions
      FROM users u
      LEFT JOIN institutes i ON u.institute_id = i.id
      WHERE ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, parseInt(limit), offset]);

    // Get total count
    const totalResult = await query(`
      SELECT COUNT(*) as total
      FROM users u
      LEFT JOIN institutes i ON u.institute_id = i.id
      WHERE ${whereClause}
    `, queryParams);

    const total = parseInt(totalResult.rows[0].total);
    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      users: users.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get users error:', error.message);
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get users', 'USERS_ERROR')
    );
  }
});

/**
 * @route DELETE /api/super-admin/institutes/:id
 * @desc Delete institute (soft delete)
 * @access Super Admin
 */
router.delete('/institutes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason || reason.trim().length < 10) {
      return res.status(400).json(
        responseUtils.createErrorResponse('Deletion reason is required (minimum 10 characters)', 'REASON_REQUIRED')
      );
    }

    const result = await transaction(async (client) => {
      // Check if institute exists
      const instituteResult = await client.query(
        'SELECT * FROM institutes WHERE id = $1',
        [id]
      );

      if (instituteResult.rows.length === 0) {
        throw new Error('Institute not found');
      }

      // Soft delete institute
      await client.query(`
        UPDATE institutes
        SET
          is_active = FALSE,
          deleted_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [id]);

      // Deactivate all users
      await client.query(`
        UPDATE users
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE institute_id = $1
      `, [id]);

      // Invalidate all sessions
      await client.query(`
        UPDATE user_sessions
        SET is_active = FALSE
        WHERE user_id IN (SELECT id FROM users WHERE institute_id = $1)
      `, [id]);

      // Log the deletion
      await client.query(`
        INSERT INTO institute_status_history (
          institute_id, changed_by, old_status, new_status, reason
        )
        VALUES ($1, $2, TRUE, FALSE, $3)
      `, [id, req.user.id, `DELETED: ${reason}`]);

      return instituteResult.rows[0];
    });

    res.json({
      success: true,
      message: 'Institute deleted successfully',
      institute: result
    });

  } catch (error) {
    console.error('Delete institute error:', error.message);

    if (error.message === 'Institute not found') {
      return res.status(404).json(
        responseUtils.createErrorResponse('Institute not found', 'INSTITUTE_NOT_FOUND')
      );
    }

    res.status(500).json(
      responseUtils.createErrorResponse('Failed to delete institute', 'DELETE_ERROR')
    );
  }
});

module.exports = router;
