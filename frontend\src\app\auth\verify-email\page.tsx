'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { 
  Mail, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  ArrowLeft,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/button'

enum VerificationStatus {
  PENDING = 'pending',
  VERIFYING = 'verifying',
  SUCCESS = 'success',
  ERROR = 'error',
  EXPIRED = 'expired',
}

export default function VerifyEmailPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<VerificationStatus>(VerificationStatus.PENDING)
  const [isResending, setIsResending] = useState(false)
  
  const email = searchParams.get('email') || ''
  const token = searchParams.get('token') || ''

  useEffect(() => {
    // If token is provided in URL, automatically verify
    if (token) {
      handleVerifyToken(token)
    }
  }, [token])

  const handleVerifyToken = async (verificationToken: string) => {
    setStatus(VerificationStatus.VERIFYING)
    
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/users/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setStatus(VerificationStatus.SUCCESS)
        toast.success('Email verified successfully!')
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login')
        }, 3000)
      } else {
        if (data.code === 'INVALID_TOKEN') {
          setStatus(VerificationStatus.EXPIRED)
        } else {
          setStatus(VerificationStatus.ERROR)
        }
        toast.error(data.error || 'Verification failed')
      }
    } catch (error) {
      console.error('Verification error:', error)
      setStatus(VerificationStatus.ERROR)
      toast.error('Verification failed. Please try again.')
    }
  }

  const handleResendVerification = async () => {
    if (!email) {
      toast.error('Email address is required')
      return
    }

    setIsResending(true)
    
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/users/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast.success('Verification email sent! Please check your inbox.')
        setStatus(VerificationStatus.PENDING)
      } else {
        toast.error(data.error || 'Failed to resend verification email')
      }
    } catch (error) {
      console.error('Resend verification error:', error)
      toast.error('Failed to resend verification email')
    } finally {
      setIsResending(false)
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case VerificationStatus.PENDING:
        return <Mail className="h-16 w-16 text-blue-600" />
      case VerificationStatus.VERIFYING:
        return <RefreshCw className="h-16 w-16 text-blue-600 animate-spin" />
      case VerificationStatus.SUCCESS:
        return <CheckCircle className="h-16 w-16 text-green-600" />
      case VerificationStatus.ERROR:
      case VerificationStatus.EXPIRED:
        return <AlertCircle className="h-16 w-16 text-red-600" />
      default:
        return <Mail className="h-16 w-16 text-blue-600" />
    }
  }

  const getStatusTitle = () => {
    switch (status) {
      case VerificationStatus.PENDING:
        return 'Check your email'
      case VerificationStatus.VERIFYING:
        return 'Verifying your email...'
      case VerificationStatus.SUCCESS:
        return 'Email verified!'
      case VerificationStatus.ERROR:
        return 'Verification failed'
      case VerificationStatus.EXPIRED:
        return 'Verification link expired'
      default:
        return 'Email verification'
    }
  }

  const getStatusMessage = () => {
    switch (status) {
      case VerificationStatus.PENDING:
        return email 
          ? `We've sent a verification link to ${email}. Please check your inbox and click the link to verify your account.`
          : 'We\'ve sent a verification link to your email address. Please check your inbox and click the link to verify your account.'
      case VerificationStatus.VERIFYING:
        return 'Please wait while we verify your email address...'
      case VerificationStatus.SUCCESS:
        return 'Your email has been successfully verified! You can now log in to your account. Redirecting to login page...'
      case VerificationStatus.ERROR:
        return 'We couldn\'t verify your email address. The verification link may be invalid or corrupted.'
      case VerificationStatus.EXPIRED:
        return 'The verification link has expired. Please request a new verification email.'
      default:
        return 'Please verify your email address to continue.'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Back to login link */}
        <div className="flex justify-center mb-6">
          <Link 
            href="/auth/login" 
            className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to login</span>
          </Link>
        </div>

        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          {getStatusIcon()}
        </div>

        {/* Title */}
        <h2 className="text-center text-3xl font-bold text-gray-900 mb-2">
          {getStatusTitle()}
        </h2>

        {/* Message */}
        <p className="text-center text-gray-600 mb-8">
          {getStatusMessage()}
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          {(status === VerificationStatus.ERROR || status === VerificationStatus.EXPIRED) && (
            <Button
              onClick={handleResendVerification}
              loading={isResending}
              className="w-full"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Resend verification email
            </Button>
          )}

          {status === VerificationStatus.PENDING && email && (
            <Button
              onClick={handleResendVerification}
              loading={isResending}
              variant="outline"
              className="w-full"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Resend verification email
            </Button>
          )}

          {status === VerificationStatus.SUCCESS && (
            <Button
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              Continue to login
            </Button>
          )}

          {(status === VerificationStatus.ERROR || status === VerificationStatus.EXPIRED) && (
            <Button
              onClick={() => router.push('/auth/register')}
              variant="outline"
              className="w-full"
            >
              Register again
            </Button>
          )}
        </div>

        {/* Help text */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex">
            <Clock className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-800">
                Didn't receive the email?
              </h3>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Check your spam or junk folder</li>
                <li>• Make sure you entered the correct email address</li>
                <li>• Wait a few minutes for the email to arrive</li>
                <li>• Contact your institute administrator if you continue having issues</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact support */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Need help?{' '}
            <Link 
              href="/support" 
              className="text-blue-600 hover:underline font-medium"
            >
              Contact support
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
