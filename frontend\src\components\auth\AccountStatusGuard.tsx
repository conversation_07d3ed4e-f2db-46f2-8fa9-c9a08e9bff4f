'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  AlertCircle, 
  Mail, 
  Shield, 
  Clock, 
  RefreshCw,
  CheckCircle,
  XCircle,
  User
} from 'lucide-react'
import { Button } from '@/components/ui/button'

export enum AccountStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  DEACTIVATED = 'deactivated',
}

export enum RequiredAction {
  EMAIL_VERIFICATION = 'email_verification',
  CONTACT_ADMIN = 'contact_admin',
  NONE = 'none',
}

interface AccountStatusInfo {
  status: AccountStatus
  emailVerified: boolean
  requiresAction: RequiredAction
  message?: string
}

interface AccountStatusGuardProps {
  children: React.ReactNode
  requireActive?: boolean
  requireVerified?: boolean
  fallbackComponent?: React.ReactNode
}

export default function AccountStatusGuard({ 
  children, 
  requireActive = true, 
  requireVerified = true,
  fallbackComponent 
}: AccountStatusGuardProps) {
  const router = useRouter()
  const [accountStatus, setAccountStatus] = useState<AccountStatusInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isResending, setIsResending] = useState(false)

  useEffect(() => {
    checkAccountStatus()
  }, [])

  const checkAccountStatus = async () => {
    try {
      // TODO: Replace with actual API call to check account status
      // For now, we'll simulate the check
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock account status - in real implementation, this would come from API
      const mockStatus: AccountStatusInfo = {
        status: AccountStatus.ACTIVE,
        emailVerified: true,
        requiresAction: RequiredAction.NONE,
      }

      setAccountStatus(mockStatus)
    } catch (error) {
      console.error('Failed to check account status:', error)
      // Handle error - maybe redirect to login
      router.push('/auth/login')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendVerification = async () => {
    setIsResending(true)
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/users/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: '<EMAIL>' }), // Get from user context
      })

      if (response.ok) {
        // Show success message
        alert('Verification email sent! Please check your inbox.')
      } else {
        alert('Failed to send verification email. Please try again.')
      }
    } catch (error) {
      console.error('Resend verification error:', error)
      alert('Failed to send verification email. Please try again.')
    } finally {
      setIsResending(false)
    }
  }

  const getStatusIcon = (status: AccountStatus) => {
    switch (status) {
      case AccountStatus.ACTIVE:
        return <CheckCircle className="h-16 w-16 text-green-600" />
      case AccountStatus.PENDING:
        return <Clock className="h-16 w-16 text-yellow-600" />
      case AccountStatus.SUSPENDED:
        return <XCircle className="h-16 w-16 text-red-600" />
      case AccountStatus.DEACTIVATED:
        return <User className="h-16 w-16 text-gray-600" />
      default:
        return <AlertCircle className="h-16 w-16 text-gray-600" />
    }
  }

  const getStatusTitle = (status: AccountStatus, emailVerified: boolean) => {
    if (!emailVerified) {
      return 'Email Verification Required'
    }

    switch (status) {
      case AccountStatus.ACTIVE:
        return 'Account Active'
      case AccountStatus.PENDING:
        return 'Account Pending'
      case AccountStatus.SUSPENDED:
        return 'Account Suspended'
      case AccountStatus.DEACTIVATED:
        return 'Account Deactivated'
      default:
        return 'Account Status Unknown'
    }
  }

  const getStatusMessage = (status: AccountStatus, emailVerified: boolean) => {
    if (!emailVerified) {
      return 'Please verify your email address to access this feature. Check your inbox for a verification email.'
    }

    switch (status) {
      case AccountStatus.ACTIVE:
        return 'Your account is active and you have full access to all features.'
      case AccountStatus.PENDING:
        return 'Your account is pending approval. Please wait for an administrator to activate your account.'
      case AccountStatus.SUSPENDED:
        return 'Your account has been suspended. Please contact your institute administrator for assistance.'
      case AccountStatus.DEACTIVATED:
        return 'Your account has been deactivated. Please contact your institute administrator to reactivate your account.'
      default:
        return 'There was an issue checking your account status. Please try again or contact support.'
    }
  }

  const getStatusColor = (status: AccountStatus, emailVerified: boolean) => {
    if (!emailVerified) {
      return 'border-yellow-200 bg-yellow-50'
    }

    switch (status) {
      case AccountStatus.ACTIVE:
        return 'border-green-200 bg-green-50'
      case AccountStatus.PENDING:
        return 'border-yellow-200 bg-yellow-50'
      case AccountStatus.SUSPENDED:
      case AccountStatus.DEACTIVATED:
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center mb-6">
            <RefreshCw className="h-8 w-8 text-blue-600 animate-spin" />
          </div>
          <p className="text-center text-gray-600">Checking account status...</p>
        </div>
      </div>
    )
  }

  // No account status (error state)
  if (!accountStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center mb-6">
            <AlertCircle className="h-16 w-16 text-red-600" />
          </div>
          <h2 className="text-center text-2xl font-bold text-gray-900 mb-4">
            Unable to Check Account Status
          </h2>
          <p className="text-center text-gray-600 mb-6">
            We couldn't verify your account status. Please try logging in again.
          </p>
          <div className="flex justify-center">
            <Button onClick={() => router.push('/auth/login')}>
              Return to Login
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Check if account meets requirements
  const meetsActiveRequirement = !requireActive || accountStatus.status === AccountStatus.ACTIVE
  const meetsVerifiedRequirement = !requireVerified || accountStatus.emailVerified

  // If requirements are met, render children
  if (meetsActiveRequirement && meetsVerifiedRequirement) {
    return <>{children}</>
  }

  // If fallback component is provided, use it
  if (fallbackComponent) {
    return <>{fallbackComponent}</>
  }

  // Default blocked access UI
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          {!accountStatus.emailVerified ? (
            <Mail className="h-16 w-16 text-yellow-600" />
          ) : (
            getStatusIcon(accountStatus.status)
          )}
        </div>

        {/* Title */}
        <h2 className="text-center text-2xl font-bold text-gray-900 mb-4">
          {getStatusTitle(accountStatus.status, accountStatus.emailVerified)}
        </h2>

        {/* Message */}
        <p className="text-center text-gray-600 mb-6">
          {getStatusMessage(accountStatus.status, accountStatus.emailVerified)}
        </p>

        {/* Status Card */}
        <div className={`p-4 border rounded-lg mb-6 ${getStatusColor(accountStatus.status, accountStatus.emailVerified)}`}>
          <div className="flex items-center space-x-3">
            <Shield className="h-5 w-5 text-gray-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Account Status</p>
              <p className="text-xs text-gray-600">
                Status: {accountStatus.status} • Email: {accountStatus.emailVerified ? 'Verified' : 'Not Verified'}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          {accountStatus.requiresAction === RequiredAction.EMAIL_VERIFICATION && (
            <Button
              onClick={handleResendVerification}
              loading={isResending}
              className="w-full"
            >
              <Mail className="mr-2 h-4 w-4" />
              Resend Verification Email
            </Button>
          )}

          {accountStatus.requiresAction === RequiredAction.CONTACT_ADMIN && (
            <Button
              onClick={() => router.push('/support')}
              className="w-full"
            >
              Contact Administrator
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => router.push('/auth/login')}
            className="w-full"
          >
            Return to Login
          </Button>
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Need help?{' '}
            <Link href="/support" className="text-blue-600 hover:underline">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
