'use client'

import React, { useState } from 'react'
import { 
  Settings,
  Shield,
  Mail,
  Globe,
  Database,
  Bell,
  Key,
  Server,
  Save,
  RefreshCw
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { FormField, SelectField } from '@/components/forms/FormField'
import SecurityAuditPanel from '@/components/super-admin/SecurityAuditPanel'

const settingsSchema = Yup.object().shape({
  platformName: Yup.string().required('Platform name is required'),
  supportEmail: Yup.string().email('Valid email required').required('Support email is required'),
  maxInstitutes: Yup.number().min(1).required('Max institutes is required'),
  sessionTimeout: Yup.number().min(5).max(1440).required('Session timeout is required'),
  emailProvider: Yup.string().required('Email provider is required'),
  backupFrequency: Yup.string().required('Backup frequency is required'),
})

interface PlatformSettings {
  platformName: string
  supportEmail: string
  maxInstitutes: number
  sessionTimeout: number
  emailProvider: string
  backupFrequency: string
  maintenanceMode: boolean
  registrationEnabled: boolean
  emailVerificationRequired: boolean
  twoFactorRequired: boolean
}

export default function SuperAdminSettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [isSaving, setIsSaving] = useState(false)

  const initialSettings: PlatformSettings = {
    platformName: 'LMS SAAS Platform',
    supportEmail: '<EMAIL>',
    maxInstitutes: 1000,
    sessionTimeout: 60,
    emailProvider: 'sendgrid',
    backupFrequency: 'daily',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    twoFactorRequired: false,
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'audit', name: 'Security Audit', icon: Key },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'system', name: 'System', icon: Server },
    { id: 'notifications', name: 'Notifications', icon: Bell },
  ]

  const handleSubmit = async (values: PlatformSettings) => {
    setIsSaving(true)
    try {
      // TODO: Save settings to API
      console.log('Saving settings:', values)
      await new Promise(resolve => setTimeout(resolve, 1000))
      // Show success message
    } catch (error) {
      console.error('Save settings error:', error)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Platform Settings</h1>
          <p className="text-gray-600 mt-1">
            Configure platform-wide settings and preferences
          </p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          <Formik
            initialValues={initialSettings}
            validationSchema={settingsSchema}
            onSubmit={handleSubmit}
          >
            {({ values, setFieldValue }) => (
              <Form className="space-y-6">
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="platformName"
                        label="Platform Name"
                        placeholder="LMS SAAS Platform"
                        required
                      />
                      
                      <FormField
                        name="supportEmail"
                        label="Support Email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="maxInstitutes"
                        label="Maximum Institutes"
                        type="number"
                        placeholder="1000"
                        required
                      />
                      
                      <FormField
                        name="sessionTimeout"
                        label="Session Timeout (minutes)"
                        type="number"
                        placeholder="60"
                        required
                      />
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Platform Features</h4>
                      
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={values.registrationEnabled}
                            onChange={(e) => setFieldValue('registrationEnabled', e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Enable new institute registration</span>
                        </label>
                        
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={values.maintenanceMode}
                            onChange={(e) => setFieldValue('maintenanceMode', e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Maintenance mode</span>
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'security' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>

                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Authentication Requirements</h4>

                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={values.emailVerificationRequired}
                            onChange={(e) => setFieldValue('emailVerificationRequired', e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Require email verification for new accounts</span>
                        </label>

                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={values.twoFactorRequired}
                            onChange={(e) => setFieldValue('twoFactorRequired', e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Require two-factor authentication for admins</span>
                        </label>
                      </div>
                    </div>

                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex">
                        <Shield className="h-5 w-5 text-yellow-600 mr-2" />
                        <div>
                          <h4 className="text-sm font-medium text-yellow-800">Security Notice</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            Changes to security settings will affect all users on the platform.
                            Ensure you communicate any changes to institute administrators.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'audit' && (
                  <SecurityAuditPanel />
                )}

                {activeTab === 'email' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Email Configuration</h3>
                    
                    <SelectField
                      name="emailProvider"
                      label="Email Provider"
                      options={[
                        { value: 'sendgrid', label: 'SendGrid' },
                        { value: 'mailgun', label: 'Mailgun' },
                        { value: 'ses', label: 'Amazon SES' },
                        { value: 'smtp', label: 'Custom SMTP' },
                      ]}
                      required
                    />

                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex">
                        <Mail className="h-5 w-5 text-blue-600 mr-2" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-800">Email Provider Status</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            Current provider: SendGrid - Status: Active
                          </p>
                          <p className="text-sm text-blue-700">
                            Monthly quota: 50,000 emails - Used: 12,450 (24.9%)
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'system' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">System Configuration</h3>
                    
                    <SelectField
                      name="backupFrequency"
                      label="Backup Frequency"
                      options={[
                        { value: 'hourly', label: 'Hourly' },
                        { value: 'daily', label: 'Daily' },
                        { value: 'weekly', label: 'Weekly' },
                        { value: 'monthly', label: 'Monthly' },
                      ]}
                      required
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900">Database Status</h4>
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                        <p className="text-sm text-gray-600">Connected - 23.4GB used</p>
                      </div>
                      
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900">Last Backup</h4>
                          <Database className="h-4 w-4 text-gray-600" />
                        </div>
                        <p className="text-sm text-gray-600">2 hours ago - Success</p>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Notification Settings</h3>
                    
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">System Alerts</h4>
                      
                      <div className="space-y-3">
                        <label className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">High CPU usage alerts</span>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </label>
                        
                        <label className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Memory usage alerts</span>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </label>
                        
                        <label className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Service downtime alerts</span>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </label>
                        
                        <label className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">New institute registration alerts</span>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end pt-6 border-t border-gray-200">
                  <Button
                    type="submit"
                    loading={isSaving}
                    className="flex items-center"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Settings
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  )
}
