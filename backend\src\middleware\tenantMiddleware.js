const { query } = require('../database/connection');

/**
 * Multi-Tenant Middleware
 * Extracts tenant information from request and attaches institute context
 */

// In-memory cache for domain-to-institute mapping
const domainCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Extract tenant identifier from request
 * Supports multiple identification methods:
 * 1. Custom domain (institute.com)
 * 2. Subdomain (abc-college.exampllms.com)
 * 3. X-Tenant-ID header
 * 4. Query parameter (?tenant=abc-college)
 */
const extractTenantIdentifier = (req) => {
  const host = req.get('host') || '';
  const tenantHeader = req.get('X-Tenant-ID');
  const tenantQuery = req.query.tenant;

  // Method 1: Check for custom domain
  if (host && !host.includes('exampllms.com') && !host.includes('localhost')) {
    return {
      type: 'domain',
      identifier: host.replace('www.', ''),
      source: 'custom_domain'
    };
  }

  // Method 2: Check for subdomain
  if (host.includes('exampllms.com')) {
    const subdomain = host.split('.')[0];
    if (subdomain && subdomain !== 'www' && subdomain !== 'api' && subdomain !== 'admin') {
      return {
        type: 'subdomain',
        identifier: subdomain,
        source: 'subdomain'
      };
    }
  }

  // Method 3: Check X-Tenant-ID header
  if (tenantHeader) {
    return {
      type: 'header',
      identifier: tenantHeader,
      source: 'header'
    };
  }

  // Method 4: Check query parameter
  if (tenantQuery) {
    return {
      type: 'query',
      identifier: tenantQuery,
      source: 'query_param'
    };
  }

  return null;
};

/**
 * Resolve institute ID from tenant identifier
 */
const resolveInstituteId = async (tenantInfo) => {
  if (!tenantInfo) return null;

  const cacheKey = `${tenantInfo.type}:${tenantInfo.identifier}`;
  
  // Check cache first
  const cached = domainCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
    return cached.instituteId;
  }

  let instituteId = null;

  try {
    if (tenantInfo.type === 'domain') {
      // Look up by custom domain
      const result = await query(
        'SELECT institute_id FROM custom_domains WHERE domain_name = $1 AND verification_status = $2',
        [tenantInfo.identifier, 'verified']
      );
      instituteId = result.rows[0]?.institute_id || null;
    } else if (tenantInfo.type === 'subdomain' || tenantInfo.type === 'header' || tenantInfo.type === 'query') {
      // Look up by institute slug
      const result = await query(
        'SELECT id FROM institutes WHERE slug = $1 AND is_active = $2',
        [tenantInfo.identifier, true]
      );
      instituteId = result.rows[0]?.id || null;
    }

    // Cache the result
    if (instituteId) {
      domainCache.set(cacheKey, {
        instituteId,
        timestamp: Date.now()
      });
    }

  } catch (error) {
    console.error('Error resolving institute ID:', error.message);
  }

  return instituteId;
};

/**
 * Load institute information
 */
const loadInstituteInfo = async (instituteId) => {
  if (!instituteId) return null;

  const cacheKey = `institute:${instituteId}`;
  const cached = domainCache.get(cacheKey);
  
  if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
    return cached.data;
  }

  try {
    const result = await query(
      'SELECT id, name, slug, email, logo_url, primary_color, secondary_color, subscription_plan, subscription_status FROM institutes WHERE id = $1 AND is_active = $2',
      [instituteId, true]
    );

    const institute = result.rows[0] || null;
    
    if (institute) {
      domainCache.set(cacheKey, {
        data: institute,
        timestamp: Date.now()
      });
    }

    return institute;
  } catch (error) {
    console.error('Error loading institute info:', error.message);
    return null;
  }
};

/**
 * Main tenant middleware
 */
const tenantMiddleware = async (req, res, next) => {
  try {
    // Extract tenant information
    const tenantInfo = extractTenantIdentifier(req);
    
    // Resolve institute ID
    const instituteId = await resolveInstituteId(tenantInfo);
    
    // Load institute information
    const institute = instituteId ? await loadInstituteInfo(instituteId) : null;

    // Attach tenant context to request
    req.tenant = {
      identifier: tenantInfo?.identifier || null,
      source: tenantInfo?.source || null,
      instituteId: instituteId,
      institute: institute,
      isMultiTenant: !!instituteId,
      isSuperAdmin: !instituteId // Super admin requests have no institute context
    };

    // Log tenant identification for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('🏢 Tenant identified:', {
        host: req.get('host'),
        identifier: req.tenant.identifier,
        source: req.tenant.source,
        instituteId: req.tenant.instituteId,
        instituteName: req.tenant.institute?.name || 'N/A'
      });
    }

    next();
  } catch (error) {
    console.error('❌ Tenant middleware error:', error.message);
    
    // Don't block the request, but log the error
    req.tenant = {
      identifier: null,
      source: null,
      instituteId: null,
      institute: null,
      isMultiTenant: false,
      isSuperAdmin: false,
      error: error.message
    };
    
    next();
  }
};

/**
 * Middleware to require tenant context
 */
const requireTenant = (req, res, next) => {
  if (!req.tenant || !req.tenant.instituteId) {
    return res.status(400).json({
      error: 'Tenant identification required',
      message: 'This endpoint requires a valid institute context. Please access via institute domain or provide tenant identifier.',
      code: 'TENANT_REQUIRED'
    });
  }
  next();
};

/**
 * Middleware to require super admin context (no tenant)
 */
const requireSuperAdmin = (req, res, next) => {
  if (req.tenant && req.tenant.instituteId) {
    return res.status(403).json({
      error: 'Super admin access required',
      message: 'This endpoint is only accessible to super administrators.',
      code: 'SUPER_ADMIN_REQUIRED'
    });
  }
  next();
};

/**
 * Clear domain cache (for testing or cache invalidation)
 */
const clearDomainCache = () => {
  domainCache.clear();
  console.log('🧹 Domain cache cleared');
};

/**
 * Get cache statistics
 */
const getCacheStats = () => {
  return {
    size: domainCache.size,
    entries: Array.from(domainCache.keys())
  };
};

module.exports = {
  tenantMiddleware,
  requireTenant,
  requireSuperAdmin,
  clearDomainCache,
  getCacheStats,
  extractTenantIdentifier,
  resolveInstituteId,
  loadInstituteInfo
};
