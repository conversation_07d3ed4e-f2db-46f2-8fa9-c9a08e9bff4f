const { connectionPool } = require('./connectionPool');
const { DatabaseMonitor } = require('./monitoring');
const { DatabasePerformanceAnalyzer } = require('./performanceAnalysis');
const { PostgreSQLConfigOptimizer } = require('./configOptimizer');

/**
 * Comprehensive Database Setup and Testing Suite
 * Tests all database components including connection pooling, monitoring, and performance
 */

class DatabaseSetupTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '', details = null) {
    const result = {
      test: testName,
      passed,
      message,
      details,
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    if (details && process.env.VERBOSE_TESTS === 'true') {
      console.log(`   Details: ${JSON.stringify(details, null, 2)}`);
    }
  }

  /**
   * Test basic database connectivity
   */
  async testDatabaseConnectivity() {
    console.log('\n🔗 Testing Database Connectivity...');
    
    try {
      const connected = await connectionPool.testConnection();
      
      this.logResult(
        'Database Connection',
        connected,
        connected ? 'Successfully connected to PostgreSQL' : 'Failed to connect to database'
      );

      if (connected) {
        // Test basic query
        const result = await connectionPool.query('SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = $1', ['public']);
        const tableCount = parseInt(result.rows[0].table_count);
        
        this.logResult(
          'Schema Validation',
          tableCount > 0,
          `Found ${tableCount} tables in public schema`,
          { tableCount }
        );
      }

    } catch (error) {
      this.logResult('Database Connection', false, error.message);
    }
  }

  /**
   * Test connection pool functionality
   */
  async testConnectionPool() {
    console.log('\n🏊 Testing Connection Pool...');

    try {
      // Test multiple concurrent connections
      const concurrentQueries = Array.from({ length: 10 }, (_, i) => 
        connectionPool.query('SELECT $1 as query_id, pg_sleep(0.1)', [i])
      );

      const startTime = Date.now();
      const results = await Promise.all(concurrentQueries);
      const duration = Date.now() - startTime;

      this.logResult(
        'Concurrent Queries',
        results.length === 10,
        `Executed ${results.length} concurrent queries in ${duration}ms`,
        { queryCount: results.length, duration }
      );

      // Test pool statistics
      const poolStats = connectionPool.getPoolStats();
      
      this.logResult(
        'Pool Statistics',
        poolStats.main.total > 0,
        `Pool usage: ${poolStats.main.usage}% (${poolStats.main.total}/${poolStats.main.max})`,
        poolStats
      );

      // Test transaction functionality
      const transactionResult = await connectionPool.transaction(async (client) => {
        const result1 = await client.query('SELECT 1 as test_value');
        const result2 = await client.query('SELECT 2 as test_value');
        return { result1: result1.rows[0], result2: result2.rows[0] };
      });

      this.logResult(
        'Transaction Support',
        transactionResult.result1.test_value === 1 && transactionResult.result2.test_value === 2,
        'Transaction executed successfully',
        transactionResult
      );

    } catch (error) {
      this.logResult('Connection Pool', false, error.message);
    }
  }

  /**
   * Test database monitoring system
   */
  async testDatabaseMonitoring() {
    console.log('\n📊 Testing Database Monitoring...');

    try {
      const monitor = new DatabaseMonitor();
      
      // Test connection pool monitoring
      const poolHealth = await monitor.monitorConnectionPool();
      
      this.logResult(
        'Connection Pool Monitoring',
        poolHealth.status !== 'ERROR',
        `Pool health: ${poolHealth.status}`,
        { usagePercentage: poolHealth.usagePercentage }
      );

      // Test query performance monitoring
      const queryPerformance = await monitor.monitorQueryPerformance();
      
      this.logResult(
        'Query Performance Monitoring',
        queryPerformance.status !== 'ERROR',
        `Query monitoring: ${queryPerformance.status}`,
        { statsEnabled: queryPerformance.statsEnabled }
      );

      // Test security event monitoring
      const securityEvents = await monitor.monitorSecurityEvents();
      
      this.logResult(
        'Security Event Monitoring',
        securityEvents.status !== 'ERROR',
        `Security monitoring: ${securityEvents.status}`,
        { failedLogins: securityEvents.failedLogins }
      );

      // Test system health monitoring
      const systemHealth = await monitor.monitorSystemHealth();
      
      this.logResult(
        'System Health Monitoring',
        systemHealth.status !== 'ERROR',
        `System health: ${systemHealth.status}`,
        { databaseSize: systemHealth.databaseSize?.database_size }
      );

      // Calculate overall health score
      const healthScore = monitor.calculateHealthScore();
      
      this.logResult(
        'Health Score Calculation',
        healthScore.score > 0,
        `Overall health score: ${healthScore.score}/100 (${healthScore.status})`,
        { score: healthScore.score, issues: healthScore.issues }
      );

      await monitor.pool.end();

    } catch (error) {
      this.logResult('Database Monitoring', false, error.message);
    }
  }

  /**
   * Test performance analysis system
   */
  async testPerformanceAnalysis() {
    console.log('\n⚡ Testing Performance Analysis...');

    try {
      const analyzer = new DatabasePerformanceAnalyzer();

      // Test table size analysis
      const tableSizes = await analyzer.analyzeTableSizes();
      
      this.logResult(
        'Table Size Analysis',
        tableSizes.tableSizes && tableSizes.tableSizes.length > 0,
        `Analyzed ${tableSizes.tableSizes?.length || 0} tables`,
        { tableCount: tableSizes.tableSizes?.length }
      );

      // Test index usage analysis
      const indexUsage = await analyzer.analyzeIndexUsage();
      
      this.logResult(
        'Index Usage Analysis',
        indexUsage.indexUsage && indexUsage.indexUsage.length > 0,
        `Analyzed ${indexUsage.indexUsage?.length || 0} indexes`,
        { indexCount: indexUsage.indexUsage?.length }
      );

      // Test multi-tenant query performance
      const multiTenantQueries = await analyzer.testMultiTenantQueries();
      
      const avgExecutionTime = multiTenantQueries
        .filter(q => q.executionTime)
        .reduce((sum, q) => sum + q.executionTime, 0) / multiTenantQueries.length;

      this.logResult(
        'Multi-Tenant Query Performance',
        avgExecutionTime < 100, // Less than 100ms average
        `Average query time: ${avgExecutionTime.toFixed(2)}ms`,
        { avgExecutionTime, queryCount: multiTenantQueries.length }
      );

      await analyzer.pool.end();

    } catch (error) {
      this.logResult('Performance Analysis', false, error.message);
    }
  }

  /**
   * Test configuration optimization
   */
  async testConfigurationOptimization() {
    console.log('\n🔧 Testing Configuration Optimization...');

    try {
      const optimizer = new PostgreSQLConfigOptimizer();
      
      // Test system info gathering
      const systemInfo = optimizer.getSystemInfo();
      
      this.logResult(
        'System Information Gathering',
        systemInfo.totalMemoryGB > 0 && systemInfo.cpuCores > 0,
        `System: ${systemInfo.totalMemoryGB}GB RAM, ${systemInfo.cpuCores} CPU cores`,
        systemInfo
      );

      // Test current config retrieval
      const currentConfig = await optimizer.getCurrentConfig();
      
      this.logResult(
        'Current Configuration Retrieval',
        currentConfig && currentConfig.length > 0,
        `Retrieved ${currentConfig.length} configuration parameters`,
        { paramCount: currentConfig.length }
      );

      // Test optimized config calculation
      const optimizedConfig = optimizer.calculateOptimizedConfig();
      
      this.logResult(
        'Optimized Configuration Calculation',
        optimizedConfig && optimizedConfig.max_connections > 0,
        `Generated optimized config with ${optimizedConfig.max_connections} max connections`,
        { maxConnections: optimizedConfig.max_connections }
      );

      await optimizer.pool.end();

    } catch (error) {
      this.logResult('Configuration Optimization', false, error.message);
    }
  }

  /**
   * Test database security features
   */
  async testSecurityFeatures() {
    console.log('\n🛡️ Testing Security Features...');

    try {
      // Test audit logging
      const auditResult = await connectionPool.query(`
        SELECT COUNT(*) as audit_count 
        FROM security_audit_log 
        WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
      `);

      this.logResult(
        'Audit Logging',
        auditResult.rows[0].audit_count >= 0,
        `Found ${auditResult.rows[0].audit_count} audit entries in last 24 hours`,
        { auditCount: parseInt(auditResult.rows[0].audit_count) }
      );

      // Test session management
      const sessionResult = await connectionPool.query(`
        SELECT COUNT(*) as session_count 
        FROM user_sessions 
        WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
      `);

      this.logResult(
        'Session Management',
        sessionResult.rows[0].session_count >= 0,
        `Found ${sessionResult.rows[0].session_count} sessions in last 24 hours`,
        { sessionCount: parseInt(sessionResult.rows[0].session_count) }
      );

      // Test RBAC system
      const rbacResult = await connectionPool.query(`
        SELECT COUNT(*) as role_count 
        FROM user_roles
      `);

      this.logResult(
        'RBAC System',
        rbacResult.rows[0].role_count >= 0,
        `Found ${rbacResult.rows[0].role_count} user roles configured`,
        { roleCount: parseInt(rbacResult.rows[0].role_count) }
      );

    } catch (error) {
      this.logResult('Security Features', false, error.message);
    }
  }

  /**
   * Run all database tests
   */
  async runAllTests() {
    console.log('🚀 Starting Comprehensive Database Setup Tests...\n');
    console.log(`🕐 Test started at: ${new Date().toISOString()}`);

    await this.testDatabaseConnectivity();
    await this.testConnectionPool();
    await this.testDatabaseMonitoring();
    await this.testPerformanceAnalysis();
    await this.testConfigurationOptimization();
    await this.testSecurityFeatures();

    // Generate summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = Date.now() - this.startTime;

    console.log('\n📊 Database Setup Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`⏱️ Total Duration: ${totalDuration}ms`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    // Shutdown connection pool
    await connectionPool.shutdown();

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      duration: totalDuration,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new DatabaseSetupTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Database setup testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { DatabaseSetupTester };
