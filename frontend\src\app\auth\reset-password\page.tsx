'use client'

import React from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { FormField } from '@/components/forms/FormField'
import { ArrowLeft, Lock, CheckCircle, AlertCircle } from 'lucide-react'

const resetPasswordSchema = Yup.object().shape({
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
})

interface ResetPasswordFormValues {
  password: string
  confirmPassword: string
}

export default function ResetPasswordPage() {
  const searchParams = useSearchParams()
  const [isTokenValid, setIsTokenValid] = React.useState<boolean | null>(null)
  const [isPasswordReset, setIsPasswordReset] = React.useState(false)
  
  const token = searchParams.get('token')
  const email = searchParams.get('email')

  React.useEffect(() => {
    // Validate token on component mount
    const validateToken = async () => {
      if (!token) {
        setIsTokenValid(false)
        return
      }

      try {
        // TODO: Replace with actual API call to validate token
        console.log('Validating reset token:', token)
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Mock token validation (in real app, this would call the backend)
        setIsTokenValid(true)
        
      } catch (error) {
        console.error('Token validation error:', error)
        setIsTokenValid(false)
      }
    }

    validateToken()
  }, [token])

  const handleSubmit = async (values: ResetPasswordFormValues, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Reset password attempt:', { token, email, password: values.password })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock successful password reset
      setIsPasswordReset(true)
      toast.success('Password reset successfully!')
      
    } catch (error) {
      console.error('Reset password error:', error)
      toast.error('Failed to reset password. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  // Loading state
  if (isTokenValid === null) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Validating reset link...</p>
        </div>
      </div>
    )
  }

  // Invalid token
  if (isTokenValid === false) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 text-center">Invalid reset link</h1>
          <p className="text-gray-600 text-center">
            This password reset link is invalid or has expired.
          </p>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-red-50 rounded-lg border border-red-200">
            <h3 className="text-sm font-medium text-red-900 mb-2">Possible reasons:</h3>
            <ul className="space-y-1 text-sm text-red-700 list-disc list-inside">
              <li>The link has expired (links are valid for 24 hours)</li>
              <li>The link has already been used</li>
              <li>The link was copied incorrectly</li>
            </ul>
          </div>

          <div className="text-center space-y-2">
            <Button asChild className="w-full">
              <Link href="/auth/forgot-password">
                Request new reset link
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/auth/login">
                Back to login
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Password reset successful
  if (isPasswordReset) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 text-center">Password reset successful!</h1>
          <p className="text-gray-600 text-center">
            Your password has been updated successfully.
          </p>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="text-sm font-medium text-green-900 mb-2">What's next?</h3>
            <p className="text-sm text-green-700">
              You can now sign in to your account using your new password.
            </p>
          </div>

          <Button asChild className="w-full">
            <Link href="/auth/login">
              Sign in to your account
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  // Reset password form
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
          <Link 
            href="/auth/login" 
            className="flex items-center space-x-1 hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to login</span>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900">Reset your password</h1>
        <p className="text-gray-600">
          {email ? `Resetting password for ${email}` : 'Enter your new password below'}
        </p>
      </div>

      {/* Reset Password Form */}
      <Formik
        initialValues={{
          password: '',
          confirmPassword: '',
        }}
        validationSchema={resetPasswordSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, isValid }) => (
          <Form className="space-y-4">
            <FormField
              name="password"
              label="New password"
              type="password"
              placeholder="Enter your new password"
              autoComplete="new-password"
              description="Must be at least 8 characters with uppercase, lowercase, number, and special character"
              required
            />

            <FormField
              name="confirmPassword"
              label="Confirm new password"
              type="password"
              placeholder="Confirm your new password"
              autoComplete="new-password"
              required
            />

            <Button
              type="submit"
              className="w-full"
              loading={isSubmitting}
              disabled={!isValid}
            >
              <Lock className="mr-2 h-4 w-4" />
              Update password
            </Button>
          </Form>
        )}
      </Formik>

      {/* Security notice */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Security tips</h3>
        <ul className="space-y-1 text-sm text-blue-700 list-disc list-inside">
          <li>Use a unique password that you don't use elsewhere</li>
          <li>Include a mix of letters, numbers, and special characters</li>
          <li>Consider using a password manager</li>
          <li>Don't share your password with anyone</li>
        </ul>
      </div>
    </div>
  )
}
