export enum InstituteStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
}

export enum SubscriptionPlan {
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

export enum DomainStatus {
  PENDING = 'pending',
  VERIFYING = 'verifying',
  VERIFIED = 'verified',
  FAILED = 'failed',
  SSL_PENDING = 'ssl_pending',
  SSL_ACTIVE = 'ssl_active',
  SSL_FAILED = 'ssl_failed',
}

export interface Institute {
  id: string
  name: string
  email: string
  website?: string | undefined
  description?: string | undefined
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  } | undefined
  phone?: string | undefined
  status: InstituteStatus
  subscriptionPlan: SubscriptionPlan
  customDomain?: string | undefined
  domainStatus?: DomainStatus | undefined
  domainVerificationToken?: string | undefined
  sslCertificateId?: string | undefined
  branding?: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    logoUrl?: string | undefined
    faviconUrl?: string | undefined
  } | undefined
  settings?: {
    timezone: string
    language: string
    allowSelfRegistration: boolean
    requireEmailVerification: boolean
    requireApproval: boolean
    defaultRole: string
  } | undefined
  adminUserId: string
  createdAt: Date
  updatedAt: Date
  verifiedAt?: Date | undefined
  suspendedAt?: Date | undefined
}

export interface CreateInstituteRequest {
  name: string
  email: string
  website?: string
  description?: string
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  phone?: string
  subscriptionPlan: SubscriptionPlan
  adminUser: {
    firstName: string
    lastName: string
    email: string
    password: string
  }
}

export interface UpdateInstituteRequest {
  name?: string
  email?: string
  website?: string
  description?: string
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  phone?: string
  subscriptionPlan?: SubscriptionPlan
  branding?: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    logoUrl?: string
    faviconUrl?: string
  }
  settings?: {
    timezone: string
    language: string
    allowSelfRegistration: boolean
    requireEmailVerification: boolean
    requireApproval: boolean
    defaultRole: string
  }
}

export interface CustomDomainRequest {
  domain: string
}

export interface DomainVerificationRequest {
  domain: string
}

export interface DomainVerificationResponse {
  success: boolean
  domain: string
  status: DomainStatus
  verificationToken: string
  instructions: {
    type: 'TXT'
    name: string
    value: string
    ttl: number
  }
  nextSteps: string[]
}

export interface SSLCertificateInfo {
  domain: string
  issuer: string
  validFrom: Date
  validTo: Date
  status: 'active' | 'expired' | 'pending' | 'failed'
  autoRenew: boolean
}

export interface InstituteAnalytics {
  totalUsers: number
  totalCourses: number
  totalEnrollments: number
  activeUsers: number
  completionRate: number
  averageGrade: number
  recentActivity: {
    type: string
    count: number
    date: string
  }[]
  userGrowth: {
    month: string
    users: number
  }[]
  coursePopularity: {
    courseId: string
    courseName: string
    enrollments: number
  }[]
}

export interface InstituteBranding {
  primaryColor: string
  secondaryColor: string
  accentColor: string
  logoUrl?: string
  faviconUrl?: string
  customCSS?: string
  headerText?: string
  footerText?: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
}

export interface InstituteSettings {
  timezone: string
  language: string
  dateFormat: string
  timeFormat: string
  allowSelfRegistration: boolean
  requireEmailVerification: boolean
  requireApproval: boolean
  defaultRole: string
  maxStudentsPerCourse: number
  allowGuestAccess: boolean
  enableNotifications: boolean
  enableAnalytics: boolean
  maintenanceMode: boolean
  customTermsUrl?: string
  customPrivacyUrl?: string
}

export interface DomainConfiguration {
  domain: string
  subdomain?: string
  status: DomainStatus
  verificationToken: string
  verificationMethod: 'TXT' | 'CNAME' | 'FILE'
  sslEnabled: boolean
  sslCertificateId?: string
  sslAutoRenew: boolean
  redirectWww: boolean
  forceHttps: boolean
  createdAt: Date
  verifiedAt?: Date
  lastCheckedAt?: Date
  errorMessage?: string
}

// Validation schemas
export const DOMAIN_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
export const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/
export const COLOR_REGEX = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/

export const SUPPORTED_TIMEZONES = [
  'UTC',
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Asia/Kolkata',
  'Australia/Sydney',
]

export const SUPPORTED_LANGUAGES = [
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi'
]

export const SUBSCRIPTION_LIMITS = {
  [SubscriptionPlan.BASIC]: {
    maxUsers: 100,
    maxCourses: 10,
    maxStorage: 1024 * 1024 * 1024, // 1GB
    customDomain: false,
    analytics: false,
    support: 'email',
  },
  [SubscriptionPlan.PREMIUM]: {
    maxUsers: 1000,
    maxCourses: 100,
    maxStorage: 10 * 1024 * 1024 * 1024, // 10GB
    customDomain: true,
    analytics: true,
    support: 'priority',
  },
  [SubscriptionPlan.ENTERPRISE]: {
    maxUsers: -1, // unlimited
    maxCourses: -1, // unlimited
    maxStorage: -1, // unlimited
    customDomain: true,
    analytics: true,
    support: 'dedicated',
  },
}
