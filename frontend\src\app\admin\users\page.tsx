'use client'

import React, { useState, useEffect } from 'react'
import { 
  Users, 
  Search, 
  Filter, 
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Mail,
  Phone,
  Calendar,
  Edit,
  Trash2,
  UserCheck,
  UserX
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { AdminRoute } from '@/components/auth/ProtectedRoute'

enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  DEACTIVATED = 'deactivated',
}

enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  INSTITUTE_ADMIN = 'institute_admin',
  SUPER_ADMIN = 'super_admin',
}

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  status: UserStatus
  emailVerified: boolean
  lastLoginAt?: string
  createdAt: string
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
  }
}

function UserManagementPage() {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<UserStatus | 'all'>('all')
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock users data
      const mockUsers: User[] = [
        {
          id: '1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.STUDENT,
          status: UserStatus.ACTIVE,
          emailVerified: true,
          lastLoginAt: '2023-12-01T10:30:00Z',
          createdAt: '2023-01-15T00:00:00Z',
          metadata: {
            studentId: 'STU123456',
            department: 'Computer Science',
          },
        },
        {
          id: '2',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.TEACHER,
          status: UserStatus.ACTIVE,
          emailVerified: true,
          lastLoginAt: '2023-12-01T09:15:00Z',
          createdAt: '2023-02-01T00:00:00Z',
          metadata: {
            employeeId: 'EMP789012',
            department: 'Mathematics',
          },
        },
        {
          id: '3',
          email: '<EMAIL>',
          firstName: 'Pending',
          lastName: 'User',
          role: UserRole.STUDENT,
          status: UserStatus.PENDING,
          emailVerified: false,
          createdAt: '2023-12-01T00:00:00Z',
          metadata: {
            studentId: 'STU789123',
            department: 'Physics',
          },
        },
        {
          id: '4',
          email: '<EMAIL>',
          firstName: 'Suspended',
          lastName: 'User',
          role: UserRole.STUDENT,
          status: UserStatus.SUSPENDED,
          emailVerified: true,
          lastLoginAt: '2023-11-15T14:20:00Z',
          createdAt: '2023-03-10T00:00:00Z',
          metadata: {
            studentId: 'STU456789',
            department: 'Chemistry',
          },
        },
      ]

      setUsers(mockUsers)
    } catch (error) {
      console.error('Failed to fetch users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: UserStatus) => {
    const statusConfig = {
      [UserStatus.ACTIVE]: {
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
        text: 'Active',
      },
      [UserStatus.PENDING]: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: Clock,
        text: 'Pending',
      },
      [UserStatus.SUSPENDED]: {
        color: 'bg-red-100 text-red-800',
        icon: XCircle,
        text: 'Suspended',
      },
      [UserStatus.DEACTIVATED]: {
        color: 'bg-gray-100 text-gray-800',
        icon: XCircle,
        text: 'Deactivated',
      },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const getRoleBadge = (role: UserRole) => {
    const roleConfig = {
      [UserRole.STUDENT]: {
        color: 'bg-blue-100 text-blue-800',
        text: 'Student',
      },
      [UserRole.TEACHER]: {
        color: 'bg-purple-100 text-purple-800',
        text: 'Teacher',
      },
      [UserRole.INSTITUTE_ADMIN]: {
        color: 'bg-orange-100 text-orange-800',
        text: 'Admin',
      },
      [UserRole.SUPER_ADMIN]: {
        color: 'bg-red-100 text-red-800',
        text: 'Super Admin',
      },
    }

    const config = roleConfig[role]

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    )
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.metadata?.studentId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.metadata?.employeeId?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    const matchesRole = roleFilter === 'all' || user.role === roleFilter

    return matchesSearch && matchesStatus && matchesRole
  })

  const handleUserAction = async (userId: string, action: string) => {
    try {
      // TODO: Replace with actual API calls
      console.log(`Performing ${action} on user ${userId}`)
      
      switch (action) {
        case 'activate':
          setUsers(users.map(user => 
            user.id === userId ? { ...user, status: UserStatus.ACTIVE } : user
          ))
          break
        case 'suspend':
          setUsers(users.map(user => 
            user.id === userId ? { ...user, status: UserStatus.SUSPENDED } : user
          ))
          break
        case 'deactivate':
          setUsers(users.map(user => 
            user.id === userId ? { ...user, status: UserStatus.DEACTIVATED } : user
          ))
          break
        case 'verify':
          setUsers(users.map(user => 
            user.id === userId ? { ...user, emailVerified: true } : user
          ))
          break
      }
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
    }
  }

  const handleBulkAction = async (action: string) => {
    try {
      // TODO: Replace with actual API call
      console.log(`Performing bulk ${action} on users:`, selectedUsers)
      
      // Update users based on action
      setUsers(users.map(user => {
        if (selectedUsers.includes(user.id)) {
          switch (action) {
            case 'activate':
              return { ...user, status: UserStatus.ACTIVE }
            case 'suspend':
              return { ...user, status: UserStatus.SUSPENDED }
            case 'deactivate':
              return { ...user, status: UserStatus.DEACTIVATED }
            default:
              return user
          }
        }
        return user
      }))
      
      setSelectedUsers([])
    } catch (error) {
      console.error(`Failed to perform bulk ${action}:`, error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600 mt-1">
            Manage student and teacher accounts
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <Users className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as UserStatus | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value={UserStatus.ACTIVE}>Active</option>
            <option value={UserStatus.PENDING}>Pending</option>
            <option value={UserStatus.SUSPENDED}>Suspended</option>
            <option value={UserStatus.DEACTIVATED}>Deactivated</option>
          </select>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as UserRole | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Roles</option>
            <option value={UserRole.STUDENT}>Students</option>
            <option value={UserRole.TEACHER}>Teachers</option>
            <option value={UserRole.INSTITUTE_ADMIN}>Admins</option>
          </select>

          <div className="text-sm text-gray-600 flex items-center">
            {filteredUsers.length} of {users.length} users
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">
              {selectedUsers.length} user(s) selected
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('activate')}
              >
                <UserCheck className="mr-1 h-3 w-3" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('suspend')}
              >
                <UserX className="mr-1 h-3 w-3" />
                Suspend
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedUsers([])}
              >
                Clear
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(user => user.id))
                      } else {
                        setSelectedUsers([])
                      }
                    }}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id])
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id))
                        }
                      }}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white font-semibold text-sm">
                          {user.firstName[0]}{user.lastName[0]}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {user.email}
                          {!user.emailVerified && (
                            <span className="ml-2 text-xs text-red-600">(Unverified)</span>
                          )}
                        </div>
                        {user.metadata?.studentId && (
                          <div className="text-xs text-gray-500">
                            ID: {user.metadata.studentId}
                          </div>
                        )}
                        {user.metadata?.employeeId && (
                          <div className="text-xs text-gray-500">
                            ID: {user.metadata.employeeId}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getRoleBadge(user.role)}
                    {user.metadata?.department && (
                      <div className="text-xs text-gray-500 mt-1">
                        {user.metadata.department}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(user.status)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {user.lastLoginAt ? (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(user.lastLoginAt).toLocaleDateString()}
                      </div>
                    ) : (
                      <span className="text-gray-400">Never</span>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      {user.status === UserStatus.PENDING && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUserAction(user.id, 'activate')}
                        >
                          <UserCheck className="h-3 w-3" />
                        </Button>
                      )}
                      {user.status === UserStatus.ACTIVE && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUserAction(user.id, 'suspend')}
                        >
                          <UserX className="h-3 w-3" />
                        </Button>
                      )}
                      {user.status === UserStatus.SUSPENDED && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUserAction(user.id, 'activate')}
                        >
                          <UserCheck className="h-3 w-3" />
                        </Button>
                      )}
                      <Button size="sm" variant="ghost">
                        <MoreVertical className="h-3 w-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  )
}

export default function UsersPage() {
  return (
    <AdminRoute>
      <UserManagementPage />
    </AdminRoute>
  )
}
