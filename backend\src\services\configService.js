const { query } = require('../database/connection');

/**
 * Institute Configuration Service
 * Manages institute-specific configurations with caching
 */

// Configuration cache
const configCache = new Map();
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes

/**
 * Default configuration values
 */
const DEFAULT_CONFIG = {
  // Theme settings
  theme_primary_color: '#1E40AF',
  theme_secondary_color: '#3B82F6',
  theme_logo_url: null,
  theme_favicon_url: null,
  
  // Institute settings
  institute_timezone: 'UTC',
  institute_language: 'en',
  institute_currency: 'USD',
  
  // Feature flags
  allow_student_registration: true,
  allow_teacher_registration: false,
  enable_email_verification: true,
  enable_two_factor_auth: false,
  
  // Limits
  max_students_per_course: 100,
  max_courses_per_teacher: 10,
  max_file_upload_size: 10485760, // 10MB in bytes
  
  // Email settings
  email_from_name: null,
  email_from_address: null,
  email_reply_to: null,
  
  // Social login
  enable_google_login: false,
  enable_microsoft_login: false,
  
  // Content settings
  default_course_visibility: 'private',
  allow_course_comments: true,
  allow_course_ratings: true,
  
  // Notification settings
  notify_new_enrollments: true,
  notify_assignment_submissions: true,
  notify_course_completions: true
};

class ConfigService {
  constructor(instituteId) {
    this.instituteId = instituteId;
  }

  /**
   * Get cache key for institute config
   */
  getCacheKey(key = null) {
    return key ? `config:${this.instituteId}:${key}` : `config:${this.instituteId}`;
  }

  /**
   * Load all configurations for institute
   */
  async loadAllConfigs() {
    if (!this.instituteId) {
      return { ...DEFAULT_CONFIG };
    }

    const cacheKey = this.getCacheKey();
    const cached = configCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return cached.data;
    }

    try {
      const result = await query(
        'SELECT setting_key, setting_value, setting_type FROM institute_settings WHERE institute_id = $1',
        [this.instituteId]
      );

      // Start with defaults
      const config = { ...DEFAULT_CONFIG };

      // Override with database values
      result.rows.forEach(row => {
        try {
          let value = JSON.parse(row.setting_value);
          
          // Type conversion based on setting_type
          switch (row.setting_type) {
            case 'number':
              value = Number(value);
              break;
            case 'boolean':
              value = Boolean(value);
              break;
            case 'string':
            default:
              value = String(value);
              break;
          }
          
          config[row.setting_key] = value;
        } catch (error) {
          console.warn(`Failed to parse config value for ${row.setting_key}:`, error.message);
          config[row.setting_key] = row.setting_value;
        }
      });

      // Cache the result
      configCache.set(cacheKey, {
        data: config,
        timestamp: Date.now()
      });

      return config;
    } catch (error) {
      console.error('Error loading institute configs:', error.message);
      return { ...DEFAULT_CONFIG };
    }
  }

  /**
   * Get specific configuration value
   */
  async get(key, defaultValue = null) {
    const config = await this.loadAllConfigs();
    return config[key] !== undefined ? config[key] : (defaultValue !== null ? defaultValue : DEFAULT_CONFIG[key]);
  }

  /**
   * Set configuration value
   */
  async set(key, value, type = null, isPublic = false) {
    if (!this.instituteId) {
      throw new Error('Cannot set configuration without institute context');
    }

    // Auto-detect type if not provided
    if (!type) {
      if (typeof value === 'boolean') {
        type = 'boolean';
      } else if (typeof value === 'number') {
        type = 'number';
      } else {
        type = 'string';
      }
    }

    try {
      const result = await query(`
        INSERT INTO institute_settings (institute_id, setting_key, setting_value, setting_type, is_public)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (institute_id, setting_key) 
        DO UPDATE SET 
          setting_value = EXCLUDED.setting_value,
          setting_type = EXCLUDED.setting_type,
          is_public = EXCLUDED.is_public,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [this.instituteId, key, JSON.stringify(value), type, isPublic]);

      // Invalidate cache
      this.invalidateCache();

      return result.rows[0];
    } catch (error) {
      console.error('Error setting configuration:', error.message);
      throw error;
    }
  }

  /**
   * Set multiple configuration values
   */
  async setMultiple(configs) {
    if (!this.instituteId) {
      throw new Error('Cannot set configuration without institute context');
    }

    const results = [];
    
    for (const [key, config] of Object.entries(configs)) {
      const { value, type = null, isPublic = false } = config;
      const result = await this.set(key, value, type, isPublic);
      results.push(result);
    }

    return results;
  }

  /**
   * Get public configurations (accessible to students/teachers)
   */
  async getPublicConfigs() {
    if (!this.instituteId) {
      return {};
    }

    const cacheKey = this.getCacheKey('public');
    const cached = configCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return cached.data;
    }

    try {
      const result = await query(
        'SELECT setting_key, setting_value, setting_type FROM institute_settings WHERE institute_id = $1 AND is_public = true',
        [this.instituteId]
      );

      const publicConfig = {};
      result.rows.forEach(row => {
        try {
          publicConfig[row.setting_key] = JSON.parse(row.setting_value);
        } catch {
          publicConfig[row.setting_key] = row.setting_value;
        }
      });

      // Cache the result
      configCache.set(cacheKey, {
        data: publicConfig,
        timestamp: Date.now()
      });

      return publicConfig;
    } catch (error) {
      console.error('Error loading public configs:', error.message);
      return {};
    }
  }

  /**
   * Get theme configuration
   */
  async getThemeConfig() {
    const config = await this.loadAllConfigs();
    
    return {
      primaryColor: config.theme_primary_color,
      secondaryColor: config.theme_secondary_color,
      logoUrl: config.theme_logo_url,
      faviconUrl: config.theme_favicon_url
    };
  }

  /**
   * Update theme configuration
   */
  async updateThemeConfig(themeConfig) {
    const updates = {};
    
    if (themeConfig.primaryColor) {
      updates.theme_primary_color = { value: themeConfig.primaryColor, type: 'string', isPublic: true };
    }
    
    if (themeConfig.secondaryColor) {
      updates.theme_secondary_color = { value: themeConfig.secondaryColor, type: 'string', isPublic: true };
    }
    
    if (themeConfig.logoUrl !== undefined) {
      updates.theme_logo_url = { value: themeConfig.logoUrl, type: 'string', isPublic: true };
    }
    
    if (themeConfig.faviconUrl !== undefined) {
      updates.theme_favicon_url = { value: themeConfig.faviconUrl, type: 'string', isPublic: true };
    }

    return await this.setMultiple(updates);
  }

  /**
   * Get feature flags
   */
  async getFeatureFlags() {
    const config = await this.loadAllConfigs();
    
    return {
      allowStudentRegistration: config.allow_student_registration,
      allowTeacherRegistration: config.allow_teacher_registration,
      enableEmailVerification: config.enable_email_verification,
      enableTwoFactorAuth: config.enable_two_factor_auth,
      enableGoogleLogin: config.enable_google_login,
      enableMicrosoftLogin: config.enable_microsoft_login,
      allowCourseComments: config.allow_course_comments,
      allowCourseRatings: config.allow_course_ratings
    };
  }

  /**
   * Update feature flags
   */
  async updateFeatureFlags(flags) {
    const updates = {};
    
    Object.entries(flags).forEach(([key, value]) => {
      const configKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      updates[configKey] = { value, type: 'boolean', isPublic: false };
    });

    return await this.setMultiple(updates);
  }

  /**
   * Invalidate cache for this institute
   */
  invalidateCache() {
    const keys = Array.from(configCache.keys()).filter(key => key.startsWith(`config:${this.instituteId}`));
    keys.forEach(key => configCache.delete(key));
    console.log(`🧹 Invalidated config cache for institute ${this.instituteId}`);
  }

  /**
   * Get configuration schema (for validation)
   */
  static getConfigSchema() {
    return {
      theme_primary_color: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$', public: true },
      theme_secondary_color: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$', public: true },
      theme_logo_url: { type: 'string', public: true },
      theme_favicon_url: { type: 'string', public: true },
      institute_timezone: { type: 'string', public: false },
      institute_language: { type: 'string', public: true },
      institute_currency: { type: 'string', public: true },
      allow_student_registration: { type: 'boolean', public: false },
      allow_teacher_registration: { type: 'boolean', public: false },
      enable_email_verification: { type: 'boolean', public: false },
      enable_two_factor_auth: { type: 'boolean', public: false },
      max_students_per_course: { type: 'number', min: 1, max: 1000, public: false },
      max_courses_per_teacher: { type: 'number', min: 1, max: 100, public: false },
      max_file_upload_size: { type: 'number', min: 1048576, max: 104857600, public: false } // 1MB to 100MB
    };
  }
}

/**
 * Factory function to create config service
 */
const createConfigService = (req) => {
  const instituteId = req.tenant?.instituteId || null;
  return new ConfigService(instituteId);
};

/**
 * Clear all configuration cache
 */
const clearConfigCache = () => {
  configCache.clear();
  console.log('🧹 Configuration cache cleared');
};

/**
 * Get cache statistics
 */
const getConfigCacheStats = () => {
  return {
    size: configCache.size,
    entries: Array.from(configCache.keys())
  };
};

module.exports = {
  ConfigService,
  createConfigService,
  clearConfigCache,
  getConfigCacheStats,
  DEFAULT_CONFIG
};
