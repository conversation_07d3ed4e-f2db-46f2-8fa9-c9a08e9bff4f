const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

/**
 * Database Monitoring and Health Check System
 * Monitors database performance, connection health, and security metrics
 */

class DatabaseMonitor {
  constructor() {
    this.pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'lte_lms',
      password: process.env.DB_PASSWORD || '1234',
      port: process.env.DB_PORT || 5432,
    });

    this.metrics = {
      connectionPool: {},
      queryPerformance: {},
      securityEvents: {},
      systemHealth: {}
    };

    this.thresholds = {
      maxConnectionPoolUsage: 80, // %
      maxQueryTime: 1000, // ms
      maxFailedLogins: 10, // per hour
      maxDiskUsage: 85 // %
    };
  }

  /**
   * Monitor connection pool health
   */
  async monitorConnectionPool() {
    try {
      const poolStats = {
        totalConnections: this.pool.totalCount,
        idleConnections: this.pool.idleCount,
        waitingClients: this.pool.waitingCount,
        maxConnections: this.pool.options.max || 20
      };

      const usagePercentage = ((poolStats.totalConnections / poolStats.maxConnections) * 100).toFixed(2);

      // Query active connections from database
      const activeConnectionsQuery = `
        SELECT 
          state,
          COUNT(*) as count,
          AVG(EXTRACT(EPOCH FROM (now() - state_change))) as avg_duration
        FROM pg_stat_activity 
        WHERE pid != pg_backend_pid()
        GROUP BY state;
      `;

      const result = await this.pool.query(activeConnectionsQuery);
      const activeConnections = result.rows;

      this.metrics.connectionPool = {
        ...poolStats,
        usagePercentage: parseFloat(usagePercentage),
        activeConnections,
        timestamp: new Date().toISOString(),
        status: usagePercentage > this.thresholds.maxConnectionPoolUsage ? 'WARNING' : 'HEALTHY'
      };

      return this.metrics.connectionPool;

    } catch (error) {
      console.error('Error monitoring connection pool:', error.message);
      this.metrics.connectionPool = {
        status: 'ERROR',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      return this.metrics.connectionPool;
    }
  }

  /**
   * Monitor query performance metrics
   */
  async monitorQueryPerformance() {
    try {
      // Check if pg_stat_statements is available
      const statsEnabledQuery = `
        SELECT EXISTS (
          SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
        ) as enabled;
      `;

      const statsResult = await this.pool.query(statsEnabledQuery);
      const statsEnabled = statsResult.rows[0].enabled;

      let queryStats = {};

      if (statsEnabled) {
        const performanceQuery = `
          SELECT 
            COUNT(*) as total_queries,
            AVG(mean_time) as avg_query_time,
            MAX(mean_time) as max_query_time,
            SUM(calls) as total_calls,
            AVG(100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0)) as cache_hit_ratio
          FROM pg_stat_statements 
          WHERE query NOT LIKE '%pg_stat_statements%';
        `;

        const slowQueriesQuery = `
          SELECT 
            LEFT(query, 100) as query_preview,
            calls,
            mean_time,
            total_time
          FROM pg_stat_statements 
          WHERE mean_time > $1
          AND query NOT LIKE '%pg_stat_statements%'
          ORDER BY mean_time DESC 
          LIMIT 5;
        `;

        const [perfResult, slowResult] = await Promise.all([
          this.pool.query(performanceQuery),
          this.pool.query(slowQueriesQuery, [this.thresholds.maxQueryTime])
        ]);

        queryStats = {
          ...perfResult.rows[0],
          slowQueries: slowResult.rows,
          statsEnabled: true
        };
      } else {
        queryStats = {
          statsEnabled: false,
          message: 'pg_stat_statements extension not available'
        };
      }

      // Monitor database locks
      const locksQuery = `
        SELECT 
          mode,
          COUNT(*) as count
        FROM pg_locks 
        WHERE NOT granted 
        GROUP BY mode;
      `;

      const locksResult = await this.pool.query(locksQuery);

      this.metrics.queryPerformance = {
        ...queryStats,
        locks: locksResult.rows,
        timestamp: new Date().toISOString(),
        status: queryStats.avg_query_time > this.thresholds.maxQueryTime ? 'WARNING' : 'HEALTHY'
      };

      return this.metrics.queryPerformance;

    } catch (error) {
      console.error('Error monitoring query performance:', error.message);
      this.metrics.queryPerformance = {
        status: 'ERROR',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      return this.metrics.queryPerformance;
    }
  }

  /**
   * Monitor security events and threats
   */
  async monitorSecurityEvents() {
    try {
      const securityQuery = `
        SELECT 
          event_type,
          severity,
          COUNT(*) as count,
          MAX(created_at) as latest_event
        FROM security_audit_log 
        WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
        GROUP BY event_type, severity
        ORDER BY count DESC;
      `;

      const failedLoginsQuery = `
        SELECT 
          COUNT(*) as failed_logins_last_hour,
          COUNT(DISTINCT ip_address) as unique_ips
        FROM failed_login_attempts 
        WHERE attempt_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour';
      `;

      const blockedIpsQuery = `
        SELECT 
          COUNT(*) as currently_blocked,
          COUNT(CASE WHEN expires_at > CURRENT_TIMESTAMP THEN 1 END) as active_blocks
        FROM blocked_ips;
      `;

      const suspiciousActivityQuery = `
        SELECT 
          ip_address,
          COUNT(*) as event_count,
          array_agg(DISTINCT event_type) as event_types
        FROM security_audit_log 
        WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
        AND severity IN ('high', 'critical')
        GROUP BY ip_address
        HAVING COUNT(*) > 5
        ORDER BY event_count DESC
        LIMIT 10;
      `;

      const [securityResult, failedLoginsResult, blockedIpsResult, suspiciousResult] = await Promise.all([
        this.pool.query(securityQuery),
        this.pool.query(failedLoginsQuery),
        this.pool.query(blockedIpsQuery),
        this.pool.query(suspiciousActivityQuery)
      ]);

      const failedLogins = failedLoginsResult.rows[0].failed_logins_last_hour;

      this.metrics.securityEvents = {
        recentEvents: securityResult.rows,
        failedLogins: parseInt(failedLogins),
        blockedIps: blockedIpsResult.rows[0],
        suspiciousActivity: suspiciousResult.rows,
        timestamp: new Date().toISOString(),
        status: failedLogins > this.thresholds.maxFailedLogins ? 'WARNING' : 'HEALTHY'
      };

      return this.metrics.securityEvents;

    } catch (error) {
      console.error('Error monitoring security events:', error.message);
      this.metrics.securityEvents = {
        status: 'ERROR',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      return this.metrics.securityEvents;
    }
  }

  /**
   * Monitor system health and resources
   */
  async monitorSystemHealth() {
    try {
      const databaseSizeQuery = `
        SELECT 
          pg_size_pretty(pg_database_size(current_database())) as database_size,
          pg_database_size(current_database()) as database_size_bytes;
      `;

      const tablespaceSizeQuery = `
        SELECT 
          spcname as tablespace_name,
          pg_size_pretty(pg_tablespace_size(spcname)) as size
        FROM pg_tablespace;
      `;

      const replicationStatusQuery = `
        SELECT 
          client_addr,
          state,
          sync_state,
          replay_lag
        FROM pg_stat_replication;
      `;

      const vacuumStatsQuery = `
        SELECT
          schemaname,
          relname as tablename,
          last_vacuum,
          last_autovacuum,
          vacuum_count,
          autovacuum_count,
          n_dead_tup
        FROM pg_stat_user_tables
        WHERE n_dead_tup > 1000
        ORDER BY n_dead_tup DESC
        LIMIT 10;
      `;

      const [sizeResult, tablespaceResult, replicationResult, vacuumResult] = await Promise.all([
        this.pool.query(databaseSizeQuery),
        this.pool.query(tablespaceSizeQuery),
        this.pool.query(replicationStatusQuery),
        this.pool.query(vacuumStatsQuery)
      ]);

      this.metrics.systemHealth = {
        databaseSize: sizeResult.rows[0],
        tablespaces: tablespaceResult.rows,
        replication: replicationResult.rows,
        vacuumStats: vacuumResult.rows,
        timestamp: new Date().toISOString(),
        status: 'HEALTHY'
      };

      return this.metrics.systemHealth;

    } catch (error) {
      console.error('Error monitoring system health:', error.message);
      this.metrics.systemHealth = {
        status: 'ERROR',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      return this.metrics.systemHealth;
    }
  }

  /**
   * Generate health score based on all metrics
   */
  calculateHealthScore() {
    let score = 100;
    const issues = [];

    // Connection pool health
    if (this.metrics.connectionPool.status === 'WARNING') {
      score -= 20;
      issues.push('High connection pool usage');
    } else if (this.metrics.connectionPool.status === 'ERROR') {
      score -= 40;
      issues.push('Connection pool error');
    }

    // Query performance health
    if (this.metrics.queryPerformance.status === 'WARNING') {
      score -= 15;
      issues.push('Slow query performance');
    } else if (this.metrics.queryPerformance.status === 'ERROR') {
      score -= 30;
      issues.push('Query performance monitoring error');
    }

    // Security health
    if (this.metrics.securityEvents.status === 'WARNING') {
      score -= 25;
      issues.push('High security event activity');
    } else if (this.metrics.securityEvents.status === 'ERROR') {
      score -= 35;
      issues.push('Security monitoring error');
    }

    // System health
    if (this.metrics.systemHealth.status === 'ERROR') {
      score -= 30;
      issues.push('System health monitoring error');
    }

    return {
      score: Math.max(0, score),
      status: score >= 80 ? 'HEALTHY' : score >= 60 ? 'WARNING' : 'CRITICAL',
      issues
    };
  }

  /**
   * Run complete monitoring check
   */
  async runHealthCheck() {
    console.log('🏥 Starting database health check...\n');

    try {
      console.log('🔗 Monitoring connection pool...');
      await this.monitorConnectionPool();

      console.log('⚡ Monitoring query performance...');
      await this.monitorQueryPerformance();

      console.log('🛡️ Monitoring security events...');
      await this.monitorSecurityEvents();

      console.log('💾 Monitoring system health...');
      await this.monitorSystemHealth();

      const healthScore = this.calculateHealthScore();

      const report = {
        timestamp: new Date().toISOString(),
        healthScore,
        metrics: this.metrics
      };

      // Save monitoring report
      const reportPath = path.join(__dirname, '../reports/monitoring-report.json');
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

      console.log('\n📊 Database Health Summary:');
      console.log(`🏥 Health Score: ${healthScore.score}/100 (${healthScore.status})`);
      console.log(`🔗 Connection Pool: ${this.metrics.connectionPool.status}`);
      console.log(`⚡ Query Performance: ${this.metrics.queryPerformance.status}`);
      console.log(`🛡️ Security Events: ${this.metrics.securityEvents.status}`);
      console.log(`💾 System Health: ${this.metrics.systemHealth.status}`);

      if (healthScore.issues.length > 0) {
        console.log('\n⚠️ Issues Detected:');
        healthScore.issues.forEach((issue, index) => {
          console.log(`${index + 1}. ${issue}`);
        });
      }

      console.log(`\n📄 Report saved to: ${reportPath}`);

      return report;

    } catch (error) {
      console.error('❌ Health check failed:', error.message);
      throw error;
    } finally {
      await this.pool.end();
    }
  }

  /**
   * Set up continuous monitoring (for production use)
   */
  startContinuousMonitoring(intervalMinutes = 5) {
    console.log(`🔄 Starting continuous monitoring (every ${intervalMinutes} minutes)...`);

    const monitoringInterval = setInterval(async () => {
      try {
        await this.runHealthCheck();
      } catch (error) {
        console.error('Monitoring cycle failed:', error.message);
      }
    }, intervalMinutes * 60 * 1000);

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping continuous monitoring...');
      clearInterval(monitoringInterval);
      this.pool.end();
      process.exit(0);
    });

    return monitoringInterval;
  }
}

// Run health check if script is executed directly
if (require.main === module) {
  const monitor = new DatabaseMonitor();
  
  const args = process.argv.slice(2);
  if (args.includes('--continuous')) {
    const interval = parseInt(args[args.indexOf('--continuous') + 1]) || 5;
    monitor.startContinuousMonitoring(interval);
  } else {
    monitor.runHealthCheck()
      .then(() => {
        console.log('\n✅ Database health check completed successfully!');
        process.exit(0);
      })
      .catch(error => {
        console.error('❌ Database health check failed:', error.message);
        process.exit(1);
      });
  }
}

module.exports = { DatabaseMonitor };
