import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import toast from 'react-hot-toast'
import { User, LoginCredentials, RegisterData } from '@/types'

interface AuthState {
  // State
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  updateUser: (user: Partial<User>) => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
  refreshToken: () => Promise<void>
}

// Mock API functions (replace with actual API calls)
const mockApi = {
  login: async (credentials: LoginCredentials) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock user data based on email
    const mockUsers: Record<string, User> = {
      '<EMAIL>': {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin',
        instituteId: '',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      },
      '<EMAIL>': {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Institute',
        lastName: 'Admin',
        role: 'institute_admin',
        instituteId: 'inst-1',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      },
      '<EMAIL>': {
        id: '3',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Teacher',
        role: 'teacher',
        instituteId: 'inst-1',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      },
      '<EMAIL>': {
        id: '4',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Student',
        role: 'student',
        instituteId: 'inst-1',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      },
    }
    
    const user = mockUsers[credentials.email]
    if (!user || credentials.password !== 'password123') {
      throw new Error('Invalid credentials')
    }
    
    return {
      user,
      token: `mock-jwt-token-${user.id}`,
    }
  },
  
  register: async (data: RegisterData) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock registration success
    const newUser: User = {
      id: Math.random().toString(36).substr(2, 9),
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      role: data.role,
      instituteId: data.instituteId || '',
      isActive: true,
      isEmailVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    
    return {
      user: newUser,
      token: `mock-jwt-token-${newUser.id}`,
    }
  },
  
  checkAuth: async (token: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock token validation
    if (!token.startsWith('mock-jwt-token-')) {
      throw new Error('Invalid token')
    }
    
    const userId = token.replace('mock-jwt-token-', '')
    
    // Return mock user data
    return {
      id: userId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'student' as const,
      instituteId: 'inst-1',
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  },
  
  refreshToken: async (token: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Mock token refresh
    return `${token}-refreshed`
  },
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // Login action
      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true })
        
        try {
          const { user, token } = await mockApi.login(credentials)
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          })
          
          toast.success(`Welcome back, ${user.firstName}!`)
          
        } catch (error) {
          set({ isLoading: false })
          const message = error instanceof Error ? error.message : 'Login failed'
          toast.error(message)
          throw error
        }
      },

      // Register action
      register: async (data: RegisterData) => {
        set({ isLoading: true })
        
        try {
          const { user, token } = await mockApi.register(data)
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          })
          
          toast.success('Account created successfully!')
          
        } catch (error) {
          set({ isLoading: false })
          const message = error instanceof Error ? error.message : 'Registration failed'
          toast.error(message)
          throw error
        }
      },

      // Logout action
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
        
        toast.success('Logged out successfully')
      },

      // Update user action
      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData },
          })
        }
      },

      // Set loading action
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Check authentication action
      checkAuth: async () => {
        const { token } = get()
        
        if (!token) {
          set({ isAuthenticated: false, user: null })
          return
        }
        
        set({ isLoading: true })
        
        try {
          const user = await mockApi.checkAuth(token)
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          })
          
        } catch (error) {
          console.error('Auth check failed:', error)
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      },

      // Refresh token action
      refreshToken: async () => {
        const { token } = get()
        
        if (!token) {
          throw new Error('No token to refresh')
        }
        
        try {
          const newToken = await mockApi.refreshToken(token)
          
          set({ token: newToken })
          
        } catch (error) {
          console.error('Token refresh failed:', error)
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Selectors for easier access to specific state
export const useAuth = () => {
  const store = useAuthStore()
  return {
    user: store.user,
    token: store.token,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
  }
}

export const useAuthActions = () => {
  const store = useAuthStore()
  return {
    login: store.login,
    register: store.register,
    logout: store.logout,
    updateUser: store.updateUser,
    setLoading: store.setLoading,
    checkAuth: store.checkAuth,
    refreshToken: store.refreshToken,
  }
}
