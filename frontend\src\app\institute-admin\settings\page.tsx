'use client'

import React, { useState } from 'react'
import { 
  <PERSON><PERSON>s,
  Users,
  Shield,
  Bell,
  Globe,
  Database,
  Key,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { FormField, SelectField } from '@/components/forms/FormField'

const settingsSchema = Yup.object().shape({
  instituteName: Yup.string().required('Institute name is required'),
  description: Yup.string().max(500, 'Description must be less than 500 characters'),
  contactEmail: Yup.string().email('Valid email required').required('Contact email is required'),
  contactPhone: Yup.string().matches(/^[\+]?[1-9][\d]{0,15}$/, 'Valid phone number required'),
  website: Yup.string().url('Valid website URL required'),
  address: Yup.string().max(200, 'Address must be less than 200 characters'),
  timezone: Yup.string().required('Timezone is required'),
  language: Yup.string().required('Language is required'),
  maxStudents: Yup.number().min(1).max(50000).required('<PERSON> students is required'),
  maxTeachers: Yup.number().min(1).max(1000).required('Max teachers is required'),
  maxCourses: Yup.number().min(1).max(10000).required('Max courses is required'),
})

interface InstituteSettings {
  // General Information
  instituteName: string
  description: string
  contactEmail: string
  contactPhone: string
  website: string
  address: string
  timezone: string
  language: string
  
  // User Limits
  maxStudents: number
  maxTeachers: number
  maxCourses: number
  currentStudents: number
  currentTeachers: number
  currentCourses: number
  
  // Feature Toggles
  enableStudentRegistration: boolean
  enableTeacherRegistration: boolean
  enablePublicCourses: boolean
  enableCertificates: boolean
  enableDiscussions: boolean
  enableAssignments: boolean
  enableGrading: boolean
  enableReports: boolean
  enableIntegrations: boolean
  enableCustomDomain: boolean
  
  // Notification Settings
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  weeklyReports: boolean
  monthlyReports: boolean
  
  // Security Settings
  requireEmailVerification: boolean
  enableTwoFactor: boolean
  sessionTimeout: number
  passwordMinLength: number
  enforceStrongPasswords: boolean
}

export default function InstituteSettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [isSaving, setIsSaving] = useState(false)

  const initialSettings: InstituteSettings = {
    // General Information
    instituteName: 'Harvard University',
    description: 'A prestigious Ivy League research university located in Cambridge, Massachusetts.',
    contactEmail: '<EMAIL>',
    contactPhone: '******-495-1000',
    website: 'https://harvard.edu',
    address: 'Cambridge, MA 02138, United States',
    timezone: 'America/New_York',
    language: 'en',
    
    // User Limits
    maxStudents: 5000,
    maxTeachers: 500,
    maxCourses: 1000,
    currentStudents: 1247,
    currentTeachers: 89,
    currentCourses: 156,
    
    // Feature Toggles
    enableStudentRegistration: true,
    enableTeacherRegistration: false,
    enablePublicCourses: true,
    enableCertificates: true,
    enableDiscussions: true,
    enableAssignments: true,
    enableGrading: true,
    enableReports: true,
    enableIntegrations: false,
    enableCustomDomain: true,
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,
    monthlyReports: true,
    
    // Security Settings
    requireEmailVerification: true,
    enableTwoFactor: false,
    sessionTimeout: 60,
    passwordMinLength: 8,
    enforceStrongPasswords: true,
  }

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'limits', name: 'User Limits', icon: Users },
    { id: 'features', name: 'Features', icon: Zap },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
  ]

  const timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
    { value: 'Europe/Paris', label: 'Central European Time (CET)' },
    { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
    { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
    { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
    { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
  ]

  const languages = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'zh', label: 'Chinese' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'ar', label: 'Arabic' },
  ]

  const handleSubmit = async (values: InstituteSettings) => {
    setIsSaving(true)
    try {
      // TODO: Save settings to API
      console.log('Saving institute settings:', values)
      await new Promise(resolve => setTimeout(resolve, 1500))
      // Show success message
    } catch (error) {
      console.error('Save settings error:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const getUsagePercentage = (current: number, max: number) => {
    return Math.round((current / max) * 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100'
    if (percentage >= 75) return 'text-yellow-600 bg-yellow-100'
    return 'text-green-600 bg-green-100'
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Institute Settings</h1>
          <p className="text-gray-600 mt-1">
            Manage your institute's configuration and preferences
          </p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          <Formik
            initialValues={initialSettings}
            validationSchema={settingsSchema}
            onSubmit={handleSubmit}
          >
            {({ values, setFieldValue }) => (
              <Form className="space-y-6">
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">General Information</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="instituteName"
                        label="Institute Name"
                        placeholder="Harvard University"
                        required
                      />
                      
                      <FormField
                        name="contactEmail"
                        label="Contact Email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>

                    <FormField
                      name="description"
                      label="Description"
                      as="textarea"
                      rows={3}
                      placeholder="Brief description of your institute..."
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="contactPhone"
                        label="Contact Phone"
                        placeholder="******-495-1000"
                      />
                      
                      <FormField
                        name="website"
                        label="Website"
                        placeholder="https://harvard.edu"
                      />
                    </div>

                    <FormField
                      name="address"
                      label="Address"
                      placeholder="Cambridge, MA 02138, United States"
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <SelectField
                        name="timezone"
                        label="Timezone"
                        options={timezones}
                        required
                      />
                      
                      <SelectField
                        name="language"
                        label="Default Language"
                        options={languages}
                        required
                      />
                    </div>
                  </div>
                )}

                {activeTab === 'limits' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">User Limits & Usage</h3>
                    
                    {/* Current Usage Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900">Students</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${getUsageColor(getUsagePercentage(values.currentStudents, values.maxStudents))}`}>
                            {getUsagePercentage(values.currentStudents, values.maxStudents)}%
                          </span>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">{values.currentStudents.toLocaleString()}</p>
                        <p className="text-sm text-gray-600">of {values.maxStudents.toLocaleString()} limit</p>
                        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all"
                            style={{ width: `${getUsagePercentage(values.currentStudents, values.maxStudents)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900">Teachers</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${getUsageColor(getUsagePercentage(values.currentTeachers, values.maxTeachers))}`}>
                            {getUsagePercentage(values.currentTeachers, values.maxTeachers)}%
                          </span>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">{values.currentTeachers}</p>
                        <p className="text-sm text-gray-600">of {values.maxTeachers} limit</p>
                        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full transition-all"
                            style={{ width: `${getUsagePercentage(values.currentTeachers, values.maxTeachers)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900">Courses</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${getUsageColor(getUsagePercentage(values.currentCourses, values.maxCourses))}`}>
                            {getUsagePercentage(values.currentCourses, values.maxCourses)}%
                          </span>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">{values.currentCourses}</p>
                        <p className="text-sm text-gray-600">of {values.maxCourses} limit</p>
                        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-purple-600 h-2 rounded-full transition-all"
                            style={{ width: `${getUsagePercentage(values.currentCourses, values.maxCourses)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* Limit Configuration */}
                    <div className="space-y-4">
                      <h4 className="text-md font-medium text-gray-900">Limit Configuration</h4>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          name="maxStudents"
                          label="Maximum Students"
                          type="number"
                          min="1"
                          max="50000"
                          required
                        />
                        
                        <FormField
                          name="maxTeachers"
                          label="Maximum Teachers"
                          type="number"
                          min="1"
                          max="1000"
                          required
                        />
                        
                        <FormField
                          name="maxCourses"
                          label="Maximum Courses"
                          type="number"
                          min="1"
                          max="10000"
                          required
                        />
                      </div>
                    </div>

                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex">
                        <Info className="h-5 w-5 text-blue-600 mr-2" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-800">Subscription Limits</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            Your current Premium plan allows up to 5,000 students, 500 teachers, and 1,000 courses. 
                            Contact support to upgrade your plan if you need higher limits.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'features' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Feature Management</h3>

                    <div className="space-y-6">
                      {/* Registration Features */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Registration & Access</h4>

                        <div className="space-y-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Student Registration</span>
                              <p className="text-xs text-gray-500">Allow new students to register for your institute</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableStudentRegistration}
                              onChange={(e) => setFieldValue('enableStudentRegistration', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Teacher Registration</span>
                              <p className="text-xs text-gray-500">Allow new teachers to apply for your institute</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableTeacherRegistration}
                              onChange={(e) => setFieldValue('enableTeacherRegistration', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Public Courses</span>
                              <p className="text-xs text-gray-500">Make courses visible to non-registered users</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enablePublicCourses}
                              onChange={(e) => setFieldValue('enablePublicCourses', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>

                      {/* Learning Features */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Learning Features</h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Certificates</span>
                              <p className="text-xs text-gray-500">Issue completion certificates</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableCertificates}
                              onChange={(e) => setFieldValue('enableCertificates', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Discussions</span>
                              <p className="text-xs text-gray-500">Course discussion forums</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableDiscussions}
                              onChange={(e) => setFieldValue('enableDiscussions', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Assignments</span>
                              <p className="text-xs text-gray-500">Create and manage assignments</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableAssignments}
                              onChange={(e) => setFieldValue('enableAssignments', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Grading</span>
                              <p className="text-xs text-gray-500">Grade assignments and exams</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableGrading}
                              onChange={(e) => setFieldValue('enableGrading', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>

                      {/* Advanced Features */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Advanced Features</h4>

                        <div className="space-y-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Analytics & Reports</span>
                              <p className="text-xs text-gray-500">Detailed analytics and reporting features</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableReports}
                              onChange={(e) => setFieldValue('enableReports', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Third-party Integrations</span>
                              <p className="text-xs text-gray-500">Connect with external tools and services</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableIntegrations}
                              onChange={(e) => setFieldValue('enableIntegrations', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Custom Domain</span>
                              <p className="text-xs text-gray-500">Use your own domain for the institute</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableCustomDomain}
                              onChange={(e) => setFieldValue('enableCustomDomain', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>

                    <div className="space-y-6">
                      {/* Communication Channels */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Communication Channels</h4>

                        <div className="space-y-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Email Notifications</span>
                              <p className="text-xs text-gray-500">Send notifications via email</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.emailNotifications}
                              onChange={(e) => setFieldValue('emailNotifications', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">SMS Notifications</span>
                              <p className="text-xs text-gray-500">Send notifications via SMS (Premium feature)</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.smsNotifications}
                              onChange={(e) => setFieldValue('smsNotifications', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Push Notifications</span>
                              <p className="text-xs text-gray-500">Browser and mobile push notifications</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.pushNotifications}
                              onChange={(e) => setFieldValue('pushNotifications', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>

                      {/* Report Frequency */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Automated Reports</h4>

                        <div className="space-y-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Weekly Reports</span>
                              <p className="text-xs text-gray-500">Receive weekly activity summaries</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.weeklyReports}
                              onChange={(e) => setFieldValue('weeklyReports', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Monthly Reports</span>
                              <p className="text-xs text-gray-500">Receive monthly performance reports</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.monthlyReports}
                              onChange={(e) => setFieldValue('monthlyReports', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'security' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>

                    <div className="space-y-6">
                      {/* Authentication Settings */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Authentication Requirements</h4>

                        <div className="space-y-3">
                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Email Verification</span>
                              <p className="text-xs text-gray-500">Require email verification for new accounts</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.requireEmailVerification}
                              onChange={(e) => setFieldValue('requireEmailVerification', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Two-Factor Authentication</span>
                              <p className="text-xs text-gray-500">Require 2FA for admin accounts</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enableTwoFactor}
                              onChange={(e) => setFieldValue('enableTwoFactor', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>

                          <label className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <span className="text-sm font-medium text-gray-900">Strong Passwords</span>
                              <p className="text-xs text-gray-500">Enforce strong password requirements</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={values.enforceStrongPasswords}
                              onChange={(e) => setFieldValue('enforceStrongPasswords', e.target.checked)}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </label>
                        </div>
                      </div>

                      {/* Password & Session Settings */}
                      <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900">Password & Session Settings</h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            name="passwordMinLength"
                            label="Minimum Password Length"
                            type="number"
                            min="6"
                            max="32"
                            required
                          />

                          <FormField
                            name="sessionTimeout"
                            label="Session Timeout (minutes)"
                            type="number"
                            min="5"
                            max="1440"
                            required
                          />
                        </div>
                      </div>

                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex">
                          <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                          <div>
                            <h4 className="text-sm font-medium text-yellow-800">Security Notice</h4>
                            <p className="text-sm text-yellow-700 mt-1">
                              Changes to security settings will affect all users in your institute.
                              Make sure to communicate any changes to your users in advance.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end pt-6 border-t border-gray-200">
                  <Button
                    type="submit"
                    loading={isSaving}
                    className="flex items-center"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Settings
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  )
}
