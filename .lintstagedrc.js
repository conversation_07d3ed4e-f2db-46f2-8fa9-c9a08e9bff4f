module.exports = {
  // TypeScript and JavaScript files
  '**/*.{ts,tsx,js,jsx}': [
    'eslint --fix',
    'prettier --write',
  ],
  
  // JSON files
  '**/*.json': [
    'prettier --write',
  ],
  
  // Markdown files
  '**/*.md': [
    'prettier --write',
  ],
  
  // YAML files
  '**/*.{yml,yaml}': [
    'prettier --write',
  ],
  
  // CSS and SCSS files
  '**/*.{css,scss,sass}': [
    'prettier --write',
  ],
  
  // Frontend specific files
  'frontend/**/*.{ts,tsx}': [
    'cd frontend && npm run type-check',
  ],
  
  // Backend specific files
  'backend/**/*.{ts,js}': [
    'cd backend && npm run type-check',
  ],
}
