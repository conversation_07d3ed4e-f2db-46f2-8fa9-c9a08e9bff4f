export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  INSTITUTE_ADMIN = 'institute_admin',
  TEACHER = 'teacher',
  STUDENT = 'student',
}

export enum Permission {
  // Super Admin Permissions
  MANAGE_PLATFORM = 'manage_platform',
  MANAGE_INSTITUTES = 'manage_institutes',
  MANAGE_SUBSCRIPTIONS = 'manage_subscriptions',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_SUPER_ADMINS = 'manage_super_admins',

  // Institute Admin Permissions
  MANAGE_INSTITUTE = 'manage_institute',
  MANAGE_INSTITUTE_USERS = 'manage_institute_users',
  MANAGE_INSTITUTE_SETTINGS = 'manage_institute_settings',
  MANAGE_INSTITUTE_BRANDING = 'manage_institute_branding',
  MANAGE_COURSES = 'manage_courses',
  VIEW_INSTITUTE_ANALYTICS = 'view_institute_analytics',
  MANAGE_TEACHERS = 'manage_teachers',
  MANAGE_STUDENTS = 'manage_students',

  // Teacher Permissions
  CREATE_COURSES = 'create_courses',
  EDIT_OWN_COURSES = 'edit_own_courses',
  MANAGE_COURSE_CONTENT = 'manage_course_content',
  GRADE_ASSIGNMENTS = 'grade_assignments',
  VIEW_STUDENT_PROGRESS = 'view_student_progress',
  MANAGE_ENROLLMENTS = 'manage_enrollments',
  COMMUNICATE_WITH_STUDENTS = 'communicate_with_students',

  // Student Permissions
  VIEW_COURSES = 'view_courses',
  ENROLL_IN_COURSES = 'enroll_in_courses',
  SUBMIT_ASSIGNMENTS = 'submit_assignments',
  VIEW_GRADES = 'view_grades',
  COMMUNICATE_WITH_TEACHERS = 'communicate_with_teachers',
  UPDATE_PROFILE = 'update_profile',
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId?: string | undefined
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  instituteId?: string | undefined
  iat?: number
  exp?: number
}

export interface AuthenticatedRequest extends Request {
  user: User
}

export interface RolePermissions {
  [UserRole.SUPER_ADMIN]: Permission[]
  [UserRole.INSTITUTE_ADMIN]: Permission[]
  [UserRole.TEACHER]: Permission[]
  [UserRole.STUDENT]: Permission[]
}

export const ROLE_PERMISSIONS: RolePermissions = {
  [UserRole.SUPER_ADMIN]: [
    Permission.MANAGE_PLATFORM,
    Permission.MANAGE_INSTITUTES,
    Permission.MANAGE_SUBSCRIPTIONS,
    Permission.VIEW_ANALYTICS,
    Permission.MANAGE_SUPER_ADMINS,
    // Super admin has all permissions
    ...Object.values(Permission),
  ],
  [UserRole.INSTITUTE_ADMIN]: [
    Permission.MANAGE_INSTITUTE,
    Permission.MANAGE_INSTITUTE_USERS,
    Permission.MANAGE_INSTITUTE_SETTINGS,
    Permission.MANAGE_INSTITUTE_BRANDING,
    Permission.MANAGE_COURSES,
    Permission.VIEW_INSTITUTE_ANALYTICS,
    Permission.MANAGE_TEACHERS,
    Permission.MANAGE_STUDENTS,
    Permission.CREATE_COURSES,
    Permission.EDIT_OWN_COURSES,
    Permission.MANAGE_COURSE_CONTENT,
    Permission.GRADE_ASSIGNMENTS,
    Permission.VIEW_STUDENT_PROGRESS,
    Permission.MANAGE_ENROLLMENTS,
    Permission.COMMUNICATE_WITH_STUDENTS,
    Permission.UPDATE_PROFILE,
  ],
  [UserRole.TEACHER]: [
    Permission.CREATE_COURSES,
    Permission.EDIT_OWN_COURSES,
    Permission.MANAGE_COURSE_CONTENT,
    Permission.GRADE_ASSIGNMENTS,
    Permission.VIEW_STUDENT_PROGRESS,
    Permission.MANAGE_ENROLLMENTS,
    Permission.COMMUNICATE_WITH_STUDENTS,
    Permission.VIEW_COURSES,
    Permission.UPDATE_PROFILE,
  ],
  [UserRole.STUDENT]: [
    Permission.VIEW_COURSES,
    Permission.ENROLL_IN_COURSES,
    Permission.SUBMIT_ASSIGNMENTS,
    Permission.VIEW_GRADES,
    Permission.COMMUNICATE_WITH_TEACHERS,
    Permission.UPDATE_PROFILE,
  ],
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: UserRole
  instituteId?: string
}

export interface AuthResponse {
  success: boolean
  user: Omit<User, 'password'>
  token: string
  refreshToken: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirmRequest {
  token: string
  password: string
}

// Resource ownership types for fine-grained access control
export interface ResourceOwnership {
  userId: string
  resourceType: 'course' | 'assignment' | 'institute' | 'user'
  resourceId: string
  permissions: Permission[]
}

// Context for permission checking
export interface PermissionContext {
  user: User
  resource?: {
    type: string
    id: string
    ownerId?: string
    instituteId?: string
  }
  action: Permission
}

export interface AccessControlResult {
  allowed: boolean
  reason?: string
  requiredRole?: UserRole
  requiredPermission?: Permission
}
