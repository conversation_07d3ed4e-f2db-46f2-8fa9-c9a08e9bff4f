{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/config/*": ["config/*"], "@/controllers/*": ["controllers/*"], "@/middleware/*": ["middleware/*"], "@/models/*": ["models/*"], "@/routes/*": ["routes/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/database/*": ["database/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "jest"]}, "include": ["src/**/*", "src/**/*.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "coverage", "uploads"], "ts-node": {"require": ["tsconfig-paths/register"], "files": true, "experimentalSpecifierResolution": "node"}}