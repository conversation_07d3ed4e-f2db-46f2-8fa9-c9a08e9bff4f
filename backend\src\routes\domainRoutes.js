const express = require('express');
const domainService = require('../services/domainService');
const brandingService = require('../services/brandingService');
const { authenticateToken } = require('../middleware/authMiddleware');
const { requireInstituteManagement } = require('../middleware/rbacMiddleware');
const { requireTenant } = require('../middleware/tenantMiddleware');
const { responseUtils } = require('../utils/auth');
const yup = require('yup');

const router = express.Router();

// Apply authentication to all domain routes
router.use(authenticateToken);

/**
 * Input validation schemas
 */
const addDomainSchema = yup.object({
  domain: yup
    .string()
    .required('Domain is required')
    .matches(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/,
      'Invalid domain format'
    )
    .max(253, 'Domain too long'),
  domainType: yup
    .string()
    .oneOf(['custom', 'subdomain'], 'Invalid domain type')
    .default('custom'),
  isPrimary: yup
    .boolean()
    .default(false)
});

const updateBrandingSchema = yup.object({
  logoUrl: yup.string().url('Invalid logo URL').nullable(),
  faviconUrl: yup.string().url('Invalid favicon URL').nullable(),
  primaryColor: yup
    .string()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color format')
    .nullable(),
  secondaryColor: yup
    .string()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color format')
    .nullable(),
  accentColor: yup
    .string()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color format')
    .nullable(),
  fontFamily: yup
    .string()
    .max(100, 'Font family name too long')
    .nullable(),
  customCss: yup
    .string()
    .max(10000, 'Custom CSS too long')
    .nullable(),
  landingPageTemplate: yup
    .string()
    .oneOf(['default', 'modern', 'academic', 'minimal'], 'Invalid template')
    .nullable(),
  landingPageContent: yup.object().nullable(),
  socialLinks: yup.object().nullable(),
  contactInfo: yup.object().nullable(),
  seoSettings: yup.object().nullable()
});

/**
 * @route GET /api/domains
 * @desc Get all domains for the institute
 * @access Institute Admin
 */
router.get('/',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const domains = await domainService.getInstituteDomains(req.tenant.instituteId);

      res.json({
        success: true,
        domains,
        count: domains.length
      });
    } catch (error) {
      console.error('Get domains error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get domains', 'DOMAINS_GET_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/domains
 * @desc Add a new custom domain
 * @access Institute Admin
 */
router.post('/',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      // Validate input
      const validatedData = await addDomainSchema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      const result = await domainService.addCustomDomain(
        req.tenant.instituteId,
        validatedData
      );

      res.status(201).json({
        success: true,
        message: 'Domain added successfully',
        domain: result.domain,
        verification: result.verificationInstructions
      });
    } catch (error) {
      console.error('Add domain error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      if (error.message.includes('already registered')) {
        return res.status(409).json(
          responseUtils.createErrorResponse(error.message, 'DOMAIN_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to add domain', 'DOMAIN_ADD_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/domains/:domainId/verify
 * @desc Verify domain ownership
 * @access Institute Admin
 */
router.post('/:domainId/verify',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const { domainId } = req.params;

      const result = await domainService.verifyDomainOwnership(domainId);

      if (result.verified) {
        res.json({
          success: true,
          message: result.message,
          verified: true,
          nextSteps: result.nextSteps
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          verified: false,
          error: result.error,
          troubleshooting: result.troubleshooting
        });
      }
    } catch (error) {
      console.error('Verify domain error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Domain verification failed', 'DOMAIN_VERIFY_ERROR')
      );
    }
  }
);

/**
 * @route DELETE /api/domains/:domainId
 * @desc Remove a custom domain
 * @access Institute Admin
 */
router.delete('/:domainId',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const { domainId } = req.params;

      const result = await domainService.removeDomain(domainId, req.tenant.instituteId);

      res.json({
        success: true,
        message: 'Domain removed successfully',
        removedDomain: result.removedDomain
      });
    } catch (error) {
      console.error('Remove domain error:', error.message);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return res.status(404).json(
          responseUtils.createErrorResponse(error.message, 'DOMAIN_NOT_FOUND')
        );
      }

      if (error.message.includes('Cannot remove')) {
        return res.status(400).json(
          responseUtils.createErrorResponse(error.message, 'DOMAIN_REMOVE_RESTRICTED')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to remove domain', 'DOMAIN_REMOVE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/domains/branding
 * @desc Get institute branding settings
 * @access Institute Admin
 */
router.get('/branding',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const branding = await brandingService.getInstituteBranding(req.tenant.instituteId);

      res.json({
        success: true,
        branding
      });
    } catch (error) {
      console.error('Get branding error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get branding', 'BRANDING_GET_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/domains/branding
 * @desc Update institute branding settings
 * @access Institute Admin
 */
router.put('/branding',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      // Validate input
      const validatedData = await updateBrandingSchema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      const branding = await brandingService.updateInstituteBranding(
        req.tenant.instituteId,
        validatedData
      );

      res.json({
        success: true,
        message: 'Branding updated successfully',
        branding
      });
    } catch (error) {
      console.error('Update branding error:', error.message);

      if (error.name === 'ValidationError') {
        return res.status(400).json(
          responseUtils.createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.errors)
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update branding', 'BRANDING_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/domains/templates
 * @desc Get available landing page templates
 * @access Institute Admin
 */
router.get('/templates',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const templates = brandingService.getAvailableTemplates();

      res.json({
        success: true,
        templates
      });
    } catch (error) {
      console.error('Get templates error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get templates', 'TEMPLATES_GET_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/domains/:domainName/landing-page
 * @desc Generate and preview landing page for domain
 * @access Institute Admin
 */
router.get('/:domainName/landing-page',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const { domainName } = req.params;

      const landingPage = await brandingService.generateLandingPage(
        req.tenant.instituteId,
        domainName
      );

      res.json({
        success: true,
        landingPage
      });
    } catch (error) {
      console.error('Generate landing page error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to generate landing page', 'LANDING_PAGE_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/domains/ssl/renew
 * @desc Check and renew SSL certificates
 * @access Institute Admin
 */
router.post('/ssl/renew',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const renewalResults = await domainService.checkSSLRenewal();

      res.json({
        success: true,
        message: 'SSL renewal check completed',
        results: renewalResults
      });
    } catch (error) {
      console.error('SSL renewal error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('SSL renewal failed', 'SSL_RENEWAL_ERROR')
      );
    }
  }
);

module.exports = router;
