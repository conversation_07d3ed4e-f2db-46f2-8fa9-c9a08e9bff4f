const { query } = require('../database/connection');

/**
 * Role-Based Access Control (RBAC) Service
 * Manages permissions, roles, and access control logic
 */

/**
 * Permission definitions for the LMS system
 */
const PERMISSIONS = {
  // Platform Management (Super Admin only)
  PLATFORM_MANAGE: 'platform:manage',
  PLATFORM_VIEW_ALL: 'platform:view_all',
  PLATFORM_ANALYTICS: 'platform:analytics',
  
  // Institute Management
  INSTITUTE_MANAGE: 'institute:manage',
  INSTITUTE_VIEW: 'institute:view',
  INSTITUTE_SETTINGS: 'institute:settings',
  INSTITUTE_ANALYTICS: 'institute:analytics',
  INSTITUTE_BILLING: 'institute:billing',
  
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE_ROLES: 'user:manage_roles',
  
  // Course Management
  COURSE_CREATE: 'course:create',
  COURSE_READ: 'course:read',
  COURSE_UPDATE: 'course:update',
  COURSE_DELETE: 'course:delete',
  COURSE_PUBLISH: 'course:publish',
  COURSE_MANAGE_CONTENT: 'course:manage_content',
  
  // Content Management
  CONTENT_CREATE: 'content:create',
  CONTENT_READ: 'content:read',
  CONTENT_UPDATE: 'content:update',
  CONTENT_DELETE: 'content:delete',
  CONTENT_UPLOAD: 'content:upload',
  
  // Student Management
  STUDENT_ENROLL: 'student:enroll',
  STUDENT_VIEW: 'student:view',
  STUDENT_PROGRESS: 'student:progress',
  STUDENT_GRADES: 'student:grades',
  
  // Assessment Management
  ASSESSMENT_CREATE: 'assessment:create',
  ASSESSMENT_READ: 'assessment:read',
  ASSESSMENT_UPDATE: 'assessment:update',
  ASSESSMENT_DELETE: 'assessment:delete',
  ASSESSMENT_GRADE: 'assessment:grade',
  
  // Reports and Analytics
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
  ANALYTICS_VIEW: 'analytics:view',
  
  // Settings and Configuration
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_UPDATE: 'settings:update',
  
  // Profile Management
  PROFILE_VIEW: 'profile:view',
  PROFILE_UPDATE: 'profile:update'
};

/**
 * Role definitions with their default permissions
 */
const ROLE_PERMISSIONS = {
  super_admin: [
    // Platform-wide access
    PERMISSIONS.PLATFORM_MANAGE,
    PERMISSIONS.PLATFORM_VIEW_ALL,
    PERMISSIONS.PLATFORM_ANALYTICS,
    
    // Full institute access
    PERMISSIONS.INSTITUTE_MANAGE,
    PERMISSIONS.INSTITUTE_VIEW,
    PERMISSIONS.INSTITUTE_SETTINGS,
    PERMISSIONS.INSTITUTE_ANALYTICS,
    PERMISSIONS.INSTITUTE_BILLING,
    
    // Full user management
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE_ROLES,
    
    // Full course management
    PERMISSIONS.COURSE_CREATE,
    PERMISSIONS.COURSE_READ,
    PERMISSIONS.COURSE_UPDATE,
    PERMISSIONS.COURSE_DELETE,
    PERMISSIONS.COURSE_PUBLISH,
    PERMISSIONS.COURSE_MANAGE_CONTENT,
    
    // Full content management
    PERMISSIONS.CONTENT_CREATE,
    PERMISSIONS.CONTENT_READ,
    PERMISSIONS.CONTENT_UPDATE,
    PERMISSIONS.CONTENT_DELETE,
    PERMISSIONS.CONTENT_UPLOAD,
    
    // Full reports and analytics
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_EXPORT,
    PERMISSIONS.ANALYTICS_VIEW,
    
    // Settings management
    PERMISSIONS.SETTINGS_VIEW,
    PERMISSIONS.SETTINGS_UPDATE,
    
    // Profile management
    PERMISSIONS.PROFILE_VIEW,
    PERMISSIONS.PROFILE_UPDATE
  ],

  institute_admin: [
    // Institute management (own institute only)
    PERMISSIONS.INSTITUTE_MANAGE,
    PERMISSIONS.INSTITUTE_VIEW,
    PERMISSIONS.INSTITUTE_SETTINGS,
    PERMISSIONS.INSTITUTE_ANALYTICS,
    
    // User management within institute
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE_ROLES,
    
    // Course management
    PERMISSIONS.COURSE_CREATE,
    PERMISSIONS.COURSE_READ,
    PERMISSIONS.COURSE_UPDATE,
    PERMISSIONS.COURSE_DELETE,
    PERMISSIONS.COURSE_PUBLISH,
    PERMISSIONS.COURSE_MANAGE_CONTENT,
    
    // Content management
    PERMISSIONS.CONTENT_CREATE,
    PERMISSIONS.CONTENT_READ,
    PERMISSIONS.CONTENT_UPDATE,
    PERMISSIONS.CONTENT_DELETE,
    PERMISSIONS.CONTENT_UPLOAD,
    
    // Student management
    PERMISSIONS.STUDENT_ENROLL,
    PERMISSIONS.STUDENT_VIEW,
    PERMISSIONS.STUDENT_PROGRESS,
    PERMISSIONS.STUDENT_GRADES,
    
    // Assessment management
    PERMISSIONS.ASSESSMENT_CREATE,
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.ASSESSMENT_UPDATE,
    PERMISSIONS.ASSESSMENT_DELETE,
    PERMISSIONS.ASSESSMENT_GRADE,
    
    // Reports and analytics
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_EXPORT,
    PERMISSIONS.ANALYTICS_VIEW,
    
    // Settings (institute level)
    PERMISSIONS.SETTINGS_VIEW,
    PERMISSIONS.SETTINGS_UPDATE,
    
    // Profile management
    PERMISSIONS.PROFILE_VIEW,
    PERMISSIONS.PROFILE_UPDATE
  ],

  teacher: [
    // Course management (own courses)
    PERMISSIONS.COURSE_CREATE,
    PERMISSIONS.COURSE_READ,
    PERMISSIONS.COURSE_UPDATE,
    PERMISSIONS.COURSE_MANAGE_CONTENT,
    
    // Content management (own content)
    PERMISSIONS.CONTENT_CREATE,
    PERMISSIONS.CONTENT_READ,
    PERMISSIONS.CONTENT_UPDATE,
    PERMISSIONS.CONTENT_DELETE,
    PERMISSIONS.CONTENT_UPLOAD,
    
    // Student management (enrolled students)
    PERMISSIONS.STUDENT_VIEW,
    PERMISSIONS.STUDENT_PROGRESS,
    PERMISSIONS.STUDENT_GRADES,
    
    // Assessment management
    PERMISSIONS.ASSESSMENT_CREATE,
    PERMISSIONS.ASSESSMENT_READ,
    PERMISSIONS.ASSESSMENT_UPDATE,
    PERMISSIONS.ASSESSMENT_DELETE,
    PERMISSIONS.ASSESSMENT_GRADE,
    
    // Reports (own courses)
    PERMISSIONS.REPORTS_VIEW,
    
    // Profile management
    PERMISSIONS.PROFILE_VIEW,
    PERMISSIONS.PROFILE_UPDATE
  ],

  student: [
    // Course access (enrolled courses only)
    PERMISSIONS.COURSE_READ,
    
    // Content access (enrolled courses)
    PERMISSIONS.CONTENT_READ,
    
    // Assessment participation
    PERMISSIONS.ASSESSMENT_READ,
    
    // Profile management
    PERMISSIONS.PROFILE_VIEW,
    PERMISSIONS.PROFILE_UPDATE
  ]
};

/**
 * Resource-based permission checks
 */
const RESOURCE_PERMISSIONS = {
  // API endpoint to permission mapping
  'GET /api/admin/institutes': [PERMISSIONS.PLATFORM_VIEW_ALL],
  'POST /api/admin/institutes': [PERMISSIONS.PLATFORM_MANAGE],
  'PUT /api/admin/institutes/:id': [PERMISSIONS.PLATFORM_MANAGE],
  'DELETE /api/admin/institutes/:id': [PERMISSIONS.PLATFORM_MANAGE],
  
  'GET /api/institute/info': [PERMISSIONS.INSTITUTE_VIEW],
  'PUT /api/institute/info': [PERMISSIONS.INSTITUTE_SETTINGS],
  'GET /api/institute/users': [PERMISSIONS.USER_READ],
  'POST /api/institute/users': [PERMISSIONS.USER_CREATE],
  'PUT /api/institute/users/:id': [PERMISSIONS.USER_UPDATE],
  'DELETE /api/institute/users/:id': [PERMISSIONS.USER_DELETE],
  
  'GET /api/courses': [PERMISSIONS.COURSE_READ],
  'POST /api/courses': [PERMISSIONS.COURSE_CREATE],
  'PUT /api/courses/:id': [PERMISSIONS.COURSE_UPDATE],
  'DELETE /api/courses/:id': [PERMISSIONS.COURSE_DELETE],
  'POST /api/courses/:id/publish': [PERMISSIONS.COURSE_PUBLISH],
  
  'GET /api/content/:id': [PERMISSIONS.CONTENT_READ],
  'POST /api/content': [PERMISSIONS.CONTENT_CREATE],
  'PUT /api/content/:id': [PERMISSIONS.CONTENT_UPDATE],
  'DELETE /api/content/:id': [PERMISSIONS.CONTENT_DELETE],
  'POST /api/content/upload': [PERMISSIONS.CONTENT_UPLOAD],
  
  'GET /api/students': [PERMISSIONS.STUDENT_VIEW],
  'POST /api/students/enroll': [PERMISSIONS.STUDENT_ENROLL],
  'GET /api/students/:id/progress': [PERMISSIONS.STUDENT_PROGRESS],
  'GET /api/students/:id/grades': [PERMISSIONS.STUDENT_GRADES],
  
  'GET /api/assessments': [PERMISSIONS.ASSESSMENT_READ],
  'POST /api/assessments': [PERMISSIONS.ASSESSMENT_CREATE],
  'PUT /api/assessments/:id': [PERMISSIONS.ASSESSMENT_UPDATE],
  'DELETE /api/assessments/:id': [PERMISSIONS.ASSESSMENT_DELETE],
  'POST /api/assessments/:id/grade': [PERMISSIONS.ASSESSMENT_GRADE],
  
  'GET /api/reports': [PERMISSIONS.REPORTS_VIEW],
  'GET /api/reports/export': [PERMISSIONS.REPORTS_EXPORT],
  'GET /api/analytics': [PERMISSIONS.ANALYTICS_VIEW],
  
  'GET /api/settings': [PERMISSIONS.SETTINGS_VIEW],
  'PUT /api/settings': [PERMISSIONS.SETTINGS_UPDATE],
  
  'GET /api/auth/me': [PERMISSIONS.PROFILE_VIEW],
  'PUT /api/auth/profile': [PERMISSIONS.PROFILE_UPDATE]
};

class RBACService {
  /**
   * Get permissions for a user role
   */
  static getRolePermissions(role) {
    return ROLE_PERMISSIONS[role] || [];
  }

  /**
   * Check if a role has a specific permission
   */
  static hasPermission(role, permission) {
    const rolePermissions = this.getRolePermissions(role);
    return rolePermissions.includes(permission);
  }

  /**
   * Check if a role has any of the required permissions
   */
  static hasAnyPermission(role, permissions) {
    const rolePermissions = this.getRolePermissions(role);
    return permissions.some(permission => rolePermissions.includes(permission));
  }

  /**
   * Check if a role has all required permissions
   */
  static hasAllPermissions(role, permissions) {
    const rolePermissions = this.getRolePermissions(role);
    return permissions.every(permission => rolePermissions.includes(permission));
  }

  /**
   * Get required permissions for a resource/endpoint
   */
  static getResourcePermissions(method, path) {
    const resourceKey = `${method.toUpperCase()} ${path}`;
    
    // Try exact match first
    if (RESOURCE_PERMISSIONS[resourceKey]) {
      return RESOURCE_PERMISSIONS[resourceKey];
    }
    
    // Try pattern matching for parameterized routes
    for (const [pattern, permissions] of Object.entries(RESOURCE_PERMISSIONS)) {
      const regex = new RegExp('^' + pattern.replace(/:[^/]+/g, '[^/]+') + '$');
      if (regex.test(resourceKey)) {
        return permissions;
      }
    }
    
    return [];
  }

  /**
   * Check if user can access a resource
   */
  static canAccessResource(userRole, method, path) {
    const requiredPermissions = this.getResourcePermissions(method, path);
    
    if (requiredPermissions.length === 0) {
      return true; // No permissions required (public endpoint)
    }
    
    return this.hasAnyPermission(userRole, requiredPermissions);
  }

  /**
   * Get user permissions from database
   */
  static async getUserPermissions(userId, instituteId = null) {
    try {
      let sql = `
        SELECT ur.role_name, ur.permissions, u.role as user_role
        FROM user_roles ur
        JOIN users u ON ur.user_id = u.id
        WHERE ur.user_id = $1 AND ur.is_active = true
      `;
      
      const params = [userId];
      
      if (instituteId) {
        sql += ' AND ur.institute_id = $2';
        params.push(instituteId);
      }
      
      const result = await query(sql, params);
      
      // Combine role permissions with custom permissions
      const allPermissions = new Set();
      
      result.rows.forEach(row => {
        // Add default role permissions
        const rolePermissions = this.getRolePermissions(row.user_role);
        rolePermissions.forEach(permission => allPermissions.add(permission));
        
        // Add custom permissions from user_roles table
        if (row.permissions) {
          try {
            let customPermissions;
            if (typeof row.permissions === 'string') {
              customPermissions = JSON.parse(row.permissions);
            } else if (Array.isArray(row.permissions)) {
              customPermissions = row.permissions;
            } else {
              customPermissions = [];
            }
            customPermissions.forEach(permission => allPermissions.add(permission));
          } catch (error) {
            console.warn('Failed to parse custom permissions:', error.message);
          }
        }
      });
      
      return Array.from(allPermissions);
    } catch (error) {
      console.error('Error getting user permissions:', error.message);
      return [];
    }
  }

  /**
   * Check if user has permission (database lookup)
   */
  static async userHasPermission(userId, permission, instituteId = null) {
    const userPermissions = await this.getUserPermissions(userId, instituteId);
    return userPermissions.includes(permission);
  }

  /**
   * Check if user can access resource (database lookup)
   */
  static async userCanAccessResource(userId, method, path, instituteId = null) {
    const requiredPermissions = this.getResourcePermissions(method, path);
    
    if (requiredPermissions.length === 0) {
      return true; // Public endpoint
    }
    
    const userPermissions = await this.getUserPermissions(userId, instituteId);
    return requiredPermissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * Create or update user role with permissions
   */
  static async assignUserRole(userId, instituteId, roleName, customPermissions = []) {
    try {
      const result = await query(`
        INSERT INTO user_roles (user_id, institute_id, role_name, permissions, is_active)
        VALUES ($1, $2, $3, $4, true)
        ON CONFLICT (user_id, institute_id, role_name)
        DO UPDATE SET 
          permissions = EXCLUDED.permissions,
          is_active = EXCLUDED.is_active,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [userId, instituteId, roleName, JSON.stringify(customPermissions)]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Error assigning user role:', error.message);
      throw error;
    }
  }

  /**
   * Remove user role
   */
  static async removeUserRole(userId, instituteId, roleName) {
    try {
      await query(
        'UPDATE user_roles SET is_active = false WHERE user_id = $1 AND institute_id = $2 AND role_name = $3',
        [userId, instituteId, roleName]
      );
      
      return true;
    } catch (error) {
      console.error('Error removing user role:', error.message);
      throw error;
    }
  }
}

module.exports = {
  RBACService,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  RESOURCE_PERMISSIONS
};
