const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { query, testConnection, closePool } = require('./connection');

/**
 * Database Seed Script
 * Adds initial data for development and testing
 */

const seedDatabase = async () => {
  console.log('🌱 Starting database seeding...');
  
  try {
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Cannot proceed with seeding - database connection failed');
      process.exit(1);
    }

    // Create super admin user
    console.log('👑 Creating super admin user...');
    const superAdminPassword = await bcrypt.hash('admin123', 12);
    const superAdminId = uuidv4();
    
    await query(`
      INSERT INTO users (id, email, password_hash, first_name, last_name, role, is_email_verified, institute_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email, institute_id) DO NOTHING
    `, [
      superAdminId,
      '<EMAIL>',
      superAdminPassword,
      'Super',
      'Admin',
      'super_admin',
      true,
      null // Super admin has no institute
    ]);

    // Create sample institute
    console.log('🏫 Creating sample institute...');
    const instituteId = uuidv4();
    
    await query(`
      INSERT INTO institutes (id, name, slug, email, phone, address, website, subscription_plan)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (slug) DO NOTHING
    `, [
      instituteId,
      'ABC Engineering College',
      'abc-engineering',
      '<EMAIL>',
      '+91-9876543210',
      '123 College Street, Tech City, TC 12345',
      'https://abcengineering.edu',
      'professional'
    ]);

    // Create institute admin
    console.log('👨‍💼 Creating institute admin...');
    const instituteAdminPassword = await bcrypt.hash('admin123', 12);
    const instituteAdminId = uuidv4();
    
    await query(`
      INSERT INTO users (id, institute_id, email, password_hash, first_name, last_name, role, is_email_verified)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email, institute_id) DO NOTHING
    `, [
      instituteAdminId,
      instituteId,
      '<EMAIL>',
      instituteAdminPassword,
      'John',
      'Smith',
      'institute_admin',
      true
    ]);

    // Create sample teacher
    console.log('👨‍🏫 Creating sample teacher...');
    const teacherPassword = await bcrypt.hash('teacher123', 12);
    const teacherId = uuidv4();
    
    await query(`
      INSERT INTO users (id, institute_id, email, password_hash, first_name, last_name, role, is_email_verified)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email, institute_id) DO NOTHING
    `, [
      teacherId,
      instituteId,
      '<EMAIL>',
      teacherPassword,
      'Jane',
      'Doe',
      'teacher',
      true
    ]);

    // Create sample student
    console.log('👨‍🎓 Creating sample student...');
    const studentPassword = await bcrypt.hash('student123', 12);
    const studentId = uuidv4();
    
    await query(`
      INSERT INTO users (id, institute_id, email, password_hash, first_name, last_name, role, is_email_verified)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email, institute_id) DO NOTHING
    `, [
      studentId,
      instituteId,
      '<EMAIL>',
      studentPassword,
      'Alice',
      'Johnson',
      'student',
      true
    ]);

    // Create user roles
    console.log('🔐 Creating user roles...');
    
    // Institute admin role
    await query(`
      INSERT INTO user_roles (user_id, institute_id, role_name, permissions)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, institute_id, role_name) DO NOTHING
    `, [
      instituteAdminId,
      instituteId,
      'institute_admin',
      JSON.stringify(['manage_institute', 'manage_users', 'view_analytics', 'manage_settings'])
    ]);

    // Teacher role
    await query(`
      INSERT INTO user_roles (user_id, institute_id, role_name, permissions)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, institute_id, role_name) DO NOTHING
    `, [
      teacherId,
      instituteId,
      'teacher',
      JSON.stringify(['create_courses', 'manage_students', 'grade_assignments', 'view_reports'])
    ]);

    // Student role
    await query(`
      INSERT INTO user_roles (user_id, institute_id, role_name, permissions)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, institute_id, role_name) DO NOTHING
    `, [
      studentId,
      instituteId,
      'student',
      JSON.stringify(['view_courses', 'submit_assignments', 'take_quizzes', 'view_grades'])
    ]);

    // Create sample institute settings
    console.log('⚙️ Creating institute settings...');
    const settings = [
      { key: 'theme_primary_color', value: '#1E40AF', type: 'string', public: true },
      { key: 'theme_secondary_color', value: '#3B82F6', type: 'string', public: true },
      { key: 'institute_timezone', value: 'Asia/Kolkata', type: 'string', public: false },
      { key: 'max_students_per_course', value: 100, type: 'number', public: false },
      { key: 'allow_student_registration', value: true, type: 'boolean', public: false }
    ];

    for (const setting of settings) {
      await query(`
        INSERT INTO institute_settings (institute_id, setting_key, setting_value, setting_type, is_public)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (institute_id, setting_key) DO NOTHING
      `, [
        instituteId,
        setting.key,
        JSON.stringify(setting.value),
        setting.type,
        setting.public
      ]);
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Created sample data:');
    console.log('   👑 Super Admin: <EMAIL> (password: admin123)');
    console.log('   🏫 Institute: ABC Engineering College');
    console.log('   👨‍💼 Institute Admin: <EMAIL> (password: admin123)');
    console.log('   👨‍🏫 Teacher: <EMAIL> (password: teacher123)');
    console.log('   👨‍🎓 Student: <EMAIL> (password: student123)');

  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await closePool();
  }
};

// Check if script is run directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
