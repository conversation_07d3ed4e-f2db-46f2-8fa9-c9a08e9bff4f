const securityService = require('../services/securityService');
const { query } = require('../database/connection');
const axios = require('axios');

/**
 * Security Testing Suite
 * Tests security hardening, input validation, rate limiting, and threat detection
 */

class SecurityTester {
  constructor() {
    this.testResults = [];
    this.baseURL = 'http://localhost:5007';
    this.testData = {
      testInstitute: null,
      testUser: null
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test input sanitization
   */
  async testInputSanitization() {
    console.log('\n🧹 Testing Input Sanitization...');

    try {
      // Test XSS payload sanitization
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript:alert("XSS")',
        '<img src="x" onerror="alert(\'XSS\')">',
        '<svg onload="alert(\'XSS\')">',
        '"><script>alert("XSS")</script>'
      ];

      for (const payload of xssPayloads) {
        try {
          const response = await axios.post(`${this.baseURL}/api/auth/register/student`, {
            email: '<EMAIL>',
            password: 'TestPassword123!',
            firstName: payload,
            lastName: 'Test'
          }, {
            headers: { 'X-Tenant-ID': 'test-institute' },
            timeout: 5000
          });

          // Check if payload was sanitized
          const sanitized = !response.data.toString().includes('<script>') && 
                           !response.data.toString().includes('javascript:') &&
                           !response.data.toString().includes('onerror=');

          this.logResult(
            `XSS Sanitization (${payload.substring(0, 20)}...)`,
            sanitized,
            sanitized ? 'Payload sanitized' : 'Payload not sanitized'
          );

        } catch (error) {
          // Error is expected for malicious input
          this.logResult(
            `XSS Sanitization (${payload.substring(0, 20)}...)`,
            error.response?.status === 400,
            'Request rejected as expected'
          );
        }
      }

    } catch (error) {
      this.logResult('Input Sanitization', false, error.message);
    }
  }

  /**
   * Test SQL injection prevention
   */
  async testSQLInjectionPrevention() {
    console.log('\n🛡️ Testing SQL Injection Prevention...');

    try {
      const sqlPayloads = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "'; INSERT INTO users VALUES ('hacker', 'password'); --",
        "' OR 1=1 --"
      ];

      for (const payload of sqlPayloads) {
        try {
          const response = await axios.post(`${this.baseURL}/api/auth/login`, {
            email: payload,
            password: 'test'
          }, {
            headers: { 'X-Tenant-ID': 'test-institute' },
            timeout: 5000
          });

          this.logResult(
            `SQL Injection (${payload.substring(0, 20)}...)`,
            false,
            'Request should have been blocked'
          );

        } catch (error) {
          // Error is expected for SQL injection attempts
          this.logResult(
            `SQL Injection (${payload.substring(0, 20)}...)`,
            error.response?.status === 400,
            'Request blocked as expected'
          );
        }
      }

    } catch (error) {
      this.logResult('SQL Injection Prevention', false, error.message);
    }
  }

  /**
   * Test rate limiting
   */
  async testRateLimiting() {
    console.log('\n⏱️ Testing Rate Limiting...');

    try {
      // Test general rate limiting
      const requests = [];
      const maxRequests = 10; // Should exceed rate limit

      for (let i = 0; i < maxRequests; i++) {
        requests.push(
          axios.get(`${this.baseURL}/health`, {
            timeout: 5000
          }).catch(error => error.response)
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r?.status === 429);

      this.logResult(
        'General Rate Limiting',
        rateLimitedResponses.length > 0,
        `${rateLimitedResponses.length} requests rate limited out of ${maxRequests}`
      );

      // Test auth rate limiting
      const authRequests = [];
      for (let i = 0; i < 6; i++) { // Should exceed auth rate limit
        authRequests.push(
          axios.post(`${this.baseURL}/api/auth/login`, {
            email: '<EMAIL>',
            password: 'wrongpassword'
          }, {
            headers: { 'X-Tenant-ID': 'test-institute' },
            timeout: 5000
          }).catch(error => error.response)
        );
      }

      const authResponses = await Promise.all(authRequests);
      const authRateLimited = authResponses.filter(r => r?.status === 429);

      this.logResult(
        'Auth Rate Limiting',
        authRateLimited.length > 0,
        `${authRateLimited.length} auth requests rate limited out of 6`
      );

    } catch (error) {
      this.logResult('Rate Limiting', false, error.message);
    }
  }

  /**
   * Test security headers
   */
  async testSecurityHeaders() {
    console.log('\n🔒 Testing Security Headers...');

    try {
      const response = await axios.get(`${this.baseURL}/health`, {
        timeout: 5000
      });

      const headers = response.headers;
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'strict-transport-security',
        'content-security-policy'
      ];

      let headerCount = 0;
      for (const header of requiredHeaders) {
        if (headers[header]) {
          headerCount++;
          this.logResult(
            `Security Header: ${header}`,
            true,
            `Present: ${headers[header]}`
          );
        } else {
          this.logResult(
            `Security Header: ${header}`,
            false,
            'Missing'
          );
        }
      }

      this.logResult(
        'Security Headers Overall',
        headerCount >= 4,
        `${headerCount}/${requiredHeaders.length} required headers present`
      );

    } catch (error) {
      this.logResult('Security Headers', false, error.message);
    }
  }

  /**
   * Test file upload security
   */
  async testFileUploadSecurity() {
    console.log('\n📁 Testing File Upload Security...');

    try {
      // Test dangerous file types
      const dangerousFiles = [
        { name: 'malware.exe', mimetype: 'application/x-executable' },
        { name: 'script.js', mimetype: 'application/javascript' },
        { name: 'shell.sh', mimetype: 'application/x-sh' },
        { name: 'virus.bat', mimetype: 'application/x-bat' }
      ];

      for (const file of dangerousFiles) {
        const validation = securityService.validateFileUpload(
          {
            originalname: file.name,
            mimetype: file.mimetype,
            size: 1024
          },
          ['image/jpeg', 'image/png', 'application/pdf'],
          5 * 1024 * 1024
        );

        this.logResult(
          `File Upload Security (${file.name})`,
          !validation.isValid,
          validation.isValid ? 'Dangerous file allowed' : 'Dangerous file blocked'
        );
      }

      // Test oversized file
      const oversizedValidation = securityService.validateFileUpload(
        {
          originalname: 'large.jpg',
          mimetype: 'image/jpeg',
          size: 10 * 1024 * 1024 // 10MB
        },
        ['image/jpeg'],
        5 * 1024 * 1024 // 5MB limit
      );

      this.logResult(
        'File Size Validation',
        !oversizedValidation.isValid,
        oversizedValidation.isValid ? 'Oversized file allowed' : 'Oversized file blocked'
      );

    } catch (error) {
      this.logResult('File Upload Security', false, error.message);
    }
  }

  /**
   * Test brute force detection
   */
  async testBruteForceDetection() {
    console.log('\n🔨 Testing Brute Force Detection...');

    try {
      // Simulate multiple failed login attempts
      const identifier = '<EMAIL>';
      
      for (let i = 0; i < 6; i++) {
        await securityService.logSecurityEvent({
          eventType: 'failed_login',
          severity: 'high',
          ipAddress: '*************',
          userAgent: 'Test-Agent',
          details: { identifier }
        });
      }

      // Check brute force detection
      const detection = await securityService.detectBruteForce(
        identifier,
        'failed_login',
        15 * 60 * 1000, // 15 minutes
        5 // threshold
      );

      this.logResult(
        'Brute Force Detection',
        detection.detected,
        detection.detected ? 
          `Detected after ${detection.attemptCount} attempts` : 
          'Not detected'
      );

    } catch (error) {
      this.logResult('Brute Force Detection', false, error.message);
    }
  }

  /**
   * Test API key validation
   */
  async testAPIKeyValidation() {
    console.log('\n🔑 Testing API Key Validation...');

    try {
      const testKeys = [
        { key: 'weak', expected: false, reason: 'Too short' },
        { key: '12345678901234567890123456789012', expected: false, reason: 'Sequential pattern' },
        { key: 'password123456789012345678901234', expected: false, reason: 'Contains common word' },
        { key: 'a'.repeat(32), expected: false, reason: 'All same character' },
        { key: 'AbC123XyZ789MnO456PqR789StU123VwX', expected: true, reason: 'Strong key' }
      ];

      for (const test of testKeys) {
        const validation = securityService.validateAPIKey(test.key);
        
        this.logResult(
          `API Key Validation (${test.reason})`,
          validation.isValid === test.expected,
          validation.isValid ? 'Valid key' : `Invalid: ${validation.errors.join(', ')}`
        );
      }

    } catch (error) {
      this.logResult('API Key Validation', false, error.message);
    }
  }

  /**
   * Test security audit logging
   */
  async testSecurityAuditLogging() {
    console.log('\n📝 Testing Security Audit Logging...');

    try {
      // Log a test security event
      await securityService.logSecurityEvent({
        eventType: 'test_event',
        severity: 'low',
        ipAddress: '127.0.0.1',
        userAgent: 'Test-Agent',
        details: { test: true }
      });

      // Check if event was logged
      const result = await query(`
        SELECT * FROM security_audit_log 
        WHERE event_type = 'test_event' 
        AND details->>'test' = 'true'
        ORDER BY created_at DESC 
        LIMIT 1
      `);

      this.logResult(
        'Security Audit Logging',
        result.rows.length > 0,
        result.rows.length > 0 ? 'Event logged successfully' : 'Event not logged'
      );

      // Clean up test event
      if (result.rows.length > 0) {
        await query('DELETE FROM security_audit_log WHERE id = $1', [result.rows[0].id]);
      }

    } catch (error) {
      this.logResult('Security Audit Logging', false, error.message);
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Clean up test security events
      await query(`
        DELETE FROM security_audit_log 
        WHERE event_type IN ('test_event', 'failed_login') 
        AND details->>'identifier' = '<EMAIL>'
      `);

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all security tests
   */
  async runAllTests() {
    console.log('🚀 Starting Security Testing Suite...\n');

    await this.testInputSanitization();
    await this.testSQLInjectionPrevention();
    await this.testRateLimiting();
    await this.testSecurityHeaders();
    await this.testFileUploadSecurity();
    await this.testBruteForceDetection();
    await this.testAPIKeyValidation();
    await this.testSecurityAuditLogging();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Security Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Security testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { SecurityTester };
