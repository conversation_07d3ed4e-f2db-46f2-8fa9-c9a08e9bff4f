'use client'

import { useState, useEffect } from 'react'

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  INSTITUTE_ADMIN = 'institute_admin',
  SUPER_ADMIN = 'super_admin'
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId?: string
  avatar?: string
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock authentication check
    // In a real app, this would check for a valid JWT token
    const mockUser: User = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'harvard'
    }

    // Simulate loading delay
    setTimeout(() => {
      setUser(mockUser)
      setIsLoading(false)
    }, 100)
  }, [])

  const login = async (email: string, password: string) => {
    // Mock login implementation
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockUser: User = {
      id: '1',
      email,
      firstName: 'John',
      lastName: 'Doe',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'harvard'
    }
    
    setUser(mockUser)
    setIsLoading(false)
    
    return { success: true, user: mockUser }
  }

  const logout = () => {
    setUser(null)
  }

  return {
    user,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user
  }
}
