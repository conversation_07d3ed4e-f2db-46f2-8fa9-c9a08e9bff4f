'use client'

import React, { useState } from 'react'
import { 
  Users,
  Search,
  Filter,
  UserCheck,
  UserX,
  Crown,
  GraduationCap,
  BookOpen,
  Building2,
  MoreVertical,
  Eye,
  Edit,
  Ban,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/button'

enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  INSTITUTE_ADMIN = 'institute_admin',
  SUPER_ADMIN = 'super_admin',
}

enum UserStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  DEACTIVATED = 'deactivated',
}

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  role: UserRole
  status: UserStatus
  instituteId: string
  instituteName: string
  lastLogin: string
  createdAt: string
  coursesEnrolled?: number
  coursesCompleted?: number
  coursesCreated?: number
}

export default function SuperAdminUsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all')
  const [statusFilter, setStatusFilter] = useState<UserStatus | 'all'>('all')
  const [instituteFilter, setInstituteFilter] = useState<string | 'all'>('all')
  const [users, setUsers] = useState<User[]>([])

  // Initialize mock data
  React.useEffect(() => {
    const mockUsers: User[] = [
      {
        id: 'user-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: UserRole.STUDENT,
        status: UserStatus.ACTIVE,
        instituteId: 'inst-1',
        instituteName: 'Harvard University',
        lastLogin: '2023-12-01T10:30:00Z',
        createdAt: '2023-01-15',
        coursesEnrolled: 5,
        coursesCompleted: 3,
      },
      {
        id: 'user-2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        role: UserRole.TEACHER,
        status: UserStatus.ACTIVE,
        instituteId: 'inst-2',
        instituteName: 'MIT',
        lastLogin: '2023-12-01T09:15:00Z',
        createdAt: '2023-02-20',
        coursesCreated: 8,
      },
      {
        id: 'user-3',
        firstName: 'Robert',
        lastName: 'Johnson',
        email: '<EMAIL>',
        role: UserRole.INSTITUTE_ADMIN,
        status: UserStatus.PENDING,
        instituteId: 'inst-3',
        instituteName: 'Stanford University',
        lastLogin: '2023-11-30T14:20:00Z',
        createdAt: '2023-12-01',
      },
      {
        id: 'user-4',
        firstName: 'Emily',
        lastName: 'Davis',
        email: '<EMAIL>',
        role: UserRole.STUDENT,
        status: UserStatus.SUSPENDED,
        instituteId: 'inst-4',
        instituteName: 'UC Berkeley',
        lastLogin: '2023-11-15T08:45:00Z',
        createdAt: '2023-06-10',
        coursesEnrolled: 3,
        coursesCompleted: 1,
      }
    ]
    setUsers(mockUsers)
  }, [])

  const getRoleIcon = (role: UserRole) => {
    const roleConfig = {
      [UserRole.STUDENT]: { icon: GraduationCap, color: 'text-blue-600 bg-blue-100' },
      [UserRole.TEACHER]: { icon: BookOpen, color: 'text-green-600 bg-green-100' },
      [UserRole.INSTITUTE_ADMIN]: { icon: Building2, color: 'text-purple-600 bg-purple-100' },
      [UserRole.SUPER_ADMIN]: { icon: Crown, color: 'text-orange-600 bg-orange-100' },
    }
    const config = roleConfig[role]
    const Icon = config.icon
    return <Icon className={`w-4 h-4 ${config.color.split(' ')[0]}`} />
  }

  const getRoleBadge = (role: UserRole) => {
    const roleConfig = {
      [UserRole.STUDENT]: { color: 'bg-blue-100 text-blue-800', text: 'Student' },
      [UserRole.TEACHER]: { color: 'bg-green-100 text-green-800', text: 'Teacher' },
      [UserRole.INSTITUTE_ADMIN]: { color: 'bg-purple-100 text-purple-800', text: 'Admin' },
      [UserRole.SUPER_ADMIN]: { color: 'bg-orange-100 text-orange-800', text: 'Super Admin' },
    }
    const config = roleConfig[role]
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {getRoleIcon(role)}
        <span className="ml-1">{config.text}</span>
      </span>
    )
  }

  const getStatusBadge = (status: UserStatus) => {
    const statusConfig = {
      [UserStatus.ACTIVE]: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Active' },
      [UserStatus.PENDING]: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' },
      [UserStatus.SUSPENDED]: { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'Suspended' },
      [UserStatus.DEACTIVATED]: { color: 'bg-gray-100 text-gray-800', icon: XCircle, text: 'Deactivated' },
    }
    const config = statusConfig[status]
    const Icon = config.icon
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.instituteName.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    const matchesInstitute = instituteFilter === 'all' || user.instituteId === instituteFilter

    return matchesSearch && matchesRole && matchesStatus && matchesInstitute
  })

  const userStats = {
    total: users.length,
    active: users.filter(u => u.status === UserStatus.ACTIVE).length,
    pending: users.filter(u => u.status === UserStatus.PENDING).length,
    suspended: users.filter(u => u.status === UserStatus.SUSPENDED).length,
    students: users.filter(u => u.role === UserRole.STUDENT).length,
    teachers: users.filter(u => u.role === UserRole.TEACHER).length,
    admins: users.filter(u => u.role === UserRole.INSTITUTE_ADMIN).length,
  }

  const handleUserAction = (userId: string, action: string) => {
    console.log(`Performing ${action} on user ${userId}`)
    // TODO: Implement actual actions
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600 mt-1">
            Manage platform users across all institutes
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Export Users
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.total.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <UserCheck className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.active}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <UserX className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Suspended</p>
              <p className="text-2xl font-bold text-gray-900">{userStats.suspended}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Role Distribution */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">User Distribution by Role</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <GraduationCap className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{userStats.students}</p>
            <p className="text-sm text-gray-600">Students</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <BookOpen className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{userStats.teachers}</p>
            <p className="text-sm text-gray-600">Teachers</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Building2 className="w-8 h-8 text-purple-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{userStats.admins}</p>
            <p className="text-sm text-gray-600">Admins</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as UserRole | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Roles</option>
            <option value={UserRole.STUDENT}>Students</option>
            <option value={UserRole.TEACHER}>Teachers</option>
            <option value={UserRole.INSTITUTE_ADMIN}>Admins</option>
            <option value={UserRole.SUPER_ADMIN}>Super Admins</option>
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as UserStatus | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value={UserStatus.ACTIVE}>Active</option>
            <option value={UserStatus.PENDING}>Pending</option>
            <option value={UserStatus.SUSPENDED}>Suspended</option>
            <option value={UserStatus.DEACTIVATED}>Deactivated</option>
          </select>

          <select
            value={instituteFilter}
            onChange={(e) => setInstituteFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Institutes</option>
            <option value="inst-1">Harvard University</option>
            <option value="inst-2">MIT</option>
            <option value="inst-3">Stanford University</option>
            <option value="inst-4">UC Berkeley</option>
          </select>

          <div className="text-sm text-gray-600 flex items-center">
            {filteredUsers.length} of {users.length} users
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Institute
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Activity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white font-medium text-sm">
                          {user.firstName[0]}{user.lastName[0]}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(user.status)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{user.instituteName}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {user.role === UserRole.STUDENT && (
                        <div>
                          <div>Enrolled: {user.coursesEnrolled}</div>
                          <div className="text-xs text-gray-500">Completed: {user.coursesCompleted}</div>
                        </div>
                      )}
                      {user.role === UserRole.TEACHER && (
                        <div>
                          <div>Created: {user.coursesCreated} courses</div>
                        </div>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        Last login: {new Date(user.lastLogin).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" title="View Details">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" title="Edit User">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        title="Suspend User"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Ban className="h-3 w-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  )
}
