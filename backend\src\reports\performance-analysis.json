{"timestamp": "2025-07-14T04:41:08.218Z", "analysisResults": {"columnStats": [{"schemaname": "public", "tablename": "users", "attname": "avatar_url", "n_distinct": 0, "correlation": null, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "created_at", "n_distinct": -1, "correlation": 0.42857143, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "email", "n_distinct": -1, "correlation": -0.37142858, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "first_name", "n_distinct": -0.8333333, "correlation": 0.31428573, "most_common_vals": "{Test}", "most_common_freqs": [0.33333334]}, {"schemaname": "public", "tablename": "users", "attname": "id", "n_distinct": -1, "correlation": 0.71428573, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "institute_id", "n_distinct": -0.5, "correlation": 0.2, "most_common_vals": "{7c1efa86-502a-498e-8443-98bcb27eed47}", "most_common_freqs": [0.5]}, {"schemaname": "public", "tablename": "users", "attname": "is_active", "n_distinct": -0.16666667, "correlation": 1, "most_common_vals": "{t}", "most_common_freqs": [1]}, {"schemaname": "public", "tablename": "users", "attname": "is_email_verified", "n_distinct": -0.33333334, "correlation": 0.14285715, "most_common_vals": "{t,f}", "most_common_freqs": [0.6666667, 0.33333334]}, {"schemaname": "public", "tablename": "users", "attname": "last_login_at", "n_distinct": -0.3333333, "correlation": 1, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "last_name", "n_distinct": -0.6666667, "correlation": 0.54285717, "most_common_vals": "{Admin}", "most_common_freqs": [0.5]}, {"schemaname": "public", "tablename": "users", "attname": "password_hash", "n_distinct": -1, "correlation": -0.42857143, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "phone", "n_distinct": -0.16666669, "correlation": null, "most_common_vals": null, "most_common_freqs": null}, {"schemaname": "public", "tablename": "users", "attname": "role", "n_distinct": -0.6666667, "correlation": -0.71428573, "most_common_vals": "{institute_admin}", "most_common_freqs": [0.5]}, {"schemaname": "public", "tablename": "users", "attname": "updated_at", "n_distinct": -1, "correlation": 0.42857143, "most_common_vals": null, "most_common_freqs": null}], "tableSizes": [{"table_name": "institute_domains", "size": "128 kB", "size_bytes": "131072"}, {"table_name": "teacher_profiles", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "student_profiles", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "institutes", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "user_sessions", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "security_audit_log", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "users", "size": "112 kB", "size_bytes": "114688"}, {"table_name": "user_roles", "size": "96 kB", "size_bytes": "98304"}, {"table_name": "email_verifications", "size": "96 kB", "size_bytes": "98304"}, {"table_name": "platform_notifications", "size": "96 kB", "size_bytes": "98304"}, {"table_name": "platform_feature_flags", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "ssl_certificates", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "institute_settings", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "platform_analytics_cache", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "security_config", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "institute_usage_stats", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "domain_verification_logs", "size": "80 kB", "size_bytes": "81920"}, {"table_name": "institute_branding", "size": "64 kB", "size_bytes": "65536"}, {"table_name": "teacher_invitations", "size": "56 kB", "size_bytes": "57344"}, {"table_name": "custom_domains", "size": "56 kB", "size_bytes": "57344"}, {"table_name": "session_security", "size": "56 kB", "size_bytes": "57344"}, {"table_name": "super_admin_activity_log", "size": "48 kB", "size_bytes": "49152"}, {"table_name": "blocked_ips", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "failed_login_attempts", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "rate_limit_violations", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "two_factor_auth", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "subscription_history", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "institute_status_history", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "maintenance_windows", "size": "40 kB", "size_bytes": "40960"}, {"table_name": "course_enrollments", "size": "32 kB", "size_bytes": "32768"}, {"table_name": "password_history", "size": "24 kB", "size_bytes": "24576"}, {"table_name": "institute_overview", "size": "0 bytes", "size_bytes": "0"}], "tableActivity": [{"schemaname": "public", "tablename": "users", "inserts": "31", "updates": "25", "deletes": "21", "live_rows": "10", "dead_rows": "29", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": "2025-07-14T04:21:09.335Z"}, {"schemaname": "public", "tablename": "institutes", "inserts": "16", "updates": "0", "deletes": "9", "live_rows": "7", "dead_rows": "9", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "security_config", "inserts": "6", "updates": "0", "deletes": "0", "live_rows": "6", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "email_verifications", "inserts": "20", "updates": "2", "deletes": "14", "live_rows": "6", "dead_rows": "16", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "institute_settings", "inserts": "6", "updates": "0", "deletes": "0", "live_rows": "6", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "user_sessions", "inserts": "12", "updates": "25", "deletes": "8", "live_rows": "4", "dead_rows": "12", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "platform_feature_flags", "inserts": "3", "updates": "0", "deletes": "0", "live_rows": "3", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "security_audit_log", "inserts": "10", "updates": "3", "deletes": "7", "live_rows": "3", "dead_rows": "10", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "user_roles", "inserts": "5", "updates": "0", "deletes": "2", "live_rows": "3", "dead_rows": "2", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "institute_branding", "inserts": "2", "updates": "1", "deletes": "1", "live_rows": "1", "dead_rows": "2", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "institute_domains", "inserts": "2", "updates": "4", "deletes": "1", "live_rows": "1", "dead_rows": "5", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "student_profiles", "inserts": "8", "updates": "0", "deletes": "8", "live_rows": "1", "dead_rows": "8", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "platform_analytics_cache", "inserts": "1", "updates": "0", "deletes": "0", "live_rows": "1", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "blocked_ips", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "custom_domains", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "ssl_certificates", "inserts": "1", "updates": "1", "deletes": "1", "live_rows": "0", "dead_rows": "2", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "rate_limit_violations", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "institute_usage_stats", "inserts": "1", "updates": "0", "deletes": "1", "live_rows": "0", "dead_rows": "1", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "domain_verification_logs", "inserts": "1", "updates": "0", "deletes": "1", "live_rows": "0", "dead_rows": "1", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "password_history", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "course_enrollments", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "two_factor_auth", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "subscription_history", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "platform_notifications", "inserts": "1", "updates": "0", "deletes": "1", "live_rows": "0", "dead_rows": "1", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "teacher_profiles", "inserts": "1", "updates": "0", "deletes": "1", "live_rows": "0", "dead_rows": "1", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "failed_login_attempts", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "super_admin_activity_log", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "institute_status_history", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "session_security", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "teacher_invitations", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}, {"schemaname": "public", "tablename": "maintenance_windows", "inserts": "0", "updates": "0", "deletes": "0", "live_rows": "0", "dead_rows": "0", "last_vacuum": null, "last_autovacuum": null, "last_analyze": null, "last_autoanalyze": null}], "indexUsage": [{"schemaname": "public", "tablename": "users", "indexname": "users_pkey", "idx_tup_read": "152", "idx_tup_fetch": "104", "idx_scan": "152", "usage_level": "HIGH_USAGE"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_pkey", "idx_tup_read": "111", "idx_tup_fetch": "107", "idx_scan": "111", "usage_level": "HIGH_USAGE"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_ip_address", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "46", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_user_id", "idx_tup_read": "22", "idx_tup_fetch": "19", "idx_scan": "44", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_institute_id", "idx_tup_read": "53", "idx_tup_fetch": "42", "idx_scan": "31", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_user_id", "idx_tup_read": "23", "idx_tup_fetch": "14", "idx_scan": "29", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "unique_user_role_per_institute", "idx_tup_read": "15", "idx_tup_fetch": "15", "idx_scan": "25", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_session_token", "idx_tup_read": "24", "idx_tup_fetch": "24", "idx_scan": "24", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_user_id", "idx_tup_read": "3", "idx_tup_fetch": "0", "idx_scan": "24", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_user_id", "idx_tup_read": "12", "idx_tup_fetch": "11", "idx_scan": "23", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_role", "idx_tup_read": "36", "idx_tup_fetch": "27", "idx_scan": "21", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email_institute", "idx_tup_read": "20", "idx_tup_fetch": "15", "idx_scan": "20", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_user_id", "idx_tup_read": "3", "idx_tup_fetch": "2", "idx_scan": "20", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_student_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "20", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_institute_id", "idx_tup_read": "5", "idx_tup_fetch": "2", "idx_scan": "18", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_slug", "idx_tup_read": "9", "idx_tup_fetch": "9", "idx_scan": "18", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_email", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "14", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_institute_id", "idx_tup_read": "5", "idx_tup_fetch": "4", "idx_scan": "14", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_user_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_user_id", "idx_tup_read": "3", "idx_tup_fetch": "3", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_changed_by", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_user_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_changed_by", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_user_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_institute_id", "idx_tup_read": "17", "idx_tup_fetch": "17", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_admin_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "12", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_ip_address", "idx_tup_read": "31", "idx_tup_fetch": "28", "idx_scan": "10", "usage_level": "MODERATE_USAGE"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_institute_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "9", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_institute_id", "idx_tup_read": "2", "idx_tup_fetch": "0", "idx_scan": "9", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_pkey", "idx_tup_read": "11", "idx_tup_fetch": "9", "idx_scan": "9", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_created_at", "idx_tup_read": "15", "idx_tup_fetch": "6", "idx_scan": "6", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "unique_setting_per_institute", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "6", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email", "idx_tup_read": "1", "idx_tup_fetch": "1", "idx_scan": "6", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_domain", "idx_tup_read": "6", "idx_tup_fetch": "4", "idx_scan": "6", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_config_key_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "6", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_event_type", "idx_tup_read": "8", "idx_tup_fetch": "1", "idx_scan": "5", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_institute_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "5", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_feature_name", "idx_tup_read": "1", "idx_tup_fetch": "1", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_institute_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_institute_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_institute_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_domain_id", "idx_tup_read": "5", "idx_tup_fetch": "3", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_institute_id", "idx_tup_read": "1", "idx_tup_fetch": "0", "idx_scan": "4", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "users", "indexname": "unique_email_per_institute", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "3", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_token", "idx_tup_read": "2", "idx_tup_fetch": "2", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_domain_id", "idx_tup_read": "2", "idx_tup_fetch": "1", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_severity", "idx_tup_read": "6", "idx_tup_fetch": "6", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_expires_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_institute_id_stat_date_key", "idx_tup_read": "1", "idx_tup_fetch": "1", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_pkey", "idx_tup_read": "2", "idx_tup_fetch": "2", "idx_scan": "2", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "security_audit_log_pkey", "idx_tup_read": "1", "idx_tup_fetch": "1", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_subscription_status", "idx_tup_read": "9", "idx_tup_fetch": "4", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_metric_type_metric_date_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_user_id_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_refresh_token_key", "idx_tup_read": "1", "idx_tup_fetch": "1", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_slug_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_user_id_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "1", "usage_level": "LOW_USAGE"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_window_start", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_config_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_is_active", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_session_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_ip_address", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_is_suspicious", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_last_activity", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_created_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_is_enabled", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "subscription_history_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "institute_status_history_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "super_admin_activity_log_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "platform_notifications_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "maintenance_windows_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_feature_name_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_created_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_created_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_type", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_date", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_action_type", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_target_type", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_created_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_audience", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_is_read", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_created_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_start_time", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_end_time", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_stat_date", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_is_enabled", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_email_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_domain_name_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_verification_token_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "user_roles_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_verification_token_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_session_token_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "institute_settings_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_domain_name", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_verification_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_role_name", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_expires_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_expires_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_domain_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "domain_verification_logs_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "ssl_certificates_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "institute_branding_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_active", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_verified", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_ssl_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_checked_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_expires_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_is_active", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_token_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "course_enrollments_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_student_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_academic_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_graduation_year", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employee_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_department", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employment_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_email", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_token", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_expires_at", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_course_id", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_status", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_ip_address_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "failed_login_attempts_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "rate_limit_violations_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "session_security", "indexname": "session_security_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "password_history", "indexname": "password_history_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_pkey", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_user_id_key", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_email", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_ip_address", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_attempt_time", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_ip_address", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_endpoint", "idx_tup_read": "0", "idx_tup_fetch": "0", "idx_scan": "0", "usage_level": "UNUSED"}], "indexSizes": [{"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_slug_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_email_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "idx_institute_settings_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "institute_domains_domain_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "domain_verification_logs_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "ssl_certificates_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "institute_branding_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_domain", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_active", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_is_verified", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_domains", "indexname": "idx_institute_domains_ssl_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_domain_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "domain_verification_logs", "indexname": "idx_domain_verification_logs_checked_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_domain_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_expires_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "ssl_certificates", "indexname": "idx_ssl_certificates_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_branding", "indexname": "idx_institute_branding_is_active", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "student_profiles_user_id_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "teacher_profiles_user_id_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_student_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_academic_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "student_profiles", "indexname": "idx_student_profiles_graduation_year", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employee_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_department", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "teacher_profiles", "indexname": "idx_teacher_profiles_employment_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "security_audit_log_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_config", "indexname": "security_config_config_key_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_event_type", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_severity", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_ip_address", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_audit_log", "indexname": "idx_security_audit_log_created_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_config_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "security_config", "indexname": "idx_security_config_is_active", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "platform_analytics_cache_metric_type_metric_date_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "platform_notifications_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "institute_usage_stats_institute_id_stat_date_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "platform_feature_flags_feature_name_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_type", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_analytics_cache", "indexname": "idx_platform_analytics_cache_metric_date", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_audience", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_target_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_is_read", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_notifications", "indexname": "idx_platform_notifications_created_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_usage_stats", "indexname": "idx_institute_usage_stats_stat_date", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_feature_name", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "platform_feature_flags", "indexname": "idx_platform_feature_flags_is_enabled", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "users_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "unique_email_per_institute", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "user_roles_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "unique_user_role_per_institute", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "email_verifications_verification_token_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_session_token_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "user_sessions_refresh_token_key", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "institute_settings_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institute_settings", "indexname": "unique_setting_per_institute", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_slug", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_email", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "idx_institutes_subscription_status", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_role", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "users", "indexname": "idx_users_email_institute", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_institute_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_roles", "indexname": "idx_user_roles_role_name", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_token", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "email_verifications", "indexname": "idx_email_verifications_expires_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_user_id", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_session_token", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "user_sessions", "indexname": "idx_user_sessions_expires_at", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "institutes", "indexname": "institutes_pkey", "index_size": "16 kB", "index_size_bytes": "16384"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_domain_name_key", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "custom_domains_verification_token_key", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_token_key", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "course_enrollments_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_is_enabled", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "subscription_history_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "institute_status_history_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_start_time", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_end_time", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "super_admin_activity_log_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_admin_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "maintenance_windows_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_institute_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_email", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_token", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "idx_teacher_invitations_expires_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_institute_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_domain_name", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "custom_domains", "indexname": "idx_custom_domains_verification_status", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_course_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_student_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "course_enrollments", "indexname": "idx_course_enrollments_status", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_action_type", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "blocked_ips_ip_address_key", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "failed_login_attempts_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "rate_limit_violations_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_target_type", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "super_admin_activity_log", "indexname": "idx_super_admin_activity_log_created_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "session_security_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "password_history", "indexname": "password_history_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "two_factor_auth_user_id_key", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "maintenance_windows", "indexname": "idx_maintenance_windows_status", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_institute_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_changed_by", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "subscription_history", "indexname": "idx_subscription_history_created_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_institute_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_ip_address", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "blocked_ips", "indexname": "idx_blocked_ips_expires_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_email", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_ip_address", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "failed_login_attempts", "indexname": "idx_failed_login_attempts_attempt_time", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_ip_address", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_endpoint", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "rate_limit_violations", "indexname": "idx_rate_limit_violations_window_start", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_changed_by", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "institute_status_history", "indexname": "idx_institute_status_history_created_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_session_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_user_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_ip_address", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_is_suspicious", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "session_security", "indexname": "idx_session_security_last_activity", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_user_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "password_history", "indexname": "idx_password_history_created_at", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "two_factor_auth", "indexname": "idx_two_factor_auth_user_id", "index_size": "8192 bytes", "index_size_bytes": "8192"}, {"schemaname": "public", "tablename": "teacher_invitations", "indexname": "teacher_invitations_pkey", "index_size": "8192 bytes", "index_size_bytes": "8192"}], "duplicateIndexes": [], "statsEnabled": false, "slowQueries": [], "connectionStats": [{"state": null, "connection_count": "5", "avg_duration_seconds": null}, {"state": "idle", "connection_count": "3", "avg_duration_seconds": "4.6678083333333333"}], "multiTenantQueries": [{"name": "Institute User Lookup", "executionTime": 0.056, "planningTime": 3.879, "totalCost": 10.61, "actualRows": 0, "plan": {"Plan": {"Node Type": "Nested Loop", "Parallel Aware": false, "Async Capable": false, "Join Type": "Left", "Startup Cost": 4.16, "Total Cost": 10.61, "Plan Rows": 55, "Plan Width": 728, "Actual Startup Time": 0.02, "Actual Total Time": 0.021, "Actual Rows": 0, "Actual Loops": 1, "Inner Unique": false, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Relation Name": "users", "Alias": "u", "Startup Cost": 0, "Total Cost": 1.09, "Plan Rows": 1, "Plan Width": 696, "Actual Startup Time": 0.02, "Actual Total Time": 0.02, "Actual Rows": 0, "Actual Loops": 1, "Filter": "((institute_id = '7a731a99-1277-4240-ad1c-d34a674f01c3'::uuid) AND ((email)::text = '<EMAIL>'::text))", "Rows Removed by Filter": 10, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, {"Node Type": "Bitmap Heap Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Async Capable": false, "Relation Name": "user_roles", "Alias": "ur", "Startup Cost": 4.16, "Total Cost": 9.5, "Plan Rows": 2, "Plan Width": 48, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Recheck Cond": "(u.id = user_id)", "Rows Removed by Index Recheck": 0, "Exact Heap Blocks": 0, "Lossy Heap Blocks": 0, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Bitmap Index Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Index Name": "idx_user_roles_user_id", "Startup Cost": 0, "Total Cost": 4.16, "Plan Rows": 2, "Plan Width": 0, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Index Cond": "(user_id = u.id)", "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}]}]}, "Planning": {"Shared Hit Blocks": 183, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, "Planning Time": 3.879, "Triggers": [], "Execution Time": 0.056}}, {"name": "Institute Students List", "executionTime": 0.046, "planningTime": 1.974, "totalCost": 9.72, "actualRows": 0, "plan": {"Plan": {"Node Type": "Limit", "Parallel Aware": false, "Async Capable": false, "Startup Cost": 9.69, "Total Cost": 9.72, "Plan Rows": 13, "Plan Width": 1044, "Actual Startup Time": 0.024, "Actual Total Time": 0.024, "Actual Rows": 0, "Actual Loops": 1, "Shared Hit Blocks": 4, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Sort", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Startup Cost": 9.69, "Total Cost": 9.72, "Plan Rows": 13, "Plan Width": 1044, "Actual Startup Time": 0.024, "Actual Total Time": 0.024, "Actual Rows": 0, "Actual Loops": 1, "Sort Key": ["u.created_at DESC"], "Sort Method": "quicksort", "Sort Space Used": 25, "Sort Space Type": "Memory", "Shared Hit Blocks": 4, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Join Type": "Left", "Startup Cost": 0.14, "Total Cost": 9.45, "Plan Rows": 13, "Plan Width": 1044, "Actual Startup Time": 0.011, "Actual Total Time": 0.011, "Actual Rows": 0, "Actual Loops": 1, "Inner Unique": true, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Relation Name": "users", "Alias": "u", "Startup Cost": 0, "Total Cost": 1.09, "Plan Rows": 1, "Plan Width": 696, "Actual Startup Time": 0.011, "Actual Total Time": 0.011, "Actual Rows": 0, "Actual Loops": 1, "Filter": "((institute_id = '7a731a99-1277-4240-ad1c-d34a674f01c3'::uuid) AND ((role)::text = 'student'::text))", "Rows Removed by Filter": 10, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Async Capable": false, "Scan Direction": "Forward", "Index Name": "idx_student_profiles_user_id", "Relation Name": "student_profiles", "Alias": "sp", "Startup Cost": 0.14, "Total Cost": 8.16, "Plan Rows": 1, "Plan Width": 364, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Index Cond": "(user_id = u.id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}]}]}]}, "Planning": {"Shared Hit Blocks": 62, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, "Planning Time": 1.974, "Triggers": [], "Execution Time": 0.046}}, {"name": "Active Sessions by Institute", "executionTime": 0.028, "planningTime": 1.644, "totalCost": 12.19, "actualRows": 0, "plan": {"Plan": {"Node Type": "Nested Loop", "Parallel Aware": false, "Async Capable": false, "Join Type": "Inner", "Startup Cost": 0, "Total Cost": 12.19, "Plan Rows": 1, "Plan Width": 1206, "Actual Startup Time": 0.013, "Actual Total Time": 0.013, "Actual Rows": 0, "Actual Loops": 1, "Inner Unique": true, "Join Filter": "(us.user_id = u.id)", "Rows Removed by Join Filter": 0, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Relation Name": "user_sessions", "Alias": "us", "Startup Cost": 0, "Total Cost": 11.05, "Plan Rows": 1, "Plan Width": 1169, "Actual Startup Time": 0.012, "Actual Total Time": 0.012, "Actual Rows": 0, "Actual Loops": 1, "Filter": "(is_active AND (institute_id = '7a731a99-1277-4240-ad1c-d34a674f01c3'::uuid) AND (expires_at > CURRENT_TIMESTAMP))", "Rows Removed by Filter": 4, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, {"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Inner", "Parallel Aware": false, "Async Capable": false, "Relation Name": "users", "Alias": "u", "Startup Cost": 0, "Total Cost": 1.06, "Plan Rows": 6, "Plan Width": 53, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}]}, "Planning": {"Shared Hit Blocks": 38, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, "Planning Time": 1.644, "Triggers": [], "Execution Time": 0.028}}, {"name": "Security Audit Log Query", "executionTime": 0.028, "planningTime": 1.672, "totalCost": 9.3, "actualRows": 0, "plan": {"Plan": {"Node Type": "Limit", "Parallel Aware": false, "Async Capable": false, "Startup Cost": 9.29, "Total Cost": 9.3, "Plan Rows": 4, "Plan Width": 928, "Actual Startup Time": 0.013, "Actual Total Time": 0.013, "Actual Rows": 0, "Actual Loops": 1, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Sort", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Startup Cost": 9.29, "Total Cost": 9.3, "Plan Rows": 4, "Plan Width": 928, "Actual Startup Time": 0.012, "Actual Total Time": 0.012, "Actual Rows": 0, "Actual Loops": 1, "Sort Key": ["sal.created_at DESC"], "Sort Method": "quicksort", "Sort Space Used": 25, "Sort Space Type": "Memory", "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Join Type": "Inner", "Startup Cost": 0.14, "Total Cost": 9.25, "Plan Rows": 4, "Plan Width": 928, "Actual Startup Time": 0.009, "Actual Total Time": 0.01, "Actual Rows": 0, "Actual Loops": 1, "Inner Unique": false, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Relation Name": "users", "Alias": "u", "Startup Cost": 0, "Total Cost": 1.07, "Plan Rows": 1, "Plan Width": 16, "Actual Startup Time": 0.009, "Actual Total Time": 0.009, "Actual Rows": 0, "Actual Loops": 1, "Filter": "(institute_id = '7a731a99-1277-4240-ad1c-d34a674f01c3'::uuid)", "Rows Removed by Filter": 10, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Async Capable": false, "Scan Direction": "Forward", "Index Name": "idx_security_audit_log_user_id", "Relation Name": "security_audit_log", "Alias": "sal", "Startup Cost": 0.14, "Total Cost": 8.17, "Plan Rows": 1, "Plan Width": 928, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Index Cond": "(user_id = u.id)", "Rows Removed by Index Recheck": 0, "Filter": "(created_at >= (CURRENT_DATE - '7 days'::interval))", "Rows Removed by Filter": 0, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}]}]}]}, "Planning": {"Shared Hit Blocks": 43, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, "Planning Time": 1.672, "Triggers": [], "Execution Time": 0.028}}, {"name": "Institute Domain Lookup", "executionTime": 0.034, "planningTime": 4.161, "totalCost": 16.83, "actualRows": 0, "plan": {"Plan": {"Node Type": "Nested Loop", "Parallel Aware": false, "Async Capable": false, "Join Type": "Inner", "Startup Cost": 0.28, "Total Cost": 16.83, "Plan Rows": 1, "Plan Width": 3296, "Actual Startup Time": 0.016, "Actual Total Time": 0.016, "Actual Rows": 0, "Actual Loops": 1, "Inner Unique": true, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "Plans": [{"Node Type": "Index Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "Async Capable": false, "Scan Direction": "Forward", "Index Name": "idx_institute_domains_domain", "Relation Name": "institute_domains", "Alias": "id", "Startup Cost": 0.14, "Total Cost": 8.15, "Plan Rows": 1, "Plan Width": 651, "Actual Startup Time": 0.015, "Actual Total Time": 0.015, "Actual Rows": 0, "Actual Loops": 1, "Index Cond": "((domain)::text = 'example.edu'::text)", "Rows Removed by Index Recheck": 0, "Filter": "is_active", "Rows Removed by Filter": 0, "Shared Hit Blocks": 1, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Async Capable": false, "Scan Direction": "Forward", "Index Name": "institutes_pkey", "Relation Name": "institutes", "Alias": "i", "Startup Cost": 0.14, "Total Cost": 8.15, "Plan Rows": 1, "Plan Width": 2661, "Actual Startup Time": 0, "Actual Total Time": 0, "Actual Rows": 0, "Actual Loops": 0, "Index Cond": "(id = id.institute_id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}]}, "Planning": {"Shared Hit Blocks": 114, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0}, "Planning Time": 4.161, "Triggers": [], "Execution Time": 0.034}}]}, "recommendations": [{"category": "Index Optimization", "priority": "low", "issue": "10 unused indexes found", "recommendation": "Consider dropping unused indexes: idx_rate_limit_violations_window_start, idx_security_config_config_key, idx_security_config_is_active, idx_session_security_session_id, idx_session_security_ip_address, idx_session_security_is_suspicious, idx_session_security_last_activity, idx_password_history_created_at, idx_two_factor_auth_is_enabled, platform_feature_flags_feature_name_key"}], "summary": {"totalTables": 32, "totalIndexes": 147, "unusedIndexes": 89, "slowQueries": 0, "recommendations": 1}}