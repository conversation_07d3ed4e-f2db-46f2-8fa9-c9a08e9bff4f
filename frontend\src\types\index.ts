// User and Authentication Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId: string
  isActive: boolean
  isEmailVerified: boolean
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  profilePicture?: string
}

export type UserRole = 'super_admin' | 'institute_admin' | 'teacher' | 'student'

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId?: string
}

// Institute Types
export interface Institute {
  id: string
  name: string
  email: string
  website?: string
  address?: string
  phone?: string
  subscriptionPlan: SubscriptionPlan
  subscriptionStatus: SubscriptionStatus
  isActive: boolean
  createdAt: string
  updatedAt: string
  allowedDomains?: string[]
  timezone?: string
  language?: string
}

export type SubscriptionPlan = 'free' | 'basic' | 'premium' | 'enterprise'
export type SubscriptionStatus = 'active' | 'inactive' | 'suspended' | 'cancelled'

export interface InstituteBranding {
  id: string
  instituteId: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  logoUrl?: string
  faviconUrl?: string
  customCss?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface InstituteSettings {
  id: string
  instituteId: string
  allowedDomains: string[]
  registrationSettings: {
    requireApproval: boolean
    allowSelfRegistration: boolean
    requireEmailVerification: boolean
    defaultRole: UserRole
  }
  notificationSettings: {
    emailNotifications: boolean
    smsNotifications: boolean
    pushNotifications: boolean
    weeklyDigest: boolean
  }
  createdAt: string
  updatedAt: string
}

// Student and Teacher Profile Types
export interface StudentProfile {
  id: string
  userId: string
  studentId?: string
  major?: string
  graduationYear?: number
  academicStatus: AcademicStatus
  gpa?: number
  enrollmentDate: string
  createdAt: string
  updatedAt: string
}

export type AcademicStatus = 'active' | 'inactive' | 'graduated' | 'suspended' | 'withdrawn'

export interface TeacherProfile {
  id: string
  userId: string
  employeeId?: string
  department?: string
  position?: string
  hireDate?: string
  qualifications?: string[]
  bio?: string
  officeLocation?: string
  officeHours?: string
  createdAt: string
  updatedAt: string
}

// Course Types
export interface Course {
  id: string
  instituteId: string
  title: string
  description: string
  code: string
  credits: number
  teacherId: string
  status: CourseStatus
  startDate: string
  endDate: string
  maxStudents?: number
  currentEnrollment: number
  createdAt: string
  updatedAt: string
}

export type CourseStatus = 'draft' | 'published' | 'active' | 'completed' | 'archived'

export interface CourseEnrollment {
  id: string
  courseId: string
  studentId: string
  enrollmentStatus: EnrollmentStatus
  enrollmentDate: string
  completionDate?: string
  grade?: string
  createdAt: string
  updatedAt: string
}

export type EnrollmentStatus = 'enrolled' | 'completed' | 'dropped' | 'failed'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: string[]
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file'
  placeholder?: string
  required?: boolean
  options?: { value: string; label: string }[]
  validation?: any
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

// UI Component Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export interface TableColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
  className?: string
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (page: number) => void
  }
  onSort?: (column: string, direction: 'asc' | 'desc') => void
  className?: string
}

// Navigation Types
export interface NavItem {
  label: string
  href: string
  icon?: React.ComponentType<any>
  children?: NavItem[]
  roles?: UserRole[]
}

export interface BreadcrumbItem {
  label: string
  href?: string
}

// Dashboard Types
export interface DashboardStats {
  totalUsers: number
  totalStudents: number
  totalTeachers: number
  totalCourses: number
  activeEnrollments: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'user_registration' | 'course_enrollment' | 'login' | 'course_completion'
  description: string
  timestamp: string
  userId?: string
  metadata?: Record<string, any>
}

// Security Types
export interface SecurityEvent {
  id: string
  eventType: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  ipAddress: string
  userAgent: string
  userId?: string
  instituteId?: string
  createdAt: string
}

export interface Session {
  id: string
  userId: string
  ipAddress: string
  userAgent: string
  isActive: boolean
  createdAt: string
  lastActivity: string
  expiresAt: string
}

// File Upload Types
export interface FileUpload {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  url?: string
  error?: string
}

// Theme Types
export interface Theme {
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
    muted: string
    border: string
  }
  fonts: {
    sans: string
    mono: string
  }
}

// Error Types
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
}

// Store Types (for Zustand)
export interface AppStore {
  // Auth
  auth: AuthState
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  register: (data: RegisterData) => Promise<void>
  
  // UI
  theme: 'light' | 'dark' | 'system'
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  
  // Loading states
  loading: Record<string, boolean>
  setLoading: (key: string, loading: boolean) => void
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
