@echo off
setlocal enabledelayedexpansion

REM LMS SAAS Platform - Development Setup Script (Windows)
REM This script sets up the development environment for the LMS SAAS platform

echo ==================================================
echo 🚀 LMS SAAS Platform - Development Setup
echo ==================================================
echo.

REM Check Node.js
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do set NODE_MAJOR=%%a
set NODE_MAJOR=%NODE_MAJOR:v=%
if %NODE_MAJOR% LSS 18 (
    echo [ERROR] Node.js version 18 or higher is required.
    pause
    exit /b 1
)

echo [SUCCESS] Node.js version check passed

REM Check npm
echo [INFO] Checking npm installation...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo [SUCCESS] npm is available

REM Install dependencies
echo.
echo [INFO] Installing dependencies...

echo [INFO] Installing root dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] Dependencies installed successfully

REM Setup environment files
echo.
echo [INFO] Setting up environment files...

if not exist "frontend\.env.local" (
    echo [INFO] Creating frontend environment file...
    copy "frontend\.env.example" "frontend\.env.local" >nul
    echo [SUCCESS] Frontend .env.local created from template
) else (
    echo [WARNING] Frontend .env.local already exists, skipping...
)

if not exist "backend\.env" (
    echo [INFO] Creating backend environment file...
    copy "backend\.env.example" "backend\.env" >nul
    echo [SUCCESS] Backend .env created from template
) else (
    echo [WARNING] Backend .env already exists, skipping...
)

echo [SUCCESS] Environment files setup completed

REM Create necessary directories
echo.
echo [INFO] Creating necessary directories...

if not exist "backend\logs" mkdir "backend\logs"
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "backend\temp" mkdir "backend\temp"
if not exist "frontend\public\uploads" mkdir "frontend\public\uploads"

echo [SUCCESS] Directories created

REM Setup Git hooks
echo.
echo [INFO] Setting up Git hooks...

if exist ".git" (
    call npx husky install
    if errorlevel 1 (
        echo [WARNING] Failed to setup Git hooks
    ) else (
        echo [SUCCESS] Git hooks setup completed
    )
) else (
    echo [WARNING] Not a Git repository. Skipping Git hooks setup.
)

REM Run initial checks
echo.
echo [INFO] Running initial checks...

echo [INFO] Running TypeScript checks...
call npm run type-check
if errorlevel 1 (
    echo [WARNING] TypeScript checks failed
) else (
    echo [SUCCESS] TypeScript checks passed
)

echo [INFO] Running linting checks...
call npm run lint
if errorlevel 1 (
    echo [WARNING] Linting checks failed
) else (
    echo [SUCCESS] Linting checks passed
)

echo.
echo ==================================================
echo ✅ Development setup completed successfully!
echo ==================================================
echo.
echo Next steps:
echo 1. Review and update environment files:
echo    - frontend\.env.local
echo    - backend\.env
echo.
echo 2. Start the development servers:
echo    npm run dev
echo.
echo 3. Access the application:
echo    - Frontend: http://localhost:3000
echo    - Backend API: http://localhost:5010
echo.
echo 4. For database management (optional):
echo    docker-compose -f docker-compose.dev.yml up pgadmin
echo    Access pgAdmin at: http://localhost:5050
echo.
echo Happy coding! 🎉
echo.
pause
