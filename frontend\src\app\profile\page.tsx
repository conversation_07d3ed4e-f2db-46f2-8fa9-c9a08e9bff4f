'use client'

import React, { useState } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin,
  Settings,
  Lock,
  Save,
  Camera,
  Shield,
  Bell,
  Globe,
  Palette
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { FormField, SelectField } from '@/components/forms/FormField'
import { Modal } from '@/components/ui/modal'

// Profile validation schema
const profileSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .required('First name is required'),
  lastName: Yup.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .required('Last name is required'),
  phone: Yup.string()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number')
    .nullable(),
  dateOfBirth: Yup.date()
    .max(new Date(), 'Date of birth cannot be in the future')
    .nullable(),
  address: Yup.object().shape({
    street: Yup.string().max(100, 'Street address is too long'),
    city: Yup.string().max(50, 'City name is too long'),
    state: Yup.string().max(50, 'State name is too long'),
    country: Yup.string().max(50, 'Country name is too long'),
    zipCode: Yup.string().max(20, 'ZIP code is too long'),
  }).nullable(),
})

// Password change validation schema
const passwordSchema = Yup.object().shape({
  currentPassword: Yup.string()
    .required('Current password is required'),
  newPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    )
    .required('New password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword')], 'Passwords must match')
    .required('Please confirm your password'),
})

interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  instituteName: string
  phone?: string
  dateOfBirth?: string
  address?: {
    street: string
    city: string
    state: string
    country: string
    zipCode: string
  }
  preferences: {
    language: string
    timezone: string
    theme: string
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
  }
  metadata?: {
    studentId?: string
    employeeId?: string
    department?: string
    yearOfStudy?: number
    major?: string
    bio?: string
  }
}

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
]

const timezoneOptions = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time' },
  { value: 'America/Chicago', label: 'Central Time' },
  { value: 'America/Denver', label: 'Mountain Time' },
  { value: 'America/Los_Angeles', label: 'Pacific Time' },
  { value: 'Europe/London', label: 'London' },
  { value: 'Europe/Paris', label: 'Paris' },
  { value: 'Asia/Tokyo', label: 'Tokyo' },
  { value: 'Asia/Shanghai', label: 'Shanghai' },
  { value: 'Asia/Kolkata', label: 'Kolkata' },
]

const themeOptions = [
  { value: 'light', label: 'Light' },
  { value: 'dark', label: 'Dark' },
  { value: 'auto', label: 'Auto' },
]

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)

  // Mock user profile data
  const mockProfile: UserProfile = {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'student',
    instituteName: 'Example University',
    phone: '+****************',
    dateOfBirth: '1995-06-15',
    address: {
      street: '123 University Ave',
      city: 'College Town',
      state: 'CA',
      country: 'United States',
      zipCode: '12345',
    },
    preferences: {
      language: 'en',
      timezone: 'America/Los_Angeles',
      theme: 'light',
      notifications: {
        email: true,
        push: true,
        sms: false,
      },
    },
    metadata: {
      studentId: 'STU123456',
      department: 'Computer Science',
      yearOfStudy: 3,
      major: 'Software Engineering',
      bio: 'Passionate about technology and learning new things.',
    },
  }

  const handleProfileSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Updating profile:', values)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('Profile updated successfully!')
    } catch (error) {
      console.error('Profile update error:', error)
      toast.error('Failed to update profile')
    } finally {
      setSubmitting(false)
    }
  }

  const handlePasswordSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Changing password')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('Password changed successfully!')
      setIsPasswordModalOpen(false)
      resetForm()
    } catch (error) {
      console.error('Password change error:', error)
      toast.error('Failed to change password')
    } finally {
      setSubmitting(false)
    }
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'preferences', label: 'Preferences', icon: Settings },
    { id: 'security', label: 'Security', icon: Shield },
  ]

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Profile Settings</h1>
          <p className="text-gray-600 mt-1">
            Manage your account settings and preferences
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
            <span className="text-white font-semibold">
              {mockProfile.firstName[0]}{mockProfile.lastName[0]}
            </span>
          </div>
          <div>
            <p className="font-medium text-gray-900">
              {mockProfile.firstName} {mockProfile.lastName}
            </p>
            <p className="text-sm text-gray-600 capitalize">
              {mockProfile.role} • {mockProfile.instituteName}
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <Formik
            initialValues={{
              firstName: mockProfile.firstName,
              lastName: mockProfile.lastName,
              phone: mockProfile.phone || '',
              dateOfBirth: mockProfile.dateOfBirth || '',
              address: mockProfile.address || {
                street: '',
                city: '',
                state: '',
                country: '',
                zipCode: '',
              },
              metadata: mockProfile.metadata || {},
            }}
            validationSchema={profileSchema}
            onSubmit={handleProfileSubmit}
          >
            {({ isSubmitting }) => (
              <Form className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Personal Information
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      name="firstName"
                      label="First Name"
                      placeholder="Enter your first name"
                      required
                    />
                    
                    <FormField
                      name="lastName"
                      label="Last Name"
                      placeholder="Enter your last name"
                      required
                    />
                    
                    <div className="md:col-span-2">
                      <FormField
                        name="email"
                        label="Email Address"
                        value={mockProfile.email}
                        disabled
                        description="Email cannot be changed. Contact your administrator if needed."
                      />
                    </div>
                    
                    <FormField
                      name="phone"
                      label="Phone Number"
                      type="tel"
                      placeholder="+****************"
                    />
                    
                    <FormField
                      name="dateOfBirth"
                      label="Date of Birth"
                      type="date"
                    />
                  </div>
                </div>

                {/* Address */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Address
                  </h3>
                  
                  <div className="space-y-4">
                    <FormField
                      name="address.street"
                      label="Street Address"
                      placeholder="123 Main Street"
                    />
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        name="address.city"
                        label="City"
                        placeholder="City"
                      />
                      
                      <FormField
                        name="address.state"
                        label="State/Province"
                        placeholder="State"
                      />
                      
                      <FormField
                        name="address.zipCode"
                        label="ZIP/Postal Code"
                        placeholder="12345"
                      />
                    </div>
                    
                    <FormField
                      name="address.country"
                      label="Country"
                      placeholder="United States"
                    />
                  </div>
                </div>

                {/* Role-specific metadata */}
                {mockProfile.role === 'student' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Academic Information
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        name="metadata.studentId"
                        label="Student ID"
                        value={mockProfile.metadata?.studentId}
                        disabled
                        description="Student ID is assigned by your institution"
                      />
                      
                      <FormField
                        name="metadata.department"
                        label="Department"
                        placeholder="Computer Science"
                      />
                      
                      <FormField
                        name="metadata.major"
                        label="Major"
                        placeholder="Software Engineering"
                      />
                      
                      <FormField
                        name="metadata.yearOfStudy"
                        label="Year of Study"
                        type="number"
                        min="1"
                        max="10"
                        placeholder="3"
                      />
                    </div>
                    
                    <FormField
                      name="metadata.bio"
                      label="Bio"
                      as="textarea"
                      rows={3}
                      placeholder="Tell us about yourself..."
                    />
                  </div>
                )}

                <div className="flex justify-end">
                  <Button type="submit" loading={isSubmitting}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        )}

        {/* Preferences Tab */}
        {activeTab === 'preferences' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Language & Region
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Language
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    {languageOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timezone
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    {timezoneOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Appearance
              </h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Theme
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {themeOptions.map(option => (
                    <div
                      key={option.value}
                      className="relative p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-gray-300"
                    >
                      <div className="flex items-center space-x-2">
                        <Palette className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">{option.label}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Notifications
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email Notifications</p>
                      <p className="text-xs text-gray-600">Receive notifications via email</p>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    defaultChecked={mockProfile.preferences.notifications.email}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Push Notifications</p>
                      <p className="text-xs text-gray-600">Receive push notifications in browser</p>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    defaultChecked={mockProfile.preferences.notifications.push}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">SMS Notifications</p>
                      <p className="text-xs text-gray-600">Receive notifications via SMS</p>
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    defaultChecked={mockProfile.preferences.notifications.sms}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </Button>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Password
              </h3>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Password</p>
                    <p className="text-xs text-gray-600">Last changed 30 days ago</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setIsPasswordModalOpen(true)}
                  >
                    <Lock className="mr-2 h-4 w-4" />
                    Change Password
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Account Status
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Email Verified</p>
                      <p className="text-xs text-green-700">Your email address has been verified</p>
                    </div>
                  </div>
                  <span className="text-green-600 text-sm font-medium">Verified</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Account Active</p>
                      <p className="text-xs text-green-700">Your account is active and in good standing</p>
                    </div>
                  </div>
                  <span className="text-green-600 text-sm font-medium">Active</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Change Password Modal */}
      <Modal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
        title="Change Password"
        size="md"
      >
        <Formik
          initialValues={{
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          }}
          validationSchema={passwordSchema}
          onSubmit={handlePasswordSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-4">
              <FormField
                name="currentPassword"
                label="Current Password"
                type="password"
                placeholder="Enter your current password"
                required
              />
              
              <FormField
                name="newPassword"
                label="New Password"
                type="password"
                placeholder="Enter your new password"
                required
              />
              
              <FormField
                name="confirmPassword"
                label="Confirm New Password"
                type="password"
                placeholder="Confirm your new password"
                required
              />
              
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-1">
                  Password Requirements
                </h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• At least 8 characters long</li>
                  <li>• Contains at least one lowercase letter</li>
                  <li>• Contains at least one uppercase letter</li>
                  <li>• Contains at least one number</li>
                </ul>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsPasswordModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" loading={isSubmitting}>
                  Change Password
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Modal>
    </div>
  )
}
