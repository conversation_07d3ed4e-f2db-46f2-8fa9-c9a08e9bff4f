const { passwordUtils, tokenUtils, verificationUtils } = require('../utils/auth');
const authService = require('../services/authService');
const { query } = require('../database/connection');

/**
 * Authentication System Test Suite
 * Tests password hashing, JWT tokens, user registration, login, and email verification
 */

class AuthTester {
  constructor() {
    this.testResults = [];
    this.testData = {
      testEmail: `test${Date.now()}@example.com`,
      testPassword: 'TestPassword123!',
      testInstitute: null,
      testUser: null,
      testTokens: {}
    };
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Test password utilities
   */
  async testPasswordUtils() {
    console.log('\n🔐 Testing Password Utilities...');

    try {
      // Test password hashing
      const hashedPassword = await passwordUtils.hashPassword(this.testData.testPassword);
      this.logResult(
        'Password Hashing',
        hashedPassword && hashedPassword.length > 50,
        `Generated hash: ${hashedPassword.substring(0, 20)}...`
      );

      // Test password comparison (correct password)
      const isValidPassword = await passwordUtils.comparePassword(this.testData.testPassword, hashedPassword);
      this.logResult(
        'Password Comparison (Valid)',
        isValidPassword === true,
        'Correct password verified'
      );

      // Test password comparison (incorrect password)
      const isInvalidPassword = await passwordUtils.comparePassword('WrongPassword123!', hashedPassword);
      this.logResult(
        'Password Comparison (Invalid)',
        isInvalidPassword === false,
        'Incorrect password rejected'
      );

      // Test password strength validation
      const strongPassword = passwordUtils.validatePasswordStrength('StrongPass123!');
      this.logResult(
        'Strong Password Validation',
        strongPassword.isValid === true,
        'Strong password accepted'
      );

      const weakPassword = passwordUtils.validatePasswordStrength('weak');
      this.logResult(
        'Weak Password Validation',
        weakPassword.isValid === false && weakPassword.errors.length > 0,
        `Weak password rejected: ${weakPassword.errors.length} errors`
      );

    } catch (error) {
      this.logResult('Password Utilities', false, error.message);
    }
  }

  /**
   * Test JWT token utilities
   */
  async testTokenUtils() {
    console.log('\n🎫 Testing JWT Token Utilities...');

    try {
      // Test token generation
      const payload = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        role: 'student'
      };

      const token = tokenUtils.generateToken(payload);
      this.logResult(
        'JWT Token Generation',
        token && token.split('.').length === 3,
        `Generated token: ${token.substring(0, 30)}...`
      );

      this.testData.testTokens.valid = token;

      // Test token verification (valid token)
      const verificationResult = tokenUtils.verifyToken(token);
      this.logResult(
        'JWT Token Verification (Valid)',
        verificationResult.isValid === true && verificationResult.payload.userId === payload.userId,
        'Valid token verified successfully'
      );

      // Test token verification (invalid token)
      const invalidVerification = tokenUtils.verifyToken('invalid.token.here');
      this.logResult(
        'JWT Token Verification (Invalid)',
        invalidVerification.isValid === false,
        'Invalid token rejected'
      );

      // Test refresh token generation
      const refreshToken = tokenUtils.generateRefreshToken();
      this.logResult(
        'Refresh Token Generation',
        refreshToken && refreshToken.length > 50,
        `Generated refresh token: ${refreshToken.substring(0, 20)}...`
      );

    } catch (error) {
      this.logResult('JWT Token Utilities', false, error.message);
    }
  }

  /**
   * Test verification token utilities
   */
  async testVerificationUtils() {
    console.log('\n📧 Testing Verification Utilities...');

    try {
      // Test email verification token
      const emailToken = verificationUtils.generateEmailVerificationToken();
      this.logResult(
        'Email Verification Token',
        emailToken.token && emailToken.expires instanceof Date,
        `Token: ${emailToken.token.substring(0, 20)}..., Expires: ${emailToken.expires.toISOString()}`
      );

      // Test password reset token
      const resetToken = verificationUtils.generatePasswordResetToken();
      this.logResult(
        'Password Reset Token',
        resetToken.token && resetToken.expires instanceof Date,
        `Token: ${resetToken.token.substring(0, 20)}..., Expires: ${resetToken.expires.toISOString()}`
      );

      // Test session token
      const sessionToken = verificationUtils.generateSessionToken();
      this.logResult(
        'Session Token Generation',
        sessionToken && sessionToken.length > 50,
        `Session token: ${sessionToken.substring(0, 20)}...`
      );

    } catch (error) {
      this.logResult('Verification Utilities', false, error.message);
    }
  }

  /**
   * Test institute admin registration
   */
  async testInstituteAdminRegistration() {
    console.log('\n🏫 Testing Institute Admin Registration...');

    try {
      const instituteData = {
        email: this.testData.testEmail,
        password: this.testData.testPassword,
        firstName: 'Test',
        lastName: 'Admin',
        phone: '+1234567890',
        instituteName: `Test Institute ${Date.now()}`,
        instituteEmail: `institute${Date.now()}@example.com`,
        institutePhone: '+1234567890',
        instituteAddress: '123 Test Street, Test City',
        instituteWebsite: 'https://testinstitute.com'
      };

      const result = await authService.registerInstituteAdmin(instituteData);
      
      this.logResult(
        'Institute Admin Registration',
        result.institute && result.user && result.verificationToken,
        `Institute: ${result.institute.name}, Admin: ${result.user.email}`
      );

      this.testData.testInstitute = result.institute;
      this.testData.testUser = result.user;
      this.testData.verificationToken = result.verificationToken;

    } catch (error) {
      this.logResult('Institute Admin Registration', false, error.message);
    }
  }

  /**
   * Test email verification
   */
  async testEmailVerification() {
    console.log('\n📬 Testing Email Verification...');

    if (!this.testData.verificationToken) {
      this.logResult('Email Verification', false, 'No verification token available');
      return;
    }

    try {
      const verifiedUser = await authService.verifyEmail(this.testData.verificationToken, this.testData.testEmail);
      
      this.logResult(
        'Email Verification',
        verifiedUser && verifiedUser.is_email_verified === true,
        `User ${verifiedUser.email} verified successfully`
      );

      this.testData.testUser = verifiedUser;

    } catch (error) {
      this.logResult('Email Verification', false, error.message);
    }
  }

  /**
   * Test user login
   */
  async testUserLogin() {
    console.log('\n🔑 Testing User Login...');

    if (!this.testData.testUser) {
      this.logResult('User Login', false, 'No test user available');
      return;
    }

    try {
      // Mock request object
      const mockReq = {
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test-Agent' : null,
        tenant: { instituteId: this.testData.testInstitute?.id }
      };

      const loginResult = await authService.loginUser(this.testData.testEmail, this.testData.testPassword, mockReq);
      
      this.logResult(
        'User Login',
        loginResult.user && loginResult.accessToken && loginResult.refreshToken,
        `Login successful for ${loginResult.user.email}`
      );

      this.testData.testTokens.access = loginResult.accessToken;
      this.testData.testTokens.refresh = loginResult.refreshToken;

      // Test login with wrong password
      try {
        await authService.loginUser(this.testData.testEmail, 'WrongPassword123!', mockReq);
        this.logResult('Login with Wrong Password', false, 'Should have failed but succeeded');
      } catch (error) {
        this.logResult(
          'Login with Wrong Password',
          error.message.includes('Invalid'),
          'Wrong password correctly rejected'
        );
      }

    } catch (error) {
      this.logResult('User Login', false, error.message);
    }
  }

  /**
   * Test password reset flow
   */
  async testPasswordReset() {
    console.log('\n🔄 Testing Password Reset...');

    if (!this.testData.testUser) {
      this.logResult('Password Reset', false, 'No test user available');
      return;
    }

    try {
      // Request password reset
      const resetRequest = await authService.requestPasswordReset(
        this.testData.testEmail, 
        this.testData.testInstitute?.id
      );
      
      this.logResult(
        'Password Reset Request',
        resetRequest.success && resetRequest.resetToken,
        'Password reset token generated'
      );

      // Reset password with token
      const newPassword = 'NewTestPassword123!';
      const resetResult = await authService.resetPassword(resetRequest.resetToken, newPassword);
      
      this.logResult(
        'Password Reset Completion',
        resetResult.success === true,
        'Password reset successfully'
      );

      // Test login with new password
      const mockReq = {
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test-Agent' : null,
        tenant: { instituteId: this.testData.testInstitute?.id }
      };

      const loginWithNewPassword = await authService.loginUser(this.testData.testEmail, newPassword, mockReq);
      
      this.logResult(
        'Login with New Password',
        loginWithNewPassword.user && loginWithNewPassword.accessToken,
        'Login successful with new password'
      );

    } catch (error) {
      this.logResult('Password Reset', false, error.message);
    }
  }

  /**
   * Test token refresh
   */
  async testTokenRefresh() {
    console.log('\n🔄 Testing Token Refresh...');

    if (!this.testData.testTokens.refresh) {
      this.logResult('Token Refresh', false, 'No refresh token available');
      return;
    }

    try {
      const refreshResult = await authService.refreshAccessToken(this.testData.testTokens.refresh);
      
      this.logResult(
        'Token Refresh',
        refreshResult.accessToken && refreshResult.user,
        `New access token generated for ${refreshResult.user.email}`
      );

    } catch (error) {
      this.logResult('Token Refresh', false, error.message);
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      if (this.testData.testUser) {
        // Delete user sessions
        await query('DELETE FROM user_sessions WHERE user_id = $1', [this.testData.testUser.id]);
        
        // Delete email verifications
        await query('DELETE FROM email_verifications WHERE user_id = $1', [this.testData.testUser.id]);
        
        // Delete user
        await query('DELETE FROM users WHERE id = $1', [this.testData.testUser.id]);
      }

      if (this.testData.testInstitute) {
        // Delete institute
        await query('DELETE FROM institutes WHERE id = $1', [this.testData.testInstitute.id]);
      }

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Run all authentication tests
   */
  async runAllTests() {
    console.log('🚀 Starting Authentication System Tests...\n');

    await this.testPasswordUtils();
    await this.testTokenUtils();
    await this.testVerificationUtils();
    await this.testInstituteAdminRegistration();
    await this.testEmailVerification();
    await this.testUserLogin();
    await this.testPasswordReset();
    await this.testTokenRefresh();

    // Cleanup
    await this.cleanupTestData();

    // Summary
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new AuthTester();
  tester.runAllTests()
    .then(summary => {
      console.log('\n🏁 Authentication testing completed!');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = { AuthTester };
