const fs = require('fs');
const path = require('path');
const { query, testConnection, closePool } = require('./connection');

/**
 * Database Migration Script
 * Creates all tables and indexes for the LMS SAAS application
 */

const runMigration = async () => {
  console.log('🚀 Starting database migration...');
  
  try {
    // Test database connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Cannot proceed with migration - database connection failed');
      process.exit(1);
    }

    // Read schema file
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');

    console.log('📄 Executing schema SQL...');
    
    // Execute the entire schema as one statement
    console.log('📝 Executing complete schema...');
    try {
      await query(schemaSql);
    } catch (err) {
      // Skip if table/extension already exists
      if (err.code === '42P07' || err.code === '42710') {
        console.log(`⚠️  Skipping: ${err.message}`);
      } else {
        throw err;
      }
    }

    console.log('✅ Database migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - institutes');
    console.log('   - custom_domains');
    console.log('   - users');
    console.log('   - user_roles');
    console.log('   - email_verifications');
    console.log('   - user_sessions');
    console.log('   - institute_settings');
    console.log('🔍 Created indexes for performance optimization');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await closePool();
  }
};

// Check if script is run directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
