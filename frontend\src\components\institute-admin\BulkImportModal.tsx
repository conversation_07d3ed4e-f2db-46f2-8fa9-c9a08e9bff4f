'use client'

import React, { useState, useRef } from 'react'
import { 
  X, 
  Upload, 
  Download, 
  FileText, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Users,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ImportedStudent {
  firstName: string
  lastName: string
  email: string
  phone?: string
  studentId: string
  dateOfBirth?: string
  address?: string
  emergencyContact?: string
  emergencyPhone?: string
}

interface BulkImportModalProps {
  isOpen: boolean
  onClose: () => void
  onImport: (students: ImportedStudent[]) => void
}

export default function BulkImportModal({ isOpen, onClose, onImport }: BulkImportModalProps) {
  const [step, setStep] = useState<'upload' | 'preview' | 'importing'>('upload')
  const [file, setFile] = useState<File | null>(null)
  const [importedData, setImportedData] = useState<ImportedStudent[]>([])
  const [errors, setErrors] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  if (!isOpen) return null

  const downloadTemplate = () => {
    const csvContent = `firstName,lastName,email,phone,studentId,dateOfBirth,address,emergencyContact,emergencyPhone
John,Doe,<EMAIL>,******-0123,STU001,1995-06-15,"123 Main St, City, State",Jane Doe (Mother),******-0124
Sarah,Johnson,<EMAIL>,******-0125,STU002,1996-08-20,"456 Oak Ave, City, State",Robert Johnson (Father),******-0126`

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'student_import_template.csv'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      processFile(selectedFile)
    }
  }

  const processFile = async (file: File) => {
    setIsProcessing(true)
    setErrors([])

    try {
      const text = await file.text()
      const lines = text.split('\n').filter(line => line.trim())
      
      if (lines.length < 2) {
        setErrors(['File must contain at least a header row and one data row'])
        setIsProcessing(false)
        return
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      const requiredHeaders = ['firstName', 'lastName', 'email', 'studentId']
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))
      
      if (missingHeaders.length > 0) {
        setErrors([`Missing required columns: ${missingHeaders.join(', ')}`])
        setIsProcessing(false)
        return
      }

      const students: ImportedStudent[] = []
      const newErrors: string[] = []

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
        
        if (values.length !== headers.length) {
          newErrors.push(`Row ${i + 1}: Column count mismatch`)
          continue
        }

        const student: any = {}
        headers.forEach((header, index) => {
          student[header] = values[index] || undefined
        })

        // Validate required fields
        if (!student.firstName || !student.lastName || !student.email || !student.studentId) {
          newErrors.push(`Row ${i + 1}: Missing required fields`)
          continue
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(student.email)) {
          newErrors.push(`Row ${i + 1}: Invalid email format`)
          continue
        }

        students.push(student as ImportedStudent)
      }

      setImportedData(students)
      setErrors(newErrors)
      
      if (students.length > 0) {
        setStep('preview')
      }
    } catch (error) {
      setErrors(['Failed to process file. Please ensure it is a valid CSV file.'])
    } finally {
      setIsProcessing(false)
    }
  }

  const handleImport = async () => {
    setStep('importing')
    
    // Simulate import process
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    onImport(importedData)
    handleClose()
  }

  const handleClose = () => {
    setStep('upload')
    setFile(null)
    setImportedData([])
    setErrors([])
    setIsProcessing(false)
    onClose()
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const droppedFile = e.dataTransfer.files[0]
    if (droppedFile && droppedFile.type === 'text/csv') {
      setFile(droppedFile)
      processFile(droppedFile)
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={handleClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                Bulk Import Students
              </h3>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {step === 'upload' && (
              <div className="space-y-6">
                {/* Instructions */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Import Instructions</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Download the CSV template to see the required format</li>
                    <li>• Required columns: firstName, lastName, email, studentId</li>
                    <li>• Optional columns: phone, dateOfBirth, address, emergencyContact, emergencyPhone</li>
                    <li>• Maximum 1000 students per import</li>
                  </ul>
                </div>

                {/* Template Download */}
                <div className="flex justify-center">
                  <Button variant="outline" onClick={downloadTemplate}>
                    <Download className="mr-2 h-4 w-4" />
                    Download CSV Template
                  </Button>
                </div>

                {/* File Upload */}
                <div
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  
                  {isProcessing ? (
                    <div className="space-y-4">
                      <Loader2 className="mx-auto h-12 w-12 text-blue-500 animate-spin" />
                      <p className="text-sm text-gray-600">Processing file...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Drop your CSV file here, or{' '}
                          <button
                            onClick={() => fileInputRef.current?.click()}
                            className="text-blue-600 hover:text-blue-500"
                          >
                            browse
                          </button>
                        </p>
                        <p className="text-xs text-gray-500 mt-1">CSV files only, max 10MB</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Errors */}
                {errors.length > 0 && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex">
                      <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                      <div>
                        <h4 className="text-sm font-medium text-red-800">Import Errors</h4>
                        <ul className="text-sm text-red-700 mt-1 space-y-1">
                          {errors.map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {step === 'preview' && (
              <div className="space-y-6">
                {/* Summary */}
                <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                    <div>
                      <h4 className="text-sm font-medium text-green-800">
                        Ready to Import
                      </h4>
                      <p className="text-sm text-green-700">
                        {importedData.length} students will be imported
                      </p>
                    </div>
                  </div>
                  <Users className="h-8 w-8 text-green-400" />
                </div>

                {/* Errors Summary */}
                {errors.length > 0 && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex">
                      <AlertCircle className="h-5 w-5 text-yellow-400 mr-2" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800">
                          {errors.length} rows skipped due to errors
                        </h4>
                        <details className="mt-2">
                          <summary className="text-sm text-yellow-700 cursor-pointer">
                            View errors
                          </summary>
                          <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                            {errors.slice(0, 10).map((error, index) => (
                              <li key={index}>• {error}</li>
                            ))}
                            {errors.length > 10 && (
                              <li>• ... and {errors.length - 10} more errors</li>
                            )}
                          </ul>
                        </details>
                      </div>
                    </div>
                  </div>
                )}

                {/* Preview Table */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900">
                      Preview ({importedData.slice(0, 5).length} of {importedData.length} students)
                    </h4>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Student ID</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Phone</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {importedData.slice(0, 5).map((student, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {student.firstName} {student.lastName}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900">{student.email}</td>
                            <td className="px-4 py-2 text-sm text-gray-900">{student.studentId}</td>
                            <td className="px-4 py-2 text-sm text-gray-500">{student.phone || '-'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {importedData.length > 5 && (
                    <div className="bg-gray-50 px-4 py-2 text-center">
                      <p className="text-sm text-gray-500">
                        ... and {importedData.length - 5} more students
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {step === 'importing' && (
              <div className="text-center py-12">
                <Loader2 className="mx-auto h-12 w-12 text-blue-500 animate-spin" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">Importing Students</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Please wait while we import {importedData.length} students...
                </p>
              </div>
            )}

            {/* Actions */}
            {step !== 'importing' && (
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                {step === 'preview' && (
                  <Button onClick={handleImport}>
                    Import {importedData.length} Students
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
