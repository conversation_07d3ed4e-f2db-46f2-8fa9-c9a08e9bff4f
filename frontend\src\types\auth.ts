export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  INSTITUTE_ADMIN = 'institute_admin',
  SUPER_ADMIN = 'super_admin'
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  instituteId?: string
  avatar?: string
  createdAt?: string
  updatedAt?: string
  lastLogin?: string
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  role?: UserRole
  instituteId?: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  token?: string
  message?: string
  error?: string
}
