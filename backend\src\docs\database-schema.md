# LMS SAAS Database Schema Documentation

Generated on: 2025-07-14T04:45:57.686Z

## Overview

This document provides comprehensive documentation for the LMS SAAS PostgreSQL database schema. The database is designed with multi-tenancy in mind, using institute-based data isolation for security and scalability.

## Database Architecture

### Multi-Tenancy Strategy
- **Institute-based isolation**: All tenant-specific data is linked to an `institute_id`
- **Shared schema**: Single database schema shared across all tenants
- **Data isolation**: Foreign key constraints and application-level filtering ensure data separation
- **Security**: Role-based access control (RBAC) with granular permissions

### Key Features
- UUID primary keys for better distribution and security
- Comprehensive audit logging for security and compliance
- Flexible user role system with JSON-based permissions
- Custom domain support with SSL certificate management
- Session management with security tracking

## Tables Overview

| Table Name | Purpose | Records |
|------------|---------|---------|
| blocked_ips | IP blocking system for security threat mitigation | - |
| course_enrollments | Student course enrollment tracking (basic structure) | - |
| custom_domains | No description available | - |
| domain_verification_logs | Audit trail for domain verification attempts | - |
| email_verifications | Email verification and password reset token management | - |
| failed_login_attempts | Failed login tracking for brute force detection | - |
| institute_branding | Institute-specific branding and customization settings | - |
| institute_domains | Custom domain management with verification and SSL support | - |
| institute_settings | Flexible key-value settings storage per institute | - |
| institute_status_history | Institute activation/deactivation tracking | - |
| institute_usage_stats | Detailed usage statistics per institute | - |
| institutes | Core tenant table storing institute information and subscription details | - |
| maintenance_windows | Scheduled maintenance tracking | - |
| password_history | No description available | - |
| platform_analytics_cache | Cached analytics data for performance optimization | - |
| platform_feature_flags | Feature rollout and A/B testing management | - |
| platform_notifications | System-wide notification management | - |
| rate_limit_violations | No description available | - |
| security_audit_log | Comprehensive security event logging and monitoring | - |
| security_config | No description available | - |
| session_security | No description available | - |
| ssl_certificates | SSL certificate management and auto-renewal tracking | - |
| student_profiles | Extended student information and academic tracking | - |
| subscription_history | Audit trail for subscription plan changes | - |
| super_admin_activity_log | Super admin action audit trail | - |
| teacher_invitations | Teacher invitation system with token-based verification | - |
| teacher_profiles | Teacher-specific information and employment details | - |
| two_factor_auth | Two-factor authentication settings and backup codes | - |
| user_roles | RBAC system with JSON-based permissions for flexible access control | - |
| user_sessions | Session management with security tracking and automatic cleanup | - |
| users | All user types (super_admin, institute_admin, teacher, student) with multi-tenant isolation | - |

## Detailed Table Documentation

### blocked_ips

IP blocking system for security threat mitigation

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| ip_address | inet | No | - | - |
| reason | text | No | - | - |
| event_count | integer | Yes | 1 | - |
| expires_at | timestamp without time zone | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80242_1_not_null | CHECK | - | - |
| 2200_80242_2_not_null | CHECK | - | - |
| 2200_80242_3_not_null | CHECK | - | - |
| 2200_80242_5_not_null | CHECK | - | - |
| blocked_ips_pkey | PRIMARY KEY | id | blocked_ips.id |
| blocked_ips_ip_address_key | UNIQUE | ip_address | blocked_ips.ip_address |

#### Indexes

| Name | Definition |
|------|------------|
| blocked_ips_ip_address_key | `CREATE UNIQUE INDEX blocked_ips_ip_address_key ON public.blocked_ips USING btree (ip_address)` |
| blocked_ips_pkey | `CREATE UNIQUE INDEX blocked_ips_pkey ON public.blocked_ips USING btree (id)` |
| idx_blocked_ips_expires_at | `CREATE INDEX idx_blocked_ips_expires_at ON public.blocked_ips USING btree (expires_at)` |
| idx_blocked_ips_ip_address | `CREATE INDEX idx_blocked_ips_ip_address ON public.blocked_ips USING btree (ip_address)` |

---

### course_enrollments

Student course enrollment tracking (basic structure)

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| course_id | uuid | No | - | - |
| student_id | uuid | No | - | - |
| enrolled_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| status | character varying(20) | Yes | 'active'::character varying | - |
| grade | character varying(5) | Yes | - | - |
| grade_points | numeric | Yes | - | - |
| completion_percentage | numeric | Yes | 0.00 | - |
| last_accessed_at | timestamp without time zone | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80197_1_not_null | CHECK | - | - |
| 2200_80197_2_not_null | CHECK | - | - |
| 2200_80197_3_not_null | CHECK | - | - |
| course_enrollments_student_id_fkey | FOREIGN KEY | student_id | users.id |
| course_enrollments_pkey | PRIMARY KEY | id | course_enrollments.id |

#### Indexes

| Name | Definition |
|------|------------|
| course_enrollments_pkey | `CREATE UNIQUE INDEX course_enrollments_pkey ON public.course_enrollments USING btree (id)` |
| idx_course_enrollments_course_id | `CREATE INDEX idx_course_enrollments_course_id ON public.course_enrollments USING btree (course_id)` |
| idx_course_enrollments_status | `CREATE INDEX idx_course_enrollments_status ON public.course_enrollments USING btree (status)` |
| idx_course_enrollments_student_id | `CREATE INDEX idx_course_enrollments_student_id ON public.course_enrollments USING btree (student_id)` |

---

### custom_domains

No description available

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| institute_id | uuid | No | - | - |
| domain_name | character varying(255) | No | - | - |
| verification_token | character varying(255) | No | - | - |
| verification_status | character varying(20) | Yes | 'pending'::character varying | - |
| ssl_status | character varying(20) | Yes | 'pending'::character varying | - |
| ssl_expires_at | timestamp without time zone | Yes | - | - |
| dns_configured | boolean | Yes | false | - |
| is_primary | boolean | Yes | false | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| verified_at | timestamp without time zone | Yes | - | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79884_1_not_null | CHECK | - | - |
| 2200_79884_2_not_null | CHECK | - | - |
| 2200_79884_3_not_null | CHECK | - | - |
| 2200_79884_4_not_null | CHECK | - | - |
| custom_domains_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| custom_domains_pkey | PRIMARY KEY | id | custom_domains.id |
| custom_domains_domain_name_key | UNIQUE | domain_name | custom_domains.domain_name |
| custom_domains_verification_token_key | UNIQUE | verification_token | custom_domains.verification_token |

#### Indexes

| Name | Definition |
|------|------------|
| custom_domains_domain_name_key | `CREATE UNIQUE INDEX custom_domains_domain_name_key ON public.custom_domains USING btree (domain_name)` |
| custom_domains_pkey | `CREATE UNIQUE INDEX custom_domains_pkey ON public.custom_domains USING btree (id)` |
| custom_domains_verification_token_key | `CREATE UNIQUE INDEX custom_domains_verification_token_key ON public.custom_domains USING btree (verification_token)` |
| idx_custom_domains_domain_name | `CREATE INDEX idx_custom_domains_domain_name ON public.custom_domains USING btree (domain_name)` |
| idx_custom_domains_institute_id | `CREATE INDEX idx_custom_domains_institute_id ON public.custom_domains USING btree (institute_id)` |
| idx_custom_domains_verification_status | `CREATE INDEX idx_custom_domains_verification_status ON public.custom_domains USING btree (verification_status)` |

---

### domain_verification_logs

Audit trail for domain verification attempts

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| domain_id | uuid | No | - | - |
| verification_type | character varying(50) | No | - | - |
| verification_status | character varying(50) | No | - | - |
| verification_details | jsonb | Yes | - | - |
| error_message | text | Yes | - | - |
| checked_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80063_1_not_null | CHECK | - | - |
| 2200_80063_2_not_null | CHECK | - | - |
| 2200_80063_3_not_null | CHECK | - | - |
| 2200_80063_4_not_null | CHECK | - | - |
| domain_verification_logs_domain_id_fkey | FOREIGN KEY | domain_id | institute_domains.id |
| domain_verification_logs_pkey | PRIMARY KEY | id | domain_verification_logs.id |

#### Indexes

| Name | Definition |
|------|------------|
| domain_verification_logs_pkey | `CREATE UNIQUE INDEX domain_verification_logs_pkey ON public.domain_verification_logs USING btree (id)` |
| idx_domain_verification_logs_checked_at | `CREATE INDEX idx_domain_verification_logs_checked_at ON public.domain_verification_logs USING btree (checked_at)` |
| idx_domain_verification_logs_domain_id | `CREATE INDEX idx_domain_verification_logs_domain_id ON public.domain_verification_logs USING btree (domain_id)` |
| idx_domain_verification_logs_status | `CREATE INDEX idx_domain_verification_logs_status ON public.domain_verification_logs USING btree (verification_status)` |

---

### email_verifications

Email verification and password reset token management

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| user_id | uuid | No | - | - |
| institute_id | uuid | Yes | - | - |
| email | character varying(255) | No | - | - |
| verification_token | character varying(255) | No | - | - |
| token_type | character varying(20) | No | - | - |
| expires_at | timestamp without time zone | No | - | - |
| is_used | boolean | Yes | false | - |
| used_at | timestamp without time zone | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79950_1_not_null | CHECK | - | - |
| 2200_79950_2_not_null | CHECK | - | - |
| 2200_79950_4_not_null | CHECK | - | - |
| 2200_79950_5_not_null | CHECK | - | - |
| 2200_79950_6_not_null | CHECK | - | - |
| 2200_79950_7_not_null | CHECK | - | - |
| email_verifications_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| email_verifications_user_id_fkey | FOREIGN KEY | user_id | users.id |
| email_verifications_pkey | PRIMARY KEY | id | email_verifications.id |
| email_verifications_verification_token_key | UNIQUE | verification_token | email_verifications.verification_token |

#### Indexes

| Name | Definition |
|------|------------|
| email_verifications_pkey | `CREATE UNIQUE INDEX email_verifications_pkey ON public.email_verifications USING btree (id)` |
| email_verifications_verification_token_key | `CREATE UNIQUE INDEX email_verifications_verification_token_key ON public.email_verifications USING btree (verification_token)` |
| idx_email_verifications_expires_at | `CREATE INDEX idx_email_verifications_expires_at ON public.email_verifications USING btree (expires_at)` |
| idx_email_verifications_token | `CREATE INDEX idx_email_verifications_token ON public.email_verifications USING btree (verification_token)` |
| idx_email_verifications_user_id | `CREATE INDEX idx_email_verifications_user_id ON public.email_verifications USING btree (user_id)` |

---

### failed_login_attempts

Failed login tracking for brute force detection

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| email | character varying(255) | No | - | - |
| ip_address | inet | No | - | - |
| user_agent | text | Yes | - | - |
| attempt_time | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| reason | character varying(100) | Yes | - | - |
| institute_id | uuid | Yes | - | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80255_1_not_null | CHECK | - | - |
| 2200_80255_2_not_null | CHECK | - | - |
| 2200_80255_3_not_null | CHECK | - | - |
| failed_login_attempts_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| failed_login_attempts_pkey | PRIMARY KEY | id | failed_login_attempts.id |

#### Indexes

| Name | Definition |
|------|------------|
| failed_login_attempts_pkey | `CREATE UNIQUE INDEX failed_login_attempts_pkey ON public.failed_login_attempts USING btree (id)` |
| idx_failed_login_attempts_attempt_time | `CREATE INDEX idx_failed_login_attempts_attempt_time ON public.failed_login_attempts USING btree (attempt_time)` |
| idx_failed_login_attempts_email | `CREATE INDEX idx_failed_login_attempts_email ON public.failed_login_attempts USING btree (email)` |
| idx_failed_login_attempts_ip_address | `CREATE INDEX idx_failed_login_attempts_ip_address ON public.failed_login_attempts USING btree (ip_address)` |

---

### institute_branding

Institute-specific branding and customization settings

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| logo_url | character varying(500) | Yes | - | - |
| favicon_url | character varying(500) | Yes | - | - |
| primary_color | character varying(7) | Yes | '#007bff'::character varying | - |
| secondary_color | character varying(7) | Yes | '#6c757d'::character varying | - |
| accent_color | character varying(7) | Yes | '#28a745'::character varying | - |
| font_family | character varying(100) | Yes | 'Inter'::character varying | - |
| custom_css | text | Yes | - | - |
| landing_page_template | character varying(50) | Yes | 'default'::character varying | - |
| landing_page_content | jsonb | Yes | - | - |
| social_links | jsonb | Yes | - | - |
| contact_info | jsonb | Yes | - | - |
| seo_settings | jsonb | Yes | - | - |
| is_active | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80096_1_not_null | CHECK | - | - |
| 2200_80096_2_not_null | CHECK | - | - |
| institute_branding_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| institute_branding_pkey | PRIMARY KEY | id | institute_branding.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institute_branding_institute_id | `CREATE INDEX idx_institute_branding_institute_id ON public.institute_branding USING btree (institute_id)` |
| idx_institute_branding_is_active | `CREATE INDEX idx_institute_branding_is_active ON public.institute_branding USING btree (is_active)` |
| institute_branding_pkey | `CREATE UNIQUE INDEX institute_branding_pkey ON public.institute_branding USING btree (id)` |

---

### institute_domains

Custom domain management with verification and SSL support

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| domain | character varying(255) | No | - | - |
| subdomain | character varying(100) | Yes | - | - |
| domain_type | character varying(20) | Yes | 'custom'::character varying | - |
| is_verified | boolean | Yes | false | - |
| verification_token | character varying(255) | Yes | - | - |
| verification_method | character varying(50) | Yes | 'dns_txt'::character varying | - |
| verification_record | character varying(500) | Yes | - | - |
| verification_attempts | integer | Yes | 0 | - |
| last_verification_check | timestamp without time zone | Yes | - | - |
| ssl_certificate_id | character varying(255) | Yes | - | - |
| ssl_status | character varying(50) | Yes | 'pending'::character varying | - |
| ssl_expires_at | timestamp without time zone | Yes | - | - |
| ssl_auto_renew | boolean | Yes | true | - |
| is_active | boolean | Yes | false | - |
| is_primary | boolean | Yes | false | - |
| redirect_to_primary | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| verified_at | timestamp without time zone | Yes | - | - |
| activated_at | timestamp without time zone | Yes | - | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80037_1_not_null | CHECK | - | - |
| 2200_80037_2_not_null | CHECK | - | - |
| 2200_80037_3_not_null | CHECK | - | - |
| institute_domains_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| institute_domains_pkey | PRIMARY KEY | id | institute_domains.id |
| institute_domains_domain_key | UNIQUE | domain | institute_domains.domain |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institute_domains_domain | `CREATE INDEX idx_institute_domains_domain ON public.institute_domains USING btree (domain)` |
| idx_institute_domains_institute_id | `CREATE INDEX idx_institute_domains_institute_id ON public.institute_domains USING btree (institute_id)` |
| idx_institute_domains_is_active | `CREATE INDEX idx_institute_domains_is_active ON public.institute_domains USING btree (is_active)` |
| idx_institute_domains_is_verified | `CREATE INDEX idx_institute_domains_is_verified ON public.institute_domains USING btree (is_verified)` |
| idx_institute_domains_ssl_status | `CREATE INDEX idx_institute_domains_ssl_status ON public.institute_domains USING btree (ssl_status)` |
| institute_domains_domain_key | `CREATE UNIQUE INDEX institute_domains_domain_key ON public.institute_domains USING btree (domain)` |
| institute_domains_pkey | `CREATE UNIQUE INDEX institute_domains_pkey ON public.institute_domains USING btree (id)` |

---

### institute_settings

Flexible key-value settings storage per institute

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| institute_id | uuid | No | - | - |
| setting_key | character varying(100) | No | - | - |
| setting_value | jsonb | Yes | - | - |
| setting_type | character varying(50) | Yes | 'string'::character varying | - |
| is_public | boolean | Yes | false | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79997_1_not_null | CHECK | - | - |
| 2200_79997_2_not_null | CHECK | - | - |
| 2200_79997_3_not_null | CHECK | - | - |
| institute_settings_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| institute_settings_pkey | PRIMARY KEY | id | institute_settings.id |
| unique_setting_per_institute | UNIQUE | institute_id | institute_settings.institute_id |
| unique_setting_per_institute | UNIQUE | institute_id | institute_settings.setting_key |
| unique_setting_per_institute | UNIQUE | setting_key | institute_settings.institute_id |
| unique_setting_per_institute | UNIQUE | setting_key | institute_settings.setting_key |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institute_settings_institute_id | `CREATE INDEX idx_institute_settings_institute_id ON public.institute_settings USING btree (institute_id)` |
| idx_institute_settings_key | `CREATE INDEX idx_institute_settings_key ON public.institute_settings USING btree (setting_key)` |
| institute_settings_pkey | `CREATE UNIQUE INDEX institute_settings_pkey ON public.institute_settings USING btree (id)` |
| unique_setting_per_institute | `CREATE UNIQUE INDEX unique_setting_per_institute ON public.institute_settings USING btree (institute_id, setting_key)` |

---

### institute_status_history

Institute activation/deactivation tracking

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| changed_by | uuid | No | - | - |
| old_status | boolean | Yes | - | - |
| new_status | boolean | No | - | - |
| reason | text | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80382_1_not_null | CHECK | - | - |
| 2200_80382_2_not_null | CHECK | - | - |
| 2200_80382_3_not_null | CHECK | - | - |
| 2200_80382_5_not_null | CHECK | - | - |
| 2200_80382_6_not_null | CHECK | - | - |
| institute_status_history_changed_by_fkey | FOREIGN KEY | changed_by | users.id |
| institute_status_history_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| institute_status_history_pkey | PRIMARY KEY | id | institute_status_history.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institute_status_history_changed_by | `CREATE INDEX idx_institute_status_history_changed_by ON public.institute_status_history USING btree (changed_by)` |
| idx_institute_status_history_created_at | `CREATE INDEX idx_institute_status_history_created_at ON public.institute_status_history USING btree (created_at)` |
| idx_institute_status_history_institute_id | `CREATE INDEX idx_institute_status_history_institute_id ON public.institute_status_history USING btree (institute_id)` |
| institute_status_history_pkey | `CREATE UNIQUE INDEX institute_status_history_pkey ON public.institute_status_history USING btree (id)` |

---

### institute_usage_stats

Detailed usage statistics per institute

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| stat_date | date | No | - | - |
| active_users | integer | Yes | 0 | - |
| new_users | integer | Yes | 0 | - |
| total_logins | integer | Yes | 0 | - |
| storage_used_mb | integer | Yes | 0 | - |
| api_requests | integer | Yes | 0 | - |
| feature_usage | jsonb | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80466_1_not_null | CHECK | - | - |
| 2200_80466_2_not_null | CHECK | - | - |
| 2200_80466_3_not_null | CHECK | - | - |
| institute_usage_stats_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| institute_usage_stats_pkey | PRIMARY KEY | id | institute_usage_stats.id |
| institute_usage_stats_institute_id_stat_date_key | UNIQUE | stat_date | institute_usage_stats.stat_date |
| institute_usage_stats_institute_id_stat_date_key | UNIQUE | institute_id | institute_usage_stats.institute_id |
| institute_usage_stats_institute_id_stat_date_key | UNIQUE | stat_date | institute_usage_stats.institute_id |
| institute_usage_stats_institute_id_stat_date_key | UNIQUE | institute_id | institute_usage_stats.stat_date |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institute_usage_stats_institute_id | `CREATE INDEX idx_institute_usage_stats_institute_id ON public.institute_usage_stats USING btree (institute_id)` |
| idx_institute_usage_stats_stat_date | `CREATE INDEX idx_institute_usage_stats_stat_date ON public.institute_usage_stats USING btree (stat_date)` |
| institute_usage_stats_institute_id_stat_date_key | `CREATE UNIQUE INDEX institute_usage_stats_institute_id_stat_date_key ON public.institute_usage_stats USING btree (institute_id, stat_date)` |
| institute_usage_stats_pkey | `CREATE UNIQUE INDEX institute_usage_stats_pkey ON public.institute_usage_stats USING btree (id)` |

---

### institutes

Core tenant table storing institute information and subscription details

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| name | character varying(255) | No | - | - |
| slug | character varying(100) | No | - | - |
| email | character varying(255) | No | - | - |
| phone | character varying(20) | Yes | - | - |
| address | text | Yes | - | - |
| website | character varying(255) | Yes | - | - |
| logo_url | character varying(500) | Yes | - | - |
| primary_color | character varying(7) | Yes | '#1E40AF'::character varying | - |
| secondary_color | character varying(7) | Yes | '#3B82F6'::character varying | - |
| subscription_plan | character varying(50) | Yes | 'basic'::character varying | - |
| subscription_status | character varying(20) | Yes | 'active'::character varying | - |
| subscription_expires_at | timestamp without time zone | Yes | - | - |
| is_active | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| deleted_at | timestamp without time zone | Yes | - | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79865_1_not_null | CHECK | - | - |
| 2200_79865_2_not_null | CHECK | - | - |
| 2200_79865_3_not_null | CHECK | - | - |
| 2200_79865_4_not_null | CHECK | - | - |
| institutes_pkey | PRIMARY KEY | id | institutes.id |
| institutes_email_key | UNIQUE | email | institutes.email |
| institutes_slug_key | UNIQUE | slug | institutes.slug |

#### Indexes

| Name | Definition |
|------|------------|
| idx_institutes_email | `CREATE INDEX idx_institutes_email ON public.institutes USING btree (email)` |
| idx_institutes_slug | `CREATE INDEX idx_institutes_slug ON public.institutes USING btree (slug)` |
| idx_institutes_subscription_status | `CREATE INDEX idx_institutes_subscription_status ON public.institutes USING btree (subscription_status)` |
| institutes_email_key | `CREATE UNIQUE INDEX institutes_email_key ON public.institutes USING btree (email)` |
| institutes_pkey | `CREATE UNIQUE INDEX institutes_pkey ON public.institutes USING btree (id)` |
| institutes_slug_key | `CREATE UNIQUE INDEX institutes_slug_key ON public.institutes USING btree (slug)` |

---

### maintenance_windows

Scheduled maintenance tracking

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| title | character varying(255) | No | - | - |
| description | text | Yes | - | - |
| maintenance_type | character varying(50) | No | - | - |
| start_time | timestamp without time zone | No | - | - |
| end_time | timestamp without time zone | No | - | - |
| affected_services | jsonb | Yes | - | - |
| status | character varying(50) | Yes | 'scheduled'::character varying | - |
| created_by | uuid | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80450_1_not_null | CHECK | - | - |
| 2200_80450_2_not_null | CHECK | - | - |
| 2200_80450_4_not_null | CHECK | - | - |
| 2200_80450_5_not_null | CHECK | - | - |
| 2200_80450_6_not_null | CHECK | - | - |
| 2200_80450_9_not_null | CHECK | - | - |
| maintenance_windows_created_by_fkey | FOREIGN KEY | created_by | users.id |
| maintenance_windows_pkey | PRIMARY KEY | id | maintenance_windows.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_maintenance_windows_end_time | `CREATE INDEX idx_maintenance_windows_end_time ON public.maintenance_windows USING btree (end_time)` |
| idx_maintenance_windows_start_time | `CREATE INDEX idx_maintenance_windows_start_time ON public.maintenance_windows USING btree (start_time)` |
| idx_maintenance_windows_status | `CREATE INDEX idx_maintenance_windows_status ON public.maintenance_windows USING btree (status)` |
| maintenance_windows_pkey | `CREATE UNIQUE INDEX maintenance_windows_pkey ON public.maintenance_windows USING btree (id)` |

---

### password_history

No description available

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| user_id | uuid | No | - | - |
| password_hash | character varying(255) | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80309_1_not_null | CHECK | - | - |
| 2200_80309_2_not_null | CHECK | - | - |
| 2200_80309_3_not_null | CHECK | - | - |
| password_history_user_id_fkey | FOREIGN KEY | user_id | users.id |
| password_history_pkey | PRIMARY KEY | id | password_history.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_password_history_created_at | `CREATE INDEX idx_password_history_created_at ON public.password_history USING btree (created_at)` |
| idx_password_history_user_id | `CREATE INDEX idx_password_history_user_id ON public.password_history USING btree (user_id)` |
| password_history_pkey | `CREATE UNIQUE INDEX password_history_pkey ON public.password_history USING btree (id)` |

---

### platform_analytics_cache

Cached analytics data for performance optimization

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| metric_type | character varying(100) | No | - | - |
| metric_date | date | No | - | - |
| metric_data | jsonb | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80401_1_not_null | CHECK | - | - |
| 2200_80401_2_not_null | CHECK | - | - |
| 2200_80401_3_not_null | CHECK | - | - |
| 2200_80401_4_not_null | CHECK | - | - |
| platform_analytics_cache_pkey | PRIMARY KEY | id | platform_analytics_cache.id |
| platform_analytics_cache_metric_type_metric_date_key | UNIQUE | metric_type | platform_analytics_cache.metric_date |
| platform_analytics_cache_metric_type_metric_date_key | UNIQUE | metric_type | platform_analytics_cache.metric_type |
| platform_analytics_cache_metric_type_metric_date_key | UNIQUE | metric_date | platform_analytics_cache.metric_type |
| platform_analytics_cache_metric_type_metric_date_key | UNIQUE | metric_date | platform_analytics_cache.metric_date |

#### Indexes

| Name | Definition |
|------|------------|
| idx_platform_analytics_cache_metric_date | `CREATE INDEX idx_platform_analytics_cache_metric_date ON public.platform_analytics_cache USING btree (metric_date)` |
| idx_platform_analytics_cache_metric_type | `CREATE INDEX idx_platform_analytics_cache_metric_type ON public.platform_analytics_cache USING btree (metric_type)` |
| platform_analytics_cache_metric_type_metric_date_key | `CREATE UNIQUE INDEX platform_analytics_cache_metric_type_metric_date_key ON public.platform_analytics_cache USING btree (metric_type, metric_date)` |
| platform_analytics_cache_pkey | `CREATE UNIQUE INDEX platform_analytics_cache_pkey ON public.platform_analytics_cache USING btree (id)` |

---

### platform_feature_flags

Feature rollout and A/B testing management

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| feature_name | character varying(100) | No | - | - |
| description | text | Yes | - | - |
| is_enabled | boolean | Yes | false | - |
| rollout_percentage | integer | Yes | 0 | - |
| target_subscription_plans | jsonb | Yes | - | - |
| target_institutes | jsonb | Yes | - | - |
| created_by | uuid | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80487_1_not_null | CHECK | - | - |
| 2200_80487_2_not_null | CHECK | - | - |
| 2200_80487_8_not_null | CHECK | - | - |
| platform_feature_flags_created_by_fkey | FOREIGN KEY | created_by | users.id |
| platform_feature_flags_pkey | PRIMARY KEY | id | platform_feature_flags.id |
| platform_feature_flags_feature_name_key | UNIQUE | feature_name | platform_feature_flags.feature_name |

#### Indexes

| Name | Definition |
|------|------------|
| idx_platform_feature_flags_feature_name | `CREATE INDEX idx_platform_feature_flags_feature_name ON public.platform_feature_flags USING btree (feature_name)` |
| idx_platform_feature_flags_is_enabled | `CREATE INDEX idx_platform_feature_flags_is_enabled ON public.platform_feature_flags USING btree (is_enabled)` |
| platform_feature_flags_feature_name_key | `CREATE UNIQUE INDEX platform_feature_flags_feature_name_key ON public.platform_feature_flags USING btree (feature_name)` |
| platform_feature_flags_pkey | `CREATE UNIQUE INDEX platform_feature_flags_pkey ON public.platform_feature_flags USING btree (id)` |

---

### platform_notifications

System-wide notification management

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| notification_type | character varying(100) | No | - | - |
| title | character varying(255) | No | - | - |
| message | text | No | - | - |
| severity | character varying(20) | Yes | 'info'::character varying | - |
| target_audience | character varying(50) | Yes | 'super_admin'::character varying | - |
| target_institute_id | uuid | Yes | - | - |
| is_read | boolean | Yes | false | - |
| is_dismissed | boolean | Yes | false | - |
| expires_at | timestamp without time zone | Yes | - | - |
| created_by | uuid | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80427_1_not_null | CHECK | - | - |
| 2200_80427_2_not_null | CHECK | - | - |
| 2200_80427_3_not_null | CHECK | - | - |
| 2200_80427_4_not_null | CHECK | - | - |
| platform_notifications_created_by_fkey | FOREIGN KEY | created_by | users.id |
| platform_notifications_target_institute_id_fkey | FOREIGN KEY | target_institute_id | institutes.id |
| platform_notifications_pkey | PRIMARY KEY | id | platform_notifications.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_platform_notifications_created_at | `CREATE INDEX idx_platform_notifications_created_at ON public.platform_notifications USING btree (created_at)` |
| idx_platform_notifications_is_read | `CREATE INDEX idx_platform_notifications_is_read ON public.platform_notifications USING btree (is_read)` |
| idx_platform_notifications_target_audience | `CREATE INDEX idx_platform_notifications_target_audience ON public.platform_notifications USING btree (target_audience)` |
| idx_platform_notifications_target_institute_id | `CREATE INDEX idx_platform_notifications_target_institute_id ON public.platform_notifications USING btree (target_institute_id)` |
| platform_notifications_pkey | `CREATE UNIQUE INDEX platform_notifications_pkey ON public.platform_notifications USING btree (id)` |

---

### rate_limit_violations

No description available

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| ip_address | inet | No | - | - |
| endpoint | character varying(255) | No | - | - |
| violation_count | integer | Yes | 1 | - |
| window_start | timestamp without time zone | No | - | - |
| window_end | timestamp without time zone | No | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80269_1_not_null | CHECK | - | - |
| 2200_80269_2_not_null | CHECK | - | - |
| 2200_80269_3_not_null | CHECK | - | - |
| 2200_80269_5_not_null | CHECK | - | - |
| 2200_80269_6_not_null | CHECK | - | - |
| rate_limit_violations_pkey | PRIMARY KEY | id | rate_limit_violations.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_rate_limit_violations_endpoint | `CREATE INDEX idx_rate_limit_violations_endpoint ON public.rate_limit_violations USING btree (endpoint)` |
| idx_rate_limit_violations_ip_address | `CREATE INDEX idx_rate_limit_violations_ip_address ON public.rate_limit_violations USING btree (ip_address)` |
| idx_rate_limit_violations_window_start | `CREATE INDEX idx_rate_limit_violations_window_start ON public.rate_limit_violations USING btree (window_start)` |
| rate_limit_violations_pkey | `CREATE UNIQUE INDEX rate_limit_violations_pkey ON public.rate_limit_violations USING btree (id)` |

---

### security_audit_log

Comprehensive security event logging and monitoring

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| event_type | character varying(100) | No | - | - |
| severity | character varying(20) | No | - | - |
| ip_address | inet | No | - | - |
| user_agent | text | Yes | - | - |
| user_id | uuid | Yes | - | - |
| endpoint | character varying(255) | Yes | - | - |
| details | jsonb | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80228_1_not_null | CHECK | - | - |
| 2200_80228_2_not_null | CHECK | - | - |
| 2200_80228_3_not_null | CHECK | - | - |
| 2200_80228_4_not_null | CHECK | - | - |
| security_audit_log_user_id_fkey | FOREIGN KEY | user_id | users.id |
| security_audit_log_pkey | PRIMARY KEY | id | security_audit_log.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_security_audit_log_created_at | `CREATE INDEX idx_security_audit_log_created_at ON public.security_audit_log USING btree (created_at)` |
| idx_security_audit_log_event_type | `CREATE INDEX idx_security_audit_log_event_type ON public.security_audit_log USING btree (event_type)` |
| idx_security_audit_log_ip_address | `CREATE INDEX idx_security_audit_log_ip_address ON public.security_audit_log USING btree (ip_address)` |
| idx_security_audit_log_severity | `CREATE INDEX idx_security_audit_log_severity ON public.security_audit_log USING btree (severity)` |
| idx_security_audit_log_user_id | `CREATE INDEX idx_security_audit_log_user_id ON public.security_audit_log USING btree (user_id)` |
| security_audit_log_pkey | `CREATE UNIQUE INDEX security_audit_log_pkey ON public.security_audit_log USING btree (id)` |

---

### security_config

No description available

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| config_key | character varying(100) | No | - | - |
| config_value | jsonb | No | - | - |
| description | text | Yes | - | - |
| is_active | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80279_1_not_null | CHECK | - | - |
| 2200_80279_2_not_null | CHECK | - | - |
| 2200_80279_3_not_null | CHECK | - | - |
| security_config_pkey | PRIMARY KEY | id | security_config.id |
| security_config_config_key_key | UNIQUE | config_key | security_config.config_key |

#### Indexes

| Name | Definition |
|------|------------|
| idx_security_config_config_key | `CREATE INDEX idx_security_config_config_key ON public.security_config USING btree (config_key)` |
| idx_security_config_is_active | `CREATE INDEX idx_security_config_is_active ON public.security_config USING btree (is_active)` |
| security_config_config_key_key | `CREATE UNIQUE INDEX security_config_config_key_key ON public.security_config USING btree (config_key)` |
| security_config_pkey | `CREATE UNIQUE INDEX security_config_pkey ON public.security_config USING btree (id)` |

---

### session_security

No description available

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| session_id | character varying(255) | No | - | - |
| user_id | uuid | No | - | - |
| ip_address | inet | No | - | - |
| user_agent | text | Yes | - | - |
| location_country | character varying(2) | Yes | - | - |
| location_city | character varying(100) | Yes | - | - |
| is_suspicious | boolean | Yes | false | - |
| risk_score | integer | Yes | 0 | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| last_activity | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80292_1_not_null | CHECK | - | - |
| 2200_80292_2_not_null | CHECK | - | - |
| 2200_80292_3_not_null | CHECK | - | - |
| 2200_80292_4_not_null | CHECK | - | - |
| session_security_user_id_fkey | FOREIGN KEY | user_id | users.id |
| session_security_pkey | PRIMARY KEY | id | session_security.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_session_security_ip_address | `CREATE INDEX idx_session_security_ip_address ON public.session_security USING btree (ip_address)` |
| idx_session_security_is_suspicious | `CREATE INDEX idx_session_security_is_suspicious ON public.session_security USING btree (is_suspicious)` |
| idx_session_security_last_activity | `CREATE INDEX idx_session_security_last_activity ON public.session_security USING btree (last_activity)` |
| idx_session_security_session_id | `CREATE INDEX idx_session_security_session_id ON public.session_security USING btree (session_id)` |
| idx_session_security_user_id | `CREATE INDEX idx_session_security_user_id ON public.session_security USING btree (user_id)` |
| session_security_pkey | `CREATE UNIQUE INDEX session_security_pkey ON public.session_security USING btree (id)` |

---

### ssl_certificates

SSL certificate management and auto-renewal tracking

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| domain_id | uuid | No | - | - |
| certificate_authority | character varying(100) | Yes | 'letsencrypt'::character varying | - |
| certificate_data | text | Yes | - | - |
| private_key_data | text | Yes | - | - |
| certificate_chain | text | Yes | - | - |
| issued_at | timestamp without time zone | Yes | - | - |
| expires_at | timestamp without time zone | Yes | - | - |
| auto_renew | boolean | Yes | true | - |
| renewal_attempts | integer | Yes | 0 | - |
| last_renewal_attempt | timestamp without time zone | Yes | - | - |
| status | character varying(50) | Yes | 'pending'::character varying | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80077_1_not_null | CHECK | - | - |
| 2200_80077_2_not_null | CHECK | - | - |
| ssl_certificates_domain_id_fkey | FOREIGN KEY | domain_id | institute_domains.id |
| ssl_certificates_pkey | PRIMARY KEY | id | ssl_certificates.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_ssl_certificates_domain_id | `CREATE INDEX idx_ssl_certificates_domain_id ON public.ssl_certificates USING btree (domain_id)` |
| idx_ssl_certificates_expires_at | `CREATE INDEX idx_ssl_certificates_expires_at ON public.ssl_certificates USING btree (expires_at)` |
| idx_ssl_certificates_status | `CREATE INDEX idx_ssl_certificates_status ON public.ssl_certificates USING btree (status)` |
| ssl_certificates_pkey | `CREATE UNIQUE INDEX ssl_certificates_pkey ON public.ssl_certificates USING btree (id)` |

---

### student_profiles

Extended student information and academic tracking

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| user_id | uuid | No | - | - |
| student_id | character varying(50) | Yes | - | - |
| enrollment_date | date | Yes | CURRENT_DATE | - |
| graduation_year | integer | Yes | - | - |
| major | character varying(100) | Yes | - | - |
| gpa | numeric | Yes | - | - |
| academic_status | character varying(20) | Yes | 'active'::character varying | - |
| emergency_contact_name | character varying(100) | Yes | - | - |
| emergency_contact_phone | character varying(20) | Yes | - | - |
| emergency_contact_relationship | character varying(50) | Yes | - | - |
| address | text | Yes | - | - |
| date_of_birth | date | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80131_1_not_null | CHECK | - | - |
| 2200_80131_2_not_null | CHECK | - | - |
| student_profiles_user_id_fkey | FOREIGN KEY | user_id | users.id |
| student_profiles_pkey | PRIMARY KEY | id | student_profiles.id |
| student_profiles_user_id_key | UNIQUE | user_id | student_profiles.user_id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_student_profiles_academic_status | `CREATE INDEX idx_student_profiles_academic_status ON public.student_profiles USING btree (academic_status)` |
| idx_student_profiles_graduation_year | `CREATE INDEX idx_student_profiles_graduation_year ON public.student_profiles USING btree (graduation_year)` |
| idx_student_profiles_student_id | `CREATE INDEX idx_student_profiles_student_id ON public.student_profiles USING btree (student_id)` |
| idx_student_profiles_user_id | `CREATE INDEX idx_student_profiles_user_id ON public.student_profiles USING btree (user_id)` |
| student_profiles_pkey | `CREATE UNIQUE INDEX student_profiles_pkey ON public.student_profiles USING btree (id)` |
| student_profiles_user_id_key | `CREATE UNIQUE INDEX student_profiles_user_id_key ON public.student_profiles USING btree (user_id)` |

---

### subscription_history

Audit trail for subscription plan changes

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| changed_by | uuid | No | - | - |
| old_plan | character varying(50) | Yes | - | - |
| new_plan | character varying(50) | No | - | - |
| old_status | character varying(50) | Yes | - | - |
| new_status | character varying(50) | No | - | - |
| notes | text | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80363_1_not_null | CHECK | - | - |
| 2200_80363_2_not_null | CHECK | - | - |
| 2200_80363_3_not_null | CHECK | - | - |
| 2200_80363_5_not_null | CHECK | - | - |
| 2200_80363_7_not_null | CHECK | - | - |
| subscription_history_changed_by_fkey | FOREIGN KEY | changed_by | users.id |
| subscription_history_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| subscription_history_pkey | PRIMARY KEY | id | subscription_history.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_subscription_history_changed_by | `CREATE INDEX idx_subscription_history_changed_by ON public.subscription_history USING btree (changed_by)` |
| idx_subscription_history_created_at | `CREATE INDEX idx_subscription_history_created_at ON public.subscription_history USING btree (created_at)` |
| idx_subscription_history_institute_id | `CREATE INDEX idx_subscription_history_institute_id ON public.subscription_history USING btree (institute_id)` |
| subscription_history_pkey | `CREATE UNIQUE INDEX subscription_history_pkey ON public.subscription_history USING btree (id)` |

---

### super_admin_activity_log

Super admin action audit trail

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| admin_id | uuid | No | - | - |
| action_type | character varying(100) | No | - | - |
| target_type | character varying(50) | No | - | - |
| target_id | uuid | No | - | - |
| action_details | jsonb | Yes | - | - |
| ip_address | inet | Yes | - | - |
| user_agent | text | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80413_1_not_null | CHECK | - | - |
| 2200_80413_2_not_null | CHECK | - | - |
| 2200_80413_3_not_null | CHECK | - | - |
| 2200_80413_4_not_null | CHECK | - | - |
| 2200_80413_5_not_null | CHECK | - | - |
| super_admin_activity_log_admin_id_fkey | FOREIGN KEY | admin_id | users.id |
| super_admin_activity_log_pkey | PRIMARY KEY | id | super_admin_activity_log.id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_super_admin_activity_log_action_type | `CREATE INDEX idx_super_admin_activity_log_action_type ON public.super_admin_activity_log USING btree (action_type)` |
| idx_super_admin_activity_log_admin_id | `CREATE INDEX idx_super_admin_activity_log_admin_id ON public.super_admin_activity_log USING btree (admin_id)` |
| idx_super_admin_activity_log_created_at | `CREATE INDEX idx_super_admin_activity_log_created_at ON public.super_admin_activity_log USING btree (created_at)` |
| idx_super_admin_activity_log_target_type | `CREATE INDEX idx_super_admin_activity_log_target_type ON public.super_admin_activity_log USING btree (target_type)` |
| super_admin_activity_log_pkey | `CREATE UNIQUE INDEX super_admin_activity_log_pkey ON public.super_admin_activity_log USING btree (id)` |

---

### teacher_invitations

Teacher invitation system with token-based verification

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| institute_id | uuid | No | - | - |
| email | character varying(255) | No | - | - |
| token | character varying(255) | No | - | - |
| invited_by | uuid | No | - | - |
| department | character varying(100) | Yes | - | - |
| message | text | Yes | - | - |
| expires_at | timestamp without time zone | Yes | (CURRENT_TIMESTAMP + '7 days'::interval) | - |
| is_used | boolean | Yes | false | - |
| used_at | timestamp without time zone | Yes | - | - |
| used_by | uuid | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80169_1_not_null | CHECK | - | - |
| 2200_80169_2_not_null | CHECK | - | - |
| 2200_80169_3_not_null | CHECK | - | - |
| 2200_80169_4_not_null | CHECK | - | - |
| 2200_80169_5_not_null | CHECK | - | - |
| teacher_invitations_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| teacher_invitations_invited_by_fkey | FOREIGN KEY | invited_by | users.id |
| teacher_invitations_used_by_fkey | FOREIGN KEY | used_by | users.id |
| teacher_invitations_pkey | PRIMARY KEY | id | teacher_invitations.id |
| teacher_invitations_token_key | UNIQUE | token | teacher_invitations.token |

#### Indexes

| Name | Definition |
|------|------------|
| idx_teacher_invitations_email | `CREATE INDEX idx_teacher_invitations_email ON public.teacher_invitations USING btree (email)` |
| idx_teacher_invitations_expires_at | `CREATE INDEX idx_teacher_invitations_expires_at ON public.teacher_invitations USING btree (expires_at)` |
| idx_teacher_invitations_institute_id | `CREATE INDEX idx_teacher_invitations_institute_id ON public.teacher_invitations USING btree (institute_id)` |
| idx_teacher_invitations_token | `CREATE INDEX idx_teacher_invitations_token ON public.teacher_invitations USING btree (token)` |
| teacher_invitations_pkey | `CREATE UNIQUE INDEX teacher_invitations_pkey ON public.teacher_invitations USING btree (id)` |
| teacher_invitations_token_key | `CREATE UNIQUE INDEX teacher_invitations_token_key ON public.teacher_invitations USING btree (token)` |

---

### teacher_profiles

Teacher-specific information and employment details

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| user_id | uuid | No | - | - |
| employee_id | character varying(50) | Yes | - | - |
| department | character varying(100) | Yes | - | - |
| specialization | character varying(255) | Yes | - | - |
| hire_date | date | Yes | CURRENT_DATE | - |
| office_location | character varying(100) | Yes | - | - |
| office_hours | character varying(255) | Yes | - | - |
| bio | text | Yes | - | - |
| qualifications | text | Yes | - | - |
| salary | numeric | Yes | - | - |
| employment_status | character varying(20) | Yes | 'active'::character varying | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80150_1_not_null | CHECK | - | - |
| 2200_80150_2_not_null | CHECK | - | - |
| teacher_profiles_user_id_fkey | FOREIGN KEY | user_id | users.id |
| teacher_profiles_pkey | PRIMARY KEY | id | teacher_profiles.id |
| teacher_profiles_user_id_key | UNIQUE | user_id | teacher_profiles.user_id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_teacher_profiles_department | `CREATE INDEX idx_teacher_profiles_department ON public.teacher_profiles USING btree (department)` |
| idx_teacher_profiles_employee_id | `CREATE INDEX idx_teacher_profiles_employee_id ON public.teacher_profiles USING btree (employee_id)` |
| idx_teacher_profiles_employment_status | `CREATE INDEX idx_teacher_profiles_employment_status ON public.teacher_profiles USING btree (employment_status)` |
| idx_teacher_profiles_user_id | `CREATE INDEX idx_teacher_profiles_user_id ON public.teacher_profiles USING btree (user_id)` |
| teacher_profiles_pkey | `CREATE UNIQUE INDEX teacher_profiles_pkey ON public.teacher_profiles USING btree (id)` |
| teacher_profiles_user_id_key | `CREATE UNIQUE INDEX teacher_profiles_user_id_key ON public.teacher_profiles USING btree (user_id)` |

---

### two_factor_auth

Two-factor authentication settings and backup codes

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | gen_random_uuid() | - |
| user_id | uuid | No | - | - |
| secret_key | character varying(255) | No | - | - |
| backup_codes | jsonb | Yes | - | - |
| is_enabled | boolean | Yes | false | - |
| last_used_at | timestamp without time zone | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_80321_1_not_null | CHECK | - | - |
| 2200_80321_2_not_null | CHECK | - | - |
| 2200_80321_3_not_null | CHECK | - | - |
| two_factor_auth_user_id_fkey | FOREIGN KEY | user_id | users.id |
| two_factor_auth_pkey | PRIMARY KEY | id | two_factor_auth.id |
| two_factor_auth_user_id_key | UNIQUE | user_id | two_factor_auth.user_id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_two_factor_auth_is_enabled | `CREATE INDEX idx_two_factor_auth_is_enabled ON public.two_factor_auth USING btree (is_enabled)` |
| idx_two_factor_auth_user_id | `CREATE INDEX idx_two_factor_auth_user_id ON public.two_factor_auth USING btree (user_id)` |
| two_factor_auth_pkey | `CREATE UNIQUE INDEX two_factor_auth_pkey ON public.two_factor_auth USING btree (id)` |
| two_factor_auth_user_id_key | `CREATE UNIQUE INDEX two_factor_auth_user_id_key ON public.two_factor_auth USING btree (user_id)` |

---

### user_roles

RBAC system with JSON-based permissions for flexible access control

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| user_id | uuid | No | - | - |
| institute_id | uuid | Yes | - | - |
| role_name | character varying(50) | No | - | - |
| permissions | jsonb | Yes | '[]'::jsonb | - |
| is_active | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79926_1_not_null | CHECK | - | - |
| 2200_79926_2_not_null | CHECK | - | - |
| 2200_79926_4_not_null | CHECK | - | - |
| user_roles_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| user_roles_user_id_fkey | FOREIGN KEY | user_id | users.id |
| user_roles_pkey | PRIMARY KEY | id | user_roles.id |
| unique_user_role_per_institute | UNIQUE | institute_id | user_roles.role_name |
| unique_user_role_per_institute | UNIQUE | user_id | user_roles.institute_id |
| unique_user_role_per_institute | UNIQUE | user_id | user_roles.role_name |
| unique_user_role_per_institute | UNIQUE | user_id | user_roles.user_id |
| unique_user_role_per_institute | UNIQUE | institute_id | user_roles.institute_id |
| unique_user_role_per_institute | UNIQUE | role_name | user_roles.institute_id |
| unique_user_role_per_institute | UNIQUE | role_name | user_roles.role_name |
| unique_user_role_per_institute | UNIQUE | role_name | user_roles.user_id |
| unique_user_role_per_institute | UNIQUE | institute_id | user_roles.user_id |

#### Indexes

| Name | Definition |
|------|------------|
| idx_user_roles_institute_id | `CREATE INDEX idx_user_roles_institute_id ON public.user_roles USING btree (institute_id)` |
| idx_user_roles_role_name | `CREATE INDEX idx_user_roles_role_name ON public.user_roles USING btree (role_name)` |
| idx_user_roles_user_id | `CREATE INDEX idx_user_roles_user_id ON public.user_roles USING btree (user_id)` |
| unique_user_role_per_institute | `CREATE UNIQUE INDEX unique_user_role_per_institute ON public.user_roles USING btree (user_id, institute_id, role_name)` |
| user_roles_pkey | `CREATE UNIQUE INDEX user_roles_pkey ON public.user_roles USING btree (id)` |

---

### user_sessions

Session management with security tracking and automatic cleanup

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| user_id | uuid | No | - | - |
| institute_id | uuid | Yes | - | - |
| session_token | character varying(500) | No | - | - |
| refresh_token | character varying(500) | Yes | - | - |
| ip_address | inet | Yes | - | - |
| user_agent | text | Yes | - | - |
| expires_at | timestamp without time zone | No | - | - |
| is_active | boolean | Yes | true | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| last_accessed_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79972_1_not_null | CHECK | - | - |
| 2200_79972_2_not_null | CHECK | - | - |
| 2200_79972_4_not_null | CHECK | - | - |
| 2200_79972_8_not_null | CHECK | - | - |
| user_sessions_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| user_sessions_user_id_fkey | FOREIGN KEY | user_id | users.id |
| user_sessions_pkey | PRIMARY KEY | id | user_sessions.id |
| user_sessions_refresh_token_key | UNIQUE | refresh_token | user_sessions.refresh_token |
| user_sessions_session_token_key | UNIQUE | session_token | user_sessions.session_token |

#### Indexes

| Name | Definition |
|------|------------|
| idx_user_sessions_expires_at | `CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions USING btree (expires_at)` |
| idx_user_sessions_session_token | `CREATE INDEX idx_user_sessions_session_token ON public.user_sessions USING btree (session_token)` |
| idx_user_sessions_user_id | `CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id)` |
| user_sessions_pkey | `CREATE UNIQUE INDEX user_sessions_pkey ON public.user_sessions USING btree (id)` |
| user_sessions_refresh_token_key | `CREATE UNIQUE INDEX user_sessions_refresh_token_key ON public.user_sessions USING btree (refresh_token)` |
| user_sessions_session_token_key | `CREATE UNIQUE INDEX user_sessions_session_token_key ON public.user_sessions USING btree (session_token)` |

---

### users

All user types (super_admin, institute_admin, teacher, student) with multi-tenant isolation

#### Columns

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | uuid | No | uuid_generate_v4() | - |
| institute_id | uuid | Yes | - | - |
| email | character varying(255) | No | - | - |
| password_hash | character varying(255) | No | - | - |
| first_name | character varying(100) | No | - | - |
| last_name | character varying(100) | No | - | - |
| phone | character varying(20) | Yes | - | - |
| avatar_url | character varying(500) | Yes | - | - |
| role | character varying(50) | No | - | - |
| is_active | boolean | Yes | true | - |
| is_email_verified | boolean | Yes | false | - |
| last_login_at | timestamp without time zone | Yes | - | - |
| created_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |
| updated_at | timestamp without time zone | Yes | CURRENT_TIMESTAMP | - |

#### Constraints

| Name | Type | Columns | References |
|------|------|---------|------------|
| 2200_79907_1_not_null | CHECK | - | - |
| 2200_79907_3_not_null | CHECK | - | - |
| 2200_79907_4_not_null | CHECK | - | - |
| 2200_79907_5_not_null | CHECK | - | - |
| 2200_79907_6_not_null | CHECK | - | - |
| 2200_79907_9_not_null | CHECK | - | - |
| users_institute_id_fkey | FOREIGN KEY | institute_id | institutes.id |
| users_pkey | PRIMARY KEY | id | users.id |
| unique_email_per_institute | UNIQUE | institute_id | users.institute_id |
| unique_email_per_institute | UNIQUE | email | users.email |
| unique_email_per_institute | UNIQUE | email | users.institute_id |
| unique_email_per_institute | UNIQUE | institute_id | users.email |

#### Indexes

| Name | Definition |
|------|------------|
| idx_users_email | `CREATE INDEX idx_users_email ON public.users USING btree (email)` |
| idx_users_email_institute | `CREATE INDEX idx_users_email_institute ON public.users USING btree (email, institute_id)` |
| idx_users_institute_id | `CREATE INDEX idx_users_institute_id ON public.users USING btree (institute_id)` |
| idx_users_role | `CREATE INDEX idx_users_role ON public.users USING btree (role)` |
| unique_email_per_institute | `CREATE UNIQUE INDEX unique_email_per_institute ON public.users USING btree (email, institute_id)` |
| users_pkey | `CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id)` |

---

## Table Relationships

### Foreign Key Relationships

- **users -> course_enrollments**: id -> student_id
- **institutes -> custom_domains**: id -> institute_id
- **institute_domains -> domain_verification_logs**: id -> domain_id
- **institutes -> email_verifications**: id -> institute_id
- **users -> email_verifications**: id -> user_id
- **institutes -> failed_login_attempts**: id -> institute_id
- **institutes -> institute_branding**: id -> institute_id
- **institutes -> institute_domains**: id -> institute_id
- **institutes -> institute_settings**: id -> institute_id
- **users -> institute_status_history**: id -> changed_by
- **institutes -> institute_status_history**: id -> institute_id
- **institutes -> institute_usage_stats**: id -> institute_id
- **users -> maintenance_windows**: id -> created_by
- **users -> password_history**: id -> user_id
- **users -> platform_feature_flags**: id -> created_by
- **users -> platform_notifications**: id -> created_by
- **institutes -> platform_notifications**: id -> target_institute_id
- **users -> security_audit_log**: id -> user_id
- **users -> session_security**: id -> user_id
- **institute_domains -> ssl_certificates**: id -> domain_id
- **users -> student_profiles**: id -> user_id
- **users -> subscription_history**: id -> changed_by
- **institutes -> subscription_history**: id -> institute_id
- **users -> super_admin_activity_log**: id -> admin_id
- **institutes -> teacher_invitations**: id -> institute_id
- **users -> teacher_invitations**: id -> invited_by, id -> used_by
- **users -> teacher_profiles**: id -> user_id
- **users -> two_factor_auth**: id -> user_id
- **institutes -> user_roles**: id -> institute_id
- **users -> user_roles**: id -> user_id
- **institutes -> user_sessions**: id -> institute_id
- **users -> user_sessions**: id -> user_id
- **institutes -> users**: id -> institute_id

## Multi-Tenancy Implementation

### Tenant Isolation Strategy

1. **Institute-based isolation**: All tenant data is linked via `institute_id` foreign keys
2. **Application-level filtering**: All queries include institute context
3. **RBAC enforcement**: Role-based permissions prevent cross-tenant access
4. **Session management**: User sessions are scoped to specific institutes

### Tables with Multi-Tenant Data

- **custom_domains**: Isolated by institute_id
- **email_verifications**: Isolated by institute_id
- **failed_login_attempts**: Isolated by institute_id
- **institute_branding**: Isolated by institute_id
- **institute_domains**: Isolated by institute_id
- **institute_settings**: Isolated by institute_id
- **institute_status_history**: Isolated by institute_id
- **institute_usage_stats**: Isolated by institute_id
- **subscription_history**: Isolated by institute_id
- **teacher_invitations**: Isolated by institute_id
- **user_roles**: Isolated by institute_id
- **user_sessions**: Isolated by institute_id
- **users**: Isolated by institute_id

## Security Features

### Audit Logging
- **security_audit_log**: Comprehensive security event tracking
- **super_admin_activity_log**: Super admin action audit trail
- **failed_login_attempts**: Brute force attack detection

### Access Control
- **user_roles**: Flexible RBAC with JSON permissions
- **blocked_ips**: Automated IP blocking for threats
- **two_factor_auth**: Enhanced authentication security

### Session Security
- **user_sessions**: Secure session management with expiration
- **email_verifications**: Token-based email verification
- IP address and user agent tracking for all sessions

