import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Toaster } from 'react-hot-toast'
import { AccessibilityProvider } from '@/components/accessibility/AccessibilityProvider'
import ErrorBoundary from '@/components/ErrorBoundary'
import '../styles/globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'LMS SAAS - Learning Management System',
    template: '%s | LMS SAAS'
  },
  description: 'Multi-tenant Learning Management System for educational institutions',
  keywords: ['LMS', 'Learning Management System', 'Education', 'SAAS', 'Multi-tenant'],
  authors: [{ name: 'LMS SAAS Team' }],
  creator: 'LMS SAAS Team',
  publisher: 'LMS SAAS',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    title: 'LMS SAAS - Learning Management System',
    description: 'Multi-tenant Learning Management System for educational institutions',
    siteName: 'LMS SAAS',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'LMS SAAS - Learning Management System',
    description: 'Multi-tenant Learning Management System for educational institutions',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <AccessibilityProvider>
            <div id="root" className="min-h-screen bg-background">
              <main id="main-content">
                {children}
              </main>
            </div>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: 'hsl(var(--card))',
                  color: 'hsl(var(--card-foreground))',
                  border: '1px solid hsl(var(--border))',
                },
                success: {
                  iconTheme: {
                    primary: 'hsl(var(--primary))',
                    secondary: 'hsl(var(--primary-foreground))',
                  },
                },
                error: {
                  iconTheme: {
                    primary: 'hsl(var(--destructive))',
                    secondary: 'hsl(var(--destructive-foreground))',
                  },
                },
              }}
            />
          </AccessibilityProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
