'use client'

import React from 'react'
import { X, User, Mail, Phone, Calendar, Hash } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { FormField, SelectField } from '@/components/forms/FormField'

const studentSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Valid email required').required('Email is required'),
  phone: Yup.string().matches(/^[\+]?[1-9][\d]{0,15}$/, 'Valid phone number required'),
  studentId: Yup.string().required('Student ID is required'),
  dateOfBirth: Yup.date().max(new Date(), 'Date of birth cannot be in the future'),
  address: Yup.string().max(200, 'Address must be less than 200 characters'),
  emergencyContact: Yup.string().max(100, 'Emergency contact must be less than 100 characters'),
  emergencyPhone: Yup.string().matches(/^[\+]?[1-9][\d]{0,15}$/, 'Valid emergency phone required'),
})

interface StudentFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  studentId: string
  dateOfBirth: string
  address: string
  emergencyContact: string
  emergencyPhone: string
  sendInvitation: boolean
}

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  studentId: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  enrollmentDate: string
  lastLogin?: string
  coursesEnrolled: number
  completedCourses: number
  avatar?: string
}

interface StudentFormModalProps {
  isOpen: boolean
  onClose: () => void
  student?: Student | null
  onSave: (studentData: StudentFormData) => void
}

export default function StudentFormModal({ isOpen, onClose, student, onSave }: StudentFormModalProps) {
  if (!isOpen) return null

  const initialValues: StudentFormData = {
    firstName: student?.firstName || '',
    lastName: student?.lastName || '',
    email: student?.email || '',
    phone: student?.phone || '',
    studentId: student?.studentId || '',
    dateOfBirth: '',
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    sendInvitation: !student, // Send invitation for new students by default
  }

  const handleSubmit = (values: StudentFormData) => {
    onSave(values)
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                {student ? 'Edit Student' : 'Add New Student'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={studentSchema}
              onSubmit={handleSubmit}
            >
              {({ values, setFieldValue, isSubmitting }) => (
                <Form className="space-y-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      Personal Information
                    </h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        name="firstName"
                        label="First Name"
                        placeholder="John"
                        required
                      />
                      
                      <FormField
                        name="lastName"
                        label="Last Name"
                        placeholder="Doe"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        name="email"
                        label="Email Address"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                      />
                      
                      <FormField
                        name="phone"
                        label="Phone Number"
                        placeholder="******-0123"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        name="studentId"
                        label="Student ID"
                        placeholder="STU001"
                        required
                        help="Unique identifier for the student"
                      />
                      
                      <FormField
                        name="dateOfBirth"
                        label="Date of Birth"
                        type="date"
                      />
                    </div>

                    <FormField
                      name="address"
                      label="Address"
                      as="textarea"
                      rows={2}
                      placeholder="Student's address..."
                    />
                  </div>

                  {/* Emergency Contact */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      Emergency Contact
                    </h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        name="emergencyContact"
                        label="Emergency Contact Name"
                        placeholder="Jane Doe (Mother)"
                      />
                      
                      <FormField
                        name="emergencyPhone"
                        label="Emergency Phone"
                        placeholder="******-0124"
                      />
                    </div>
                  </div>

                  {/* Options */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 flex items-center">
                      <Mail className="w-4 h-4 mr-2" />
                      Enrollment Options
                    </h4>
                    
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={values.sendInvitation}
                          onChange={(e) => setFieldValue('sendInvitation', e.target.checked)}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Send invitation email to student
                        </span>
                      </label>
                    </div>

                    {values.sendInvitation && (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-700">
                          An invitation email will be sent to the student with login instructions 
                          and a temporary password. The student will be required to change their 
                          password on first login.
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onClose}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      loading={isSubmitting}
                    >
                      {student ? 'Update Student' : 'Add Student'}
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  )
}
