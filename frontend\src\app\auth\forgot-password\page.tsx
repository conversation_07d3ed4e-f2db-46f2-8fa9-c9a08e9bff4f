'use client'

import React from 'react'
import Link from 'next/link'

import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import toast from 'react-hot-toast'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { FormField } from '@/components/forms/FormField'
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react'

const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
})

interface ForgotPasswordFormValues {
  email: string
}

export default function ForgotPasswordPage() {
  const [isEmailSent, setIsEmailSent] = React.useState(false)
  const [emailAddress, setEmailAddress] = React.useState('')

  const handleSubmit = async (values: ForgotPasswordFormValues, { setSubmitting }: any) => {
    try {
      // TODO: Replace with actual API call
      console.log('Forgot password request:', values)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock successful request
      setEmailAddress(values.email)
      setIsEmailSent(true)
      toast.success('Password reset instructions sent to your email!')
      
    } catch (error) {
      console.error('Forgot password error:', error)
      toast.error('Failed to send reset instructions. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const handleResendEmail = async () => {
    try {
      // TODO: Replace with actual API call
      console.log('Resending email to:', emailAddress)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      toast.success('Reset instructions sent again!')
    } catch (error) {
      console.error('Resend email error:', error)
      toast.error('Failed to resend email. Please try again.')
    }
  }

  if (isEmailSent) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
            <Link 
              href="/auth/login" 
              className="flex items-center space-x-1 hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to login</span>
            </Link>
          </div>
          
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 text-center">Check your email</h1>
          <p className="text-gray-600 text-center">
            We've sent password reset instructions to
          </p>
          <p className="text-blue-600 font-medium text-center">{emailAddress}</p>
        </div>

        {/* Instructions */}
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-900 mb-2">What's next?</h3>
            <ol className="space-y-1 text-sm text-blue-700 list-decimal list-inside">
              <li>Check your email inbox (and spam folder)</li>
              <li>Click the reset link in the email</li>
              <li>Create a new password</li>
              <li>Sign in with your new password</li>
            </ol>
          </div>

          <div className="text-center space-y-4">
            <p className="text-sm text-gray-600">
              Didn't receive the email?
            </p>
            
            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={handleResendEmail}
                className="w-full"
              >
                Resend email
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setIsEmailSent(false)}
                className="w-full"
              >
                Try a different email
              </Button>
            </div>
          </div>
        </div>

        {/* Support */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Still having trouble?{' '}
            <Link 
              href="/support"
              className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
            >
              Contact support
            </Link>
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
          <Link 
            href="/auth/login" 
            className="flex items-center space-x-1 hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to login</span>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900">Forgot your password?</h1>
        <p className="text-gray-600">
          No worries! Enter your email address and we'll send you reset instructions.
        </p>
      </div>

      {/* Forgot Password Form */}
      <Formik
        initialValues={{
          email: '',
        }}
        validationSchema={forgotPasswordSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, isValid }) => (
          <Form className="space-y-4">
            <FormField
              name="email"
              label="Email address"
              type="email"
              placeholder="Enter your email address"
              autoComplete="email"
              description="We'll send reset instructions to this email"
              required
            />

            <Button
              type="submit"
              className="w-full"
              loading={isSubmitting}
              disabled={!isValid}
            >
              <Mail className="mr-2 h-4 w-4" />
              Send reset instructions
            </Button>
          </Form>
        )}
      </Formik>

      {/* Alternative options */}
      <div className="space-y-4">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or
            </span>
          </div>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Remember your password?{' '}
            <Link 
              href="/auth/login"
              className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
            >
              Sign in
            </Link>
          </p>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{' '}
            <Link 
              href="/auth/register"
              className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
            >
              Sign up for free
            </Link>
          </p>
        </div>
      </div>

      {/* Help section */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Need help?</h3>
        <div className="space-y-1 text-sm text-gray-600">
          <p>• Make sure you're using the email address associated with your account</p>
          <p>• Check your spam or junk folder for the reset email</p>
          <p>• Reset links expire after 24 hours for security</p>
          <p>• Contact support if you continue having issues</p>
        </div>
      </div>
    </div>
  )
}
