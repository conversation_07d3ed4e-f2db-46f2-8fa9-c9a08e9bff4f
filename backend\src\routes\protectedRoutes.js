const express = require('express');
const { authenticateToken } = require('../middleware/authMiddleware');
const { 
  requirePermission,
  requireAnyPermission,
  requirePlatformAccess,
  requireInstituteManagement,
  requireUserManagement,
  requireCourseManagement,
  requireOwnership,
  attachUserPermissions
} = require('../middleware/rbacMiddleware');
const { requireTenant } = require('../middleware/tenantMiddleware');
const { PERMISSIONS } = require('../services/rbacService');
const { createTenantService, SuperAdminService } = require('../services/tenantService');
const { responseUtils } = require('../utils/auth');

const router = express.Router();

// Apply authentication to all protected routes
router.use(authenticateToken);

// Attach user permissions to all requests
router.use(attachUserPermissions);

/**
 * @route GET /api/protected/permissions
 * @desc Get current user's permissions
 * @access Private
 */
router.get('/permissions', async (req, res) => {
  try {
    res.json({
      success: true,
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        instituteId: req.user.institute_id
      },
      permissions: req.userPermissions || [],
      permissionCount: req.userPermissions?.length || 0
    });
  } catch (error) {
    res.status(500).json(
      responseUtils.createErrorResponse('Failed to get permissions', 'PERMISSIONS_ERROR')
    );
  }
});

/**
 * @route GET /api/protected/platform/stats
 * @desc Get platform-wide statistics (Super Admin only)
 * @access Super Admin
 */
router.get('/platform/stats',
  requirePlatformAccess,
  async (req, res) => {
    try {
      const superAdminService = new SuperAdminService();
      
      const [institutes, users] = await Promise.all([
        superAdminService.getAllInstitutes({ limit: 1000 }),
        superAdminService.getAllUsers({ limit: 1000 })
      ]);

      const stats = {
        totalInstitutes: institutes.rows.length,
        totalUsers: users.rows.length,
        usersByRole: users.rows.reduce((acc, user) => {
          acc[user.role] = (acc[user.role] || 0) + 1;
          return acc;
        }, {}),
        activeInstitutes: institutes.rows.filter(i => i.is_active).length
      };

      res.json({
        success: true,
        stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Platform stats error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get platform stats', 'PLATFORM_STATS_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/protected/institute/dashboard
 * @desc Get institute dashboard data
 * @access Institute Admin, Super Admin
 */
router.get('/institute/dashboard',
  requireTenant,
  requireInstituteManagement,
  async (req, res) => {
    try {
      const tenantService = createTenantService(req);
      
      const [institute, users] = await Promise.all([
        tenantService.getInstituteInfo(),
        tenantService.getUsers({ limit: 1000 })
      ]);

      const dashboard = {
        institute: {
          id: institute.id,
          name: institute.name,
          email: institute.email,
          subscriptionPlan: institute.subscription_plan
        },
        userStats: {
          totalUsers: users.rows.length,
          usersByRole: users.rows.reduce((acc, user) => {
            acc[user.role] = (acc[user.role] || 0) + 1;
            return acc;
          }, {}),
          activeUsers: users.rows.filter(u => u.is_active).length
        }
      };

      res.json({
        success: true,
        dashboard,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Institute dashboard error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get institute dashboard', 'DASHBOARD_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/protected/users
 * @desc Create a new user (Institute Admin, Super Admin)
 * @access User Management Permission
 */
router.post('/users',
  requireTenant,
  requireUserManagement,
  async (req, res) => {
    try {
      const { email, firstName, lastName, role, phone } = req.body;
      
      // Validate input
      if (!email || !firstName || !lastName || !role) {
        return res.status(400).json(
          responseUtils.createErrorResponse('Missing required fields', 'VALIDATION_ERROR')
        );
      }

      // Only allow creating users with roles that are lower or equal to current user
      const roleHierarchy = {
        super_admin: 4,
        institute_admin: 3,
        teacher: 2,
        student: 1
      };

      if (roleHierarchy[role] >= roleHierarchy[req.user.role]) {
        return res.status(403).json(
          responseUtils.createErrorResponse(
            'Cannot create user with equal or higher role',
            'ROLE_HIERARCHY_VIOLATION'
          )
        );
      }

      const tenantService = createTenantService(req);
      
      // Generate temporary password
      const tempPassword = Math.random().toString(36).slice(-8) + 'A1!';
      
      const newUser = await tenantService.createUser({
        email,
        password_hash: await require('../utils/auth').passwordUtils.hashPassword(tempPassword),
        first_name: firstName,
        last_name: lastName,
        phone,
        role
      });

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.first_name,
          lastName: newUser.last_name,
          role: newUser.role
        },
        temporaryPassword: tempPassword // In real app, send via email
      });
    } catch (error) {
      console.error('Create user error:', error.message);
      
      if (error.message.includes('already exists')) {
        return res.status(409).json(
          responseUtils.createErrorResponse('User already exists', 'USER_EXISTS')
        );
      }

      res.status(500).json(
        responseUtils.createErrorResponse('Failed to create user', 'USER_CREATE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/protected/users/:id
 * @desc Get user details
 * @access User Read Permission or Own Profile
 */
router.get('/users/:id',
  requireAnyPermission([PERMISSIONS.USER_READ, PERMISSIONS.PROFILE_VIEW]),
  async (req, res) => {
    try {
      const userId = req.params.id;
      
      // Users can always access their own profile
      if (userId !== req.user.id) {
        // Check if user has permission to read other users
        if (!req.userPermissions.includes(PERMISSIONS.USER_READ)) {
          return res.status(403).json(
            responseUtils.createErrorResponse(
              'Access denied. Can only view own profile.',
              'PROFILE_ACCESS_DENIED'
            )
          );
        }
      }

      const tenantService = createTenantService(req);
      const user = await tenantService.getUserById(userId);

      if (!user) {
        return res.status(404).json(
          responseUtils.createErrorResponse('User not found', 'USER_NOT_FOUND')
        );
      }

      res.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone,
          role: user.role,
          isActive: user.is_active,
          isEmailVerified: user.is_email_verified,
          lastLoginAt: user.last_login_at,
          createdAt: user.created_at
        }
      });
    } catch (error) {
      console.error('Get user error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get user', 'USER_GET_ERROR')
      );
    }
  }
);

/**
 * @route PUT /api/protected/users/:id
 * @desc Update user details
 * @access User Update Permission or Own Profile
 */
router.put('/users/:id',
  requireAnyPermission([PERMISSIONS.USER_UPDATE, PERMISSIONS.PROFILE_UPDATE]),
  async (req, res) => {
    try {
      const userId = req.params.id;
      const { firstName, lastName, phone, isActive } = req.body;
      
      // Users can update their own profile (limited fields)
      if (userId === req.user.id) {
        // Own profile - can only update basic info
        const updates = { first_name: firstName, last_name: lastName, phone };
        
        const tenantService = createTenantService(req);
        const updatedUser = await tenantService.updateUser(userId, updates);

        return res.json({
          success: true,
          message: 'Profile updated successfully',
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            firstName: updatedUser.first_name,
            lastName: updatedUser.last_name,
            phone: updatedUser.phone
          }
        });
      }

      // Updating other user - need USER_UPDATE permission
      if (!req.userPermissions.includes(PERMISSIONS.USER_UPDATE)) {
        return res.status(403).json(
          responseUtils.createErrorResponse(
            'Access denied. Cannot update other users.',
            'USER_UPDATE_DENIED'
          )
        );
      }

      const updates = { first_name: firstName, last_name: lastName, phone, is_active: isActive };
      
      const tenantService = createTenantService(req);
      const updatedUser = await tenantService.updateUser(userId, updates);

      if (!updatedUser) {
        return res.status(404).json(
          responseUtils.createErrorResponse('User not found', 'USER_NOT_FOUND')
        );
      }

      res.json({
        success: true,
        message: 'User updated successfully',
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          phone: updatedUser.phone,
          isActive: updatedUser.is_active
        }
      });
    } catch (error) {
      console.error('Update user error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to update user', 'USER_UPDATE_ERROR')
      );
    }
  }
);

/**
 * @route GET /api/protected/courses
 * @desc Get courses (role-based filtering)
 * @access Course Read Permission
 */
router.get('/courses',
  requireTenant,
  requirePermission(PERMISSIONS.COURSE_READ),
  async (req, res) => {
    try {
      // Mock course data - in real app, this would come from database
      const courses = [
        {
          id: 1,
          title: 'Introduction to Computer Science',
          description: 'Basic concepts of computer science',
          instructor: 'Dr. Smith',
          enrolledStudents: 45,
          status: 'active'
        },
        {
          id: 2,
          title: 'Advanced Mathematics',
          description: 'Calculus and linear algebra',
          instructor: 'Prof. Johnson',
          enrolledStudents: 32,
          status: 'active'
        }
      ];

      // Filter based on user role
      let filteredCourses = courses;
      
      if (req.user.role === 'teacher') {
        // Teachers see only their courses (mock: filter by instructor)
        filteredCourses = courses.filter(course => 
          course.instructor.includes(req.user.first_name)
        );
      } else if (req.user.role === 'student') {
        // Students see only enrolled courses (mock: all for demo)
        filteredCourses = courses.map(course => ({
          ...course,
          enrollmentStatus: 'enrolled'
        }));
      }

      res.json({
        success: true,
        courses: filteredCourses,
        userRole: req.user.role,
        totalCourses: filteredCourses.length
      });
    } catch (error) {
      console.error('Get courses error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to get courses', 'COURSES_ERROR')
      );
    }
  }
);

/**
 * @route POST /api/protected/courses
 * @desc Create a new course
 * @access Course Create Permission
 */
router.post('/courses',
  requireTenant,
  requireCourseManagement,
  async (req, res) => {
    try {
      const { title, description } = req.body;
      
      if (!title || !description) {
        return res.status(400).json(
          responseUtils.createErrorResponse('Title and description are required', 'VALIDATION_ERROR')
        );
      }

      // Mock course creation
      const newCourse = {
        id: Date.now(),
        title,
        description,
        instructor: `${req.user.first_name} ${req.user.last_name}`,
        createdBy: req.user.id,
        enrolledStudents: 0,
        status: 'draft',
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        message: 'Course created successfully',
        course: newCourse
      });
    } catch (error) {
      console.error('Create course error:', error.message);
      res.status(500).json(
        responseUtils.createErrorResponse('Failed to create course', 'COURSE_CREATE_ERROR')
      );
    }
  }
);

module.exports = router;
