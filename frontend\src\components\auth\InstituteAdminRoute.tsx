'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { UserRole } from '@/types/auth'

interface InstituteAdminRouteProps {
  children: React.ReactNode
}

export default function InstituteAdminRoute({ children }: InstituteAdminRouteProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  React.useEffect(() => {
    if (!isLoading && (!user || user.role !== UserRole.INSTITUTE_ADMIN)) {
      router.push('/unauthorized')
    }
  }, [user, isLoading, router])

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show nothing if user is not authorized (will redirect)
  if (!user || user.role !== UserRole.INSTITUTE_ADMIN) {
    return null
  }

  return <>{children}</>
}
